#!/usr/bin/env python3
"""
Complete TikTok Integration Test
Tests the full TikTok task workflow with proper user authentication
"""

import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from actor.models import ActorAccount, ActorTask, ActorScrapedData
from actor.services.actor_service import ActorService
from datetime import datetime, timedelta
from django.utils import timezone

def test_tiktok_complete_integration():
    """
    Complete integration test for TikTok task system
    """
    print("🚀 TikTok Complete Integration Test")
    print("=" * 50)
    print(f"Started at: {datetime.now()}")
    
    # 1. Get the correct user and account
    print("\n1. Setting up test environment...")
    try:
        user = User.objects.get(username='test_tiktok_user')
        account = ActorAccount.objects.get(id=21, user=user)
        print(f"✅ Found user: {user.username} (ID: {user.id})")
        print(f"✅ Found account: @{account.platform_username} (ID: {account.id})")
        print(f"   Platform: {account.platform}")
        print(f"   Active: {account.is_active}")
        print(f"   Last login: {account.last_login}")
        print(f"   Session expires: {account.session_expires_at}")
        
        # Check session validity
        session_valid = account.session_expires_at and account.session_expires_at > timezone.now()
        print(f"   Session valid: {session_valid}")
        
    except User.DoesNotExist:
        print("❌ User 'test_tiktok_user' not found")
        return False
    except ActorAccount.DoesNotExist:
        print("❌ Account ID 21 not found or doesn't belong to user")
        return False
    
    # 2. Initialize ActorService
    print("\n2. Initializing ActorService...")
    try:
        service = ActorService()
        print("✅ ActorService initialized successfully")
        
        # Check available engines
        if hasattr(service, 'engines'):
            print(f"   Available engines: {list(service.engines.keys())}")
        else:
            print("   No engines attribute found")
            
    except Exception as e:
        print(f"❌ Failed to initialize ActorService: {str(e)}")
        return False
    
    # 3. Test task creation
    print("\n3. Testing task creation...")
    try:
        task_result = service.create_task(
            user=user,
            account_id=account.id,
            task_type='CONTENT_SEARCH',
            task_name='Integration Test - TikTok Search',
            keywords='indonesia',
            max_items=5
        )
        
        print(f"Task creation result: {task_result}")
        
        if task_result.get('success'):
            task_id = task_result.get('task_id')
            print(f"✅ Task created successfully!")
            print(f"   Task ID: {task_id}")
            print(f"   Task Name: {task_result.get('task', {}).get('name')}")
            print(f"   Task Type: {task_result.get('task', {}).get('type')}")
            print(f"   Platform: {task_result.get('task', {}).get('platform')}")
            print(f"   Status: {task_result.get('task', {}).get('status')}")
            
            # Get the created task from database
            task = ActorTask.objects.get(id=task_id)
            print(f"   Keywords: {task.keywords}")
            print(f"   Max items: {task.max_items}")
            
        else:
            print(f"❌ Task creation failed: {task_result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ Task creation error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    # 4. Test task execution
    print("\n4. Testing task execution...")
    try:
        execution_result = service.execute_task(task_id, user=user)
        print(f"Task execution result: {execution_result}")
        
        if execution_result.get('success'):
            items_scraped = execution_result.get('items_scraped', 0)
            print(f"✅ Task executed successfully!")
            print(f"   Items scraped: {items_scraped}")
            
            # Check updated task status
            task.refresh_from_db()
            print(f"   Final status: {task.status}")
            print(f"   Progress: {task.progress_percentage}%")
            print(f"   Started at: {task.started_at}")
            print(f"   Completed at: {task.completed_at}")
            
            if task.error_message:
                print(f"   Error message: {task.error_message}")
                
        else:
            print(f"❌ Task execution failed: {execution_result.get('error')}")
            
            # Check task status even if execution failed
            task.refresh_from_db()
            print(f"   Task status: {task.status}")
            if task.error_message:
                print(f"   Error message: {task.error_message}")
                
    except Exception as e:
        print(f"❌ Task execution error: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # 5. Verify scraped data
    print("\n5. Verifying scraped data...")
    try:
        # Get data for this specific task
        task_data = ActorScrapedData.objects.filter(task_id=task_id)
        print(f"   Data items for this task: {task_data.count()}")
        
        for i, data in enumerate(task_data[:3], 1):  # Show first 3 items
            print(f"\n   Item {i}:")
            print(f"     ID: {data.id}")
            print(f"     Platform Content ID: {data.platform_content_id}")
            print(f"     Data Type: {data.data_type}")
            print(f"     Platform: {data.platform}")
            print(f"     Account: {data.account_username}")
            print(f"     Quality Score: {data.quality_score}")
            print(f"     Complete: {data.is_complete}")
            print(f"     Scraped At: {data.scraped_at}")
            
            # Show content preview
            if data.content:
                content_keys = list(data.content.keys()) if isinstance(data.content, dict) else []
                print(f"     Content Keys: {content_keys[:5]}...")  # First 5 keys
                
        # Get all data for this account
        account_data = ActorScrapedData.objects.filter(actor_account=account)
        print(f"\n   Total data items for account @{account.platform_username}: {account_data.count()}")
        
        # Data by type
        data_types = account_data.values_list('data_type', flat=True).distinct()
        print(f"   Data types: {list(data_types)}")
        
        for data_type in data_types:
            count = account_data.filter(data_type=data_type).count()
            print(f"     {data_type}: {count} items")
            
    except Exception as e:
        print(f"❌ Data verification error: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # 6. Test system health
    print("\n6. System health check...")
    try:
        # Check recent tasks
        recent_tasks = ActorTask.objects.filter(
            actor_account__platform='tiktok'
        ).order_by('-created_at')[:5]
        
        print(f"   Recent TikTok tasks: {recent_tasks.count()}")
        
        task_statuses = {}
        for task in recent_tasks:
            status = task.status
            task_statuses[status] = task_statuses.get(status, 0) + 1
            
        print(f"   Task status distribution: {task_statuses}")
        
        # Check data quality
        total_data = ActorScrapedData.objects.filter(actor_account__platform='tiktok').count()
        complete_data = ActorScrapedData.objects.filter(
            actor_account__platform='tiktok',
            is_complete=True
        ).count()
        
        completion_rate = (complete_data / total_data * 100) if total_data > 0 else 0
        print(f"   Data completion rate: {completion_rate:.1f}% ({complete_data}/{total_data})")
        
        # Check account health
        active_accounts = ActorAccount.objects.filter(
            platform='tiktok',
            is_active=True
        ).count()
        
        valid_sessions = ActorAccount.objects.filter(
            platform='tiktok',
            is_active=True,
            session_expires_at__gt=timezone.now()
        ).count()
        
        print(f"   Active TikTok accounts: {active_accounts}")
        print(f"   Accounts with valid sessions: {valid_sessions}")
        
        # Overall system health score
        health_score = 0
        if task_statuses.get('COMPLETED', 0) > 0:
            health_score += 30
        if completion_rate > 50:
            health_score += 30
        if valid_sessions > 0:
            health_score += 40
            
        print(f"\n   🏥 System Health Score: {health_score}/100")
        
        if health_score >= 80:
            print("   ✅ System is healthy!")
        elif health_score >= 50:
            print("   ⚠️ System has some issues but is functional")
        else:
            print("   ❌ System needs attention")
            
    except Exception as e:
        print(f"❌ Health check error: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"🏁 Integration test completed at: {datetime.now()}")
    
    # Final summary
    print("\n📊 FINAL SUMMARY:")
    print(f"✅ User Authentication: Working")
    print(f"✅ Account Access: Working")
    print(f"✅ Task Creation: {'Working' if task_result.get('success') else 'Failed'}")
    print(f"✅ Task Execution: {'Working' if execution_result.get('success') else 'Failed'}")
    print(f"✅ Data Storage: {'Working' if task_data.count() > 0 else 'No Data'}")
    
    overall_success = (
        task_result.get('success', False) and 
        execution_result.get('success', False) and 
        task_data.count() > 0
    )
    
    if overall_success:
        print("\n🎉 TikTok integration is FULLY WORKING! 🎉")
        return True
    else:
        print("\n⚠️ TikTok integration has some issues that need attention.")
        return False

if __name__ == "__main__":
    success = test_tiktok_complete_integration()
    sys.exit(0 if success else 1)