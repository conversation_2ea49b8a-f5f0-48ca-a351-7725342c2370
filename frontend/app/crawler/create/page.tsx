'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ScrapeForm } from "@/components/crawler/ScrapeForm";
import { useAuth } from '@/hooks/use-auth';
import { useToast } from "@/components/ui/use-toast";

export default function CreateTaskPage() {
    const { user, loading } = useAuth();
    const router = useRouter();
    const { toast } = useToast();

    useEffect(() => {
        if (!loading && !user) {
            toast({
                title: "Authentication Required",
                description: "Please log in to create scraping tasks.",
                variant: "destructive",
            });
            router.push('/login?redirect=/crawler/create');
        }
    }, [loading, user, router, toast]);

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
        );
    }

    if (!user) {
        return null; // Will redirect in useEffect
    }

    return (
        <div className="p-8">
            <h1 className="text-2xl font-bold mb-6">Create Scraping Task</h1>
            <ScrapeForm />
        </div>
    );
}