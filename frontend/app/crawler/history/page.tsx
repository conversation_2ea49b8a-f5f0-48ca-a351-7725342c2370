'use client';

import { useEffect, useState } from "react";
import { TaskList } from "@/components/crawler/TaskList";
import { getTaskHistory } from "@/lib/api/crawler";
import { TikTokTask } from "@/lib/types/crawler";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card } from "@/components/ui/card";

export default function TaskHistoryPage() {
    const [tasks, setTasks] = useState<TikTokTask[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [stats, setStats] = useState({
        total: 0,
        completed: 0,
        failed: 0,
    });

    useEffect(() => {
        const fetchHistory = async () => {
            try {
                setLoading(true);
                setError(null);
                const historyData = await getTaskHistory();
                setTasks(historyData.tasks);
                setStats({
                    total: historyData.tasks.length,
                    completed: historyData.tasks.filter(t => t.status === 'completed').length,
                    failed: historyData.tasks.filter(t => t.status === 'failed').length,
                });
            } catch (err) {
                setError('Failed to fetch task history');
                console.error('Error fetching task history:', err);
            } finally {
                setLoading(false);
            }
        };

        fetchHistory();
    }, []);

    if (loading) {
        return (
            <div className="p-8 space-y-4">
                <Skeleton className="h-8 w-48" />
                <Skeleton className="h-24 w-full" />
                <Skeleton className="h-[400px] w-full" />
            </div>
        );
    }

    if (error) {
        return (
            <div className="p-8">
                <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            </div>
        );
    }

    return (
        <div className="p-8 space-y-6">
            <h1 className="text-2xl font-bold">Task History</h1>
            
            <div className="grid grid-cols-3 gap-4">
                <Card className="p-4">
                    <p className="text-sm text-gray-500">Total Tasks</p>
                    <p className="text-2xl font-bold">{stats.total}</p>
                </Card>
                <Card className="p-4">
                    <p className="text-sm text-gray-500">Completed</p>
                    <p className="text-2xl font-bold text-green-600">{stats.completed}</p>
                </Card>
                <Card className="p-4">
                    <p className="text-sm text-gray-500">Failed</p>
                    <p className="text-2xl font-bold text-red-600">{stats.failed}</p>
                </Card>
            </div>

            <TaskList tasks={tasks} />
        </div>
    );
}