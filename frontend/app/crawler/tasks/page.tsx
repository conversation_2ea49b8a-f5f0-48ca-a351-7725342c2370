'use client';

import { useEffect, useState } from "react";
import { TaskList } from "@/components/crawler/TaskList";
import { getTikTokTasks } from "@/lib/api/crawler";
import { TikTokTask } from "@/lib/types/crawler";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function TaskListPage() {
    const [tasks, setTasks] = useState<TikTokTask[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchTasks = async () => {
            try {
                setLoading(true);
                setError(null);
                const tasksData = await getTikTokTasks();
                setTasks(tasksData);
            } catch (err) {
                setError('Failed to fetch tasks');
                console.error('Error fetching tasks:', err);
            } finally {
                setLoading(false);
            }
        };

        fetchTasks();

        // Refresh tasks every 30 seconds
        const interval = setInterval(fetchTasks, 30000);
        return () => clearInterval(interval);
    }, []);

    if (loading) {
        return (
            <div className="p-8 space-y-4">
                <Skeleton className="h-8 w-48" />
                <Skeleton className="h-[400px] w-full" />
            </div>
        );
    }

    if (error) {
        return (
            <div className="p-8">
                <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            </div>
        );
    }

    return (
        <div className="p-8">
            <h1 className="text-2xl font-bold mb-6">Active Tasks</h1>
            <TaskList tasks={tasks} />
        </div>
    );
}