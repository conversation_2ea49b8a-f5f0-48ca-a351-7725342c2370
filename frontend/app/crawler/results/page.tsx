'use client';

import { useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { ScrapedDataViewer } from "@/components/crawler/ScrapedDataViewer";
import { TaskList } from "@/components/crawler/TaskList";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { RefreshCw, Database } from "lucide-react";
import { getTikTokTasks } from "@/lib/api/crawler";
import type { TikTokTask } from "@/lib/types/crawler";

function ScrapedDataResultsContent() {
    const searchParams = useSearchParams();
    const taskParam = searchParams.get('task');
    
    const [tasks, setTasks] = useState<TikTokTask[]>([]);
    const [selectedTaskId, setSelectedTaskId] = useState<number | null>(
        taskParam ? parseInt(taskParam) : null
    );

    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchTasks = async () => {
        try {
            setLoading(true);
            setError(null);
            const tasksData = await getTikTokTasks();
            setTasks(tasksData);
            
            // Auto-select the first completed task if none selected
            if (!selectedTaskId && tasksData.length > 0) {
                const completedTask = tasksData.find(task => task.status === 'COMPLETED');
                if (completedTask) {
                    setSelectedTaskId(completedTask.id);
                } else {
                    // If no completed task, select the first task
                    setSelectedTaskId(tasksData[0].id);
                }
            }
        } catch (err) {
            if (err instanceof Error && err.message.includes('401')) {
                setError('Authentication required. Please log in to view tasks and scraped data.');
            } else {
                setError(err instanceof Error ? err.message : 'Failed to fetch tasks');
            }
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchTasks();
    }, []);


    const selectedTask = tasks.find(task => task.id === selectedTaskId);

    if (loading) {
        return (
            <div className="container mx-auto p-6 space-y-6">
                <div className="flex items-center justify-between">
                    <Skeleton className="h-8 w-64" />
                    <Skeleton className="h-10 w-32" />
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="lg:col-span-1">
                        <Skeleton className="h-96" />
                    </div>
                    <div className="lg:col-span-2">
                        <Skeleton className="h-96" />
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto p-6 space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Scraped Data Results</h1>
                    <p className="text-muted-foreground">
                        View and analyze your TikTok scraped content
                    </p>
                </div>
                <Button onClick={fetchTasks} variant="outline" size="sm">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh
                </Button>
            </div>

            {error && (
                <Alert variant="destructive">
                    <AlertDescription>
                        {error}
                        {error.includes('Authentication required') && (
                            <div className="mt-2">
                                <Button 
                                    variant="outline" 
                                    size="sm" 
                                    onClick={() => window.location.href = '/login'}
                                >
                                    Go to Login
                                </Button>
                            </div>
                        )}
                    </AlertDescription>
                </Alert>
            )}

            {selectedTask && (
                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Database className="h-5 w-5" />
                            Task: {selectedTask.job_name || `Task #${selectedTask.id}`}
                        </CardTitle>
                        <div className="text-sm text-muted-foreground flex items-center gap-4">
                            <span>Status: <Badge variant={selectedTask.status === 'COMPLETED' ? 'default' : selectedTask.status === 'RUNNING' ? 'secondary' : selectedTask.status === 'FAILED' ? 'destructive' : 'outline'}>{selectedTask.status}</Badge></span>
                            <span>Type: {selectedTask.task_type}</span>
                            <span>Target: {selectedTask.identifier}</span>
                        </div>
                    </CardHeader>
                </Card>
            )}

            {/* Single Task Data Viewer */}
            <div className="w-full">
                <ScrapedDataViewer 
                    taskId={selectedTaskId || undefined}
                    showSummary={false}
                />
            </div>
        </div>
    );
}

export default function ScrapedDataResultsPage() {
    return (
        <Suspense fallback={
            <div className="container mx-auto p-6">
                <div className="space-y-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold">Scraped Data Results</h1>
                            <p className="text-gray-600 mt-2">View and analyze your scraped data</p>
                        </div>
                    </div>
                    <div className="grid gap-6">
                        <Card>
                            <CardContent className="p-6">
                                <div className="space-y-4">
                                    <Skeleton className="h-4 w-1/4" />
                                    <Skeleton className="h-4 w-1/2" />
                                    <Skeleton className="h-32 w-full" />
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        }>
            <ScrapedDataResultsContent />
        </Suspense>
    );
}