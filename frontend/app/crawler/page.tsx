'use client';

import { useEffect, useState } from "react";
import { HealthStatus } from "@/components/crawler/HealthStatus";
import { TaskList } from "@/components/crawler/TaskList";
import { ScrapeForm } from "@/components/crawler/ScrapeForm";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { getHealthStatus, getTikTokTasks, getTaskStatistics } from "@/lib/api/crawler";
import type { HealthStatus as HealthStatusType, TikTokTask, TaskMetrics } from "@/lib/types/crawler";

export default function CrawlerDashboard() {
    const [health, setHealth] = useState<HealthStatusType | null>(null);
    const [tasks, setTasks] = useState<TikTokTask[]>([]);
    const [metrics, setMetrics] = useState<TaskMetrics | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(true);

    const fetchData = async () => {
        try {
            setLoading(true);
            setError(null);

            const [healthData, tasksData, metricsData] = await Promise.all([
                getHealthStatus(),
                getTikTokTasks(),
                getTaskStatistics()
            ]);

            setHealth(healthData);
            setTasks(tasksData);
            setMetrics(metricsData);
        } catch (err) {
            setError('Failed to fetch crawler data');
            console.error('Error fetching crawler data:', err);
        } finally {
            setLoading(false);
        }
    };

    const refreshTasks = async () => {
        try {
            const [tasksData, metricsData] = await Promise.all([
                getTikTokTasks(),
                getTaskStatistics()
            ]);
            setTasks(tasksData);
            setMetrics(metricsData);
        } catch (err) {
            console.error('Error refreshing tasks:', err);
        }
    };

    useEffect(() => {
        fetchData();

        // Refresh data every 30 seconds
        const interval = setInterval(fetchData, 30000);
        return () => clearInterval(interval);
    }, []);

    if (loading) {
        return (
            <div className="space-y-4 p-8">
                <Skeleton className="h-[200px] w-full" />
                <Skeleton className="h-[400px] w-full" />
            </div>
        );
    }

    if (error) {
        return (
            <Alert variant="destructive" className="m-8">
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        );
    }

    return (
        <div className="p-8 space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
                {health && <HealthStatus health={health} />}
                
                {metrics && (
                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-4">Task Statistics</h3>
                        <div className="grid grid-cols-2 gap-4">
                            <div className="text-center">
                                <p className="text-sm text-gray-500">Success Rate</p>
                                <p className="text-xl font-semibold">
                                    {(metrics.success_rate * 100).toFixed(1)}%
                                </p>
                            </div>
                            <div className="text-center">
                                <p className="text-sm text-gray-500">Average Duration</p>
                                <p className="text-xl font-semibold">
                                    {metrics.average_duration.toFixed(2)}s
                                </p>
                            </div>
                            <div className="text-center">
                                <p className="text-sm text-gray-500">Total Tasks</p>
                                <p className="text-xl font-semibold">{metrics.total_tasks}</p>
                            </div>
                            <div className="text-center">
                                <p className="text-sm text-gray-500">Failed Tasks</p>
                                <p className="text-xl font-semibold">{metrics.failed_tasks}</p>
                            </div>
                        </div>
                    </Card>
                )}
            </div>

            <ScrapeForm />

            <div className="bg-white rounded-lg shadow">
                <div className="p-6">
                    <h2 className="text-xl font-semibold mb-4">Recent Tasks</h2>
                    <TaskList tasks={tasks} onTasksChange={refreshTasks} />
                </div>
            </div>
        </div>
    );
}