'use client';

import { useEffect, useState } from "react";
import { EnhancedAccountList } from "@/components/actor/EnhancedAccountList";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Users, UserCheck, UserX, Clock, Shield, Activity } from "lucide-react";
import { getActorAccounts, type ActorAccount } from "@/lib/api/actor-system";
import { useAuthGuard } from "@/hooks/useAuthGuard";

export default function AccountsPage() {
    const { user, isLoading: authLoading } = useAuthGuard();
    const [accounts, setAccounts] = useState<ActorAccount[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(true);

    const fetchAccounts = async () => {
        try {
            setLoading(true);
            setError(null);
            const data = await getActorAccounts();
            setAccounts(data);
        } catch (err: any) {
            setError(err.response?.data?.error || 'Failed to fetch accounts');
            console.error('Error fetching accounts:', err);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchAccounts();
    }, []);

    const getAccountStats = () => {
        const total = accounts.length;
        const active = accounts.filter(acc => acc.is_active && acc.session_valid).length;
        const inactive = accounts.filter(acc => !acc.is_active).length;
        const sessionExpired = accounts.filter(acc => acc.is_active && !acc.session_valid).length;
        const platforms = new Set(accounts.map(acc => acc.platform)).size;
        const blocked = accounts.filter(acc => acc.is_blocked).length;

        return { total, active, inactive, sessionExpired, platforms, blocked };
    };

    const stats = getAccountStats();

    // Show loading while authenticating or fetching data
    if (authLoading || loading) {
        return (
            <div className="space-y-4 p-8">
                <Skeleton className="h-[100px] w-full" />
                <Skeleton className="h-[400px] w-full" />
            </div>
        );
    }

    // If not authenticated, the useAuthGuard will redirect to login
    if (!user) {
        return null;
    }

    if (error) {
        return (
            <Alert variant="destructive" className="m-8">
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        );
    }

    return (
        <div className="p-8 space-y-6">
            <div>
                <h1 className="text-2xl font-bold mb-2">My Accounts</h1>
                <p className="text-gray-600">Manage your social media accounts across platforms</p>
            </div>
            
            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Users className="h-8 w-8 text-blue-500" />
                        <div>
                            <p className="text-sm text-gray-500">Total Accounts</p>
                            <p className="text-2xl font-bold">{stats.total}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <UserCheck className="h-8 w-8 text-green-500" />
                        <div>
                            <p className="text-sm text-gray-500">Active</p>
                            <p className="text-2xl font-bold">{stats.active}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Clock className="h-8 w-8 text-yellow-500" />
                        <div>
                            <p className="text-sm text-gray-500">Session Expired</p>
                            <p className="text-2xl font-bold">{stats.sessionExpired}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <UserX className="h-8 w-8 text-red-500" />
                        <div>
                            <p className="text-sm text-gray-500">Inactive</p>
                            <p className="text-2xl font-bold">{stats.inactive}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Badge className="h-8 w-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">
                            ✓
                        </Badge>
                        <div>
                            <p className="text-sm text-gray-500">Platforms</p>
                            <p className="text-2xl font-bold">{stats.platforms}</p>
                        </div>
                    </div>
                </Card>
            </div>
            
            {/* Accounts List */}
            <EnhancedAccountList accounts={accounts} onAccountsChange={fetchAccounts} />
        </div>
    );
}