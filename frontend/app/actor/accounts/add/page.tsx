'use client';

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Shield, Eye, EyeOff, CheckCircle, AlertCircle } from "lucide-react";
import {
    createActorAccount,
    getAvailablePlatforms,
    getPlatformIcon,
    getPlatformColor,
    type Platform
} from "@/lib/api/actor-system";

interface ActorAccountFormData {
    platform: string;
    username: string;
    password: string;
    email: string;
}

export default function AddAccountPage() {
    const router = useRouter();
    const [loading, setLoading] = useState(false);
    const [platforms, setPlatforms] = useState<Platform[]>([]);
    const [showPassword, setShowPassword] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);

    const [formData, setFormData] = useState<ActorAccountFormData>({
        platform: '',
        username: '',
        password: '',
        email: ''
    });

    useEffect(() => {
        loadPlatforms();
    }, []);

    const loadPlatforms = async () => {
        try {
            const availablePlatforms = await getAvailablePlatforms();
            setPlatforms(availablePlatforms);

            // Default to TikTok if available
            if (availablePlatforms.length > 0) {
                const tiktok = availablePlatforms.find(p => p.value === 'tiktok');
                if (tiktok) {
                    setFormData(prev => ({ ...prev, platform: tiktok.value }));
                } else {
                    setFormData(prev => ({ ...prev, platform: availablePlatforms[0].value }));
                }
            }
        } catch (err: any) {
            console.error('Error loading platforms:', err);
            setError('Failed to load available platforms');
        }
    };

    const updateFormData = (updates: Partial<ActorAccountFormData>) => {
        setFormData(prev => ({ ...prev, ...updates }));
        setError(null);
        setSuccess(null);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!formData.platform || !formData.username || !formData.password) {
            setError('Platform, username, and password are required');
            return;
        }

        setLoading(true);
        setError(null);
        setSuccess(null);

        try {
            const result = await createActorAccount({
                platform: formData.platform,
                username: formData.username,
                password: formData.password,
                email: formData.email || undefined
            });

            if (result.success) {
                setSuccess(`Successfully created ${formData.platform} account!`);

                // Redirect after a short delay to show success message
                setTimeout(() => {
                    router.push('/actor/accounts');
                }, 2000);
            } else {
                setError(result.error || 'Failed to create account');
            }
        } catch (error: any) {
            console.error('Error creating account:', error);
            const errorMessage = error.response?.data?.error || error.message || 'Failed to create account. Please try again.';
            setError(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    const handleCancel = () => {
        router.push('/actor/accounts');
    };

    const selectedPlatform = platforms.find(p => p.value === formData.platform);

    return (
        <div className="container mx-auto py-6">
            <div className="max-w-2xl mx-auto">
                <div className="mb-6">
                    <Button
                        variant="ghost"
                        onClick={handleCancel}
                        className="mb-4"
                    >
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back to My Accounts
                    </Button>

                    <h1 className="text-3xl font-bold">Add Social Media Account</h1>
                    <p className="text-gray-600 mt-2">
                        Connect a new social media account to the Actor system for automation and content management.
                    </p>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Shield className="h-5 w-5" />
                            Account Details
                        </CardTitle>
                        <CardDescription>
                            Enter the details for your social media account. All fields marked with * are required.
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {error && (
                            <Alert variant="destructive">
                                <AlertCircle className="h-4 w-4" />
                                <AlertDescription>{error}</AlertDescription>
                            </Alert>
                        )}

                        {success && (
                            <Alert>
                                <CheckCircle className="h-4 w-4" />
                                <AlertDescription>{success}</AlertDescription>
                            </Alert>
                        )}

                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Platform Selection */}
                            <div className="space-y-2">
                                <Label htmlFor="platform">Platform *</Label>
                                <Select
                                    value={formData.platform}
                                    onValueChange={(value) => updateFormData({ platform: value })}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select a platform">
                                            {selectedPlatform && (
                                                <div className="flex items-center gap-2">
                                                    <span>{getPlatformIcon(selectedPlatform.value)}</span>
                                                    <span>{selectedPlatform.label}</span>
                                                </div>
                                            )}
                                        </SelectValue>
                                    </SelectTrigger>
                                    <SelectContent>
                                        {platforms.map((platform) => (
                                            <SelectItem key={platform.value} value={platform.value}>
                                                <div className="flex items-center gap-2">
                                                    <span>{getPlatformIcon(platform.value)}</span>
                                                    <span>{platform.label}</span>
                                                </div>
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <p className="text-xs text-gray-500">
                                    Choose the social media platform for this account.
                                </p>
                            </div>

                            {/* Username */}
                            <div className="space-y-2">
                                <Label htmlFor="username">Username *</Label>
                                <Input
                                    id="username"
                                    value={formData.username}
                                    onChange={(e) => updateFormData({ username: e.target.value })}
                                    placeholder={`Your ${selectedPlatform?.label || 'platform'} username`}
                                    autoComplete="off"
                                    required
                                    disabled={loading}
                                />
                                <p className="text-xs text-gray-500">
                                    Enter your username on {selectedPlatform?.label || 'the platform'} (without @).
                                </p>
                            </div>

                            {/* Password */}
                            <div className="space-y-2">
                                <Label htmlFor="password">Password *</Label>
                                <div className="relative">
                                    <Input
                                        id="password"
                                        type={showPassword ? 'text' : 'password'}
                                        value={formData.password}
                                        onChange={(e) => updateFormData({ password: e.target.value })}
                                        placeholder="Enter your password"
                                        autoComplete="new-password"
                                        required
                                        disabled={loading}
                                    />
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                        onClick={() => setShowPassword(!showPassword)}
                                        disabled={loading}
                                    >
                                        {showPassword ? (
                                            <EyeOff className="h-4 w-4" />
                                        ) : (
                                            <Eye className="h-4 w-4" />
                                        )}
                                    </Button>
                                </div>
                                <p className="text-xs text-gray-500">
                                    Your account password. This will be encrypted and stored securely.
                                </p>
                            </div>

                            {/* Email (Optional) */}
                            <div className="space-y-2">
                                <Label htmlFor="email">Email (Optional)</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    value={formData.email}
                                    onChange={(e) => updateFormData({ email: e.target.value })}
                                    placeholder="<EMAIL>"
                                    autoComplete="email"
                                    disabled={loading}
                                />
                                <p className="text-xs text-gray-500">
                                    Optional email associated with this account for recovery purposes.
                                </p>
                            </div>

                            {/* Platform Badge */}
                            {selectedPlatform && (
                                <div className="flex justify-center">
                                    <Badge className={getPlatformColor(selectedPlatform.value)}>
                                        {getPlatformIcon(selectedPlatform.value)} {selectedPlatform.label} Account
                                    </Badge>
                                </div>
                            )}

                            <div className="flex justify-end gap-3 pt-6">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleCancel}
                                    disabled={loading}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="submit"
                                    disabled={loading || !formData.platform || !formData.username || !formData.password}
                                    className="min-w-[140px]"
                                >
                                    {loading ? 'Creating...' : `Create ${selectedPlatform?.label || 'Account'}`}
                                </Button>
                            </div>
                        </form>

                        {/* Info */}
                        <div className="text-sm text-muted-foreground text-center border-t pt-4">
                            <p>
                                Your credentials are encrypted and stored securely.
                                Sessions persist to avoid re-authentication.
                            </p>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}