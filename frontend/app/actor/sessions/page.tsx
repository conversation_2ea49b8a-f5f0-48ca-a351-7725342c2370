'use client';

import { useEffect, useState } from "react";
import { EnhancedSessionManager } from "@/components/actor/EnhancedSessionManager";
import { SessionMonitor } from "@/components/actor/SessionMonitor";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Shield, ShieldCheck, ShieldAlert, ShieldX, Activity, Clock, TrendingUp, Filter, RefreshCw } from "lucide-react";
import { getActorAccounts, type ActorAccount } from "@/lib/api/actor-system";
import type { TikTokSession } from "@/lib/types/actor";

type HealthFilter = 'ALL' | 'HEALTHY' | 'UNHEALTHY';

export default function SessionsPage() {
    // Legacy system state
    const [sessions, setSessions] = useState<TikTokSession[]>([]);
    const [filteredSessions, setFilteredSessions] = useState<TikTokSession[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(true);

    // New Actor system state
    const [accounts, setAccounts] = useState<ActorAccount[]>([]);
    const [showNewSystem, setShowNewSystem] = useState(true);
    
    // Filters
    const [healthFilter, setHealthFilter] = useState<HealthFilter>('ALL');
    const [searchQuery, setSearchQuery] = useState('');
    const [minDetectionScore, setMinDetectionScore] = useState('');
    const [maxDetectionScore, setMaxDetectionScore] = useState('');

    const fetchSessions = async () => {
        try {
            setLoading(true);
            setError(null);
            const data = await getSessions();
            setSessions(data);
        } catch (err) {
            setError('Failed to fetch sessions');
            console.error('Error fetching sessions:', err);
        } finally {
            setLoading(false);
        }
    };

    const loadActorData = async () => {
        try {
            setLoading(true);
            setError(null);

            const accountsData = await getActorAccounts();
            setAccounts(accountsData);

        } catch (err: any) {
            setError(err.response?.data?.error || 'Failed to fetch Actor data');
            console.error('Error fetching Actor data:', err);
        } finally {
            setLoading(false);
        }
    };

    // Apply filters
    useEffect(() => {
        let filtered = sessions;
        
        // Health filter
        if (healthFilter !== 'ALL') {
            filtered = filtered.filter(session => 
                healthFilter === 'HEALTHY' ? session.is_healthy : !session.is_healthy
            );
        }
        
        // Search filter
        if (searchQuery) {
            filtered = filtered.filter(session => 
                session.session_id.toLowerCase().includes(searchQuery.toLowerCase()) ||
                (session.user_agent && session.user_agent.toLowerCase().includes(searchQuery.toLowerCase())) ||
                (session.proxy_used && session.proxy_used.toLowerCase().includes(searchQuery.toLowerCase()))
            );
        }
        
        // Detection score filters
        if (minDetectionScore) {
            const min = parseFloat(minDetectionScore);
            filtered = filtered.filter(session => session.detection_score >= min);
        }
        
        if (maxDetectionScore) {
            const max = parseFloat(maxDetectionScore);
            filtered = filtered.filter(session => session.detection_score <= max);
        }
        
        setFilteredSessions(filtered);
    }, [sessions, healthFilter, searchQuery, minDetectionScore, maxDetectionScore]);

    useEffect(() => {
        if (showNewSystem) {
            loadActorData();
        } else {
            fetchSessions();
        }
    }, [showNewSystem]);

    const getSessionStats = () => {
        const total = sessions.length;
        const healthy = sessions.filter(s => s.is_healthy).length;
        const unhealthy = total - healthy;
        const avgDetectionScore = sessions.length > 0 
            ? sessions.reduce((sum, s) => sum + s.detection_score, 0) / sessions.length 
            : 0;
        const avgSuccessRate = sessions.length > 0
            ? sessions.reduce((sum, s) => {
                const total = s.successful_requests + s.failed_requests;
                return sum + (total > 0 ? (s.successful_requests / total) * 100 : 0);
            }, 0) / sessions.length
            : 0;
        const totalRequests = sessions.reduce((sum, s) => sum + s.requests_made, 0);
        const activeSessions = sessions.filter(s => {
            if (!s.last_activity) return false;
            const lastActivity = new Date(s.last_activity);
            const now = new Date();
            const diffMinutes = (now.getTime() - lastActivity.getTime()) / (1000 * 60);
            return diffMinutes < 30; // Active if last activity within 30 minutes
        }).length;
        
        return {
            total,
            healthy,
            unhealthy,
            avgDetectionScore,
            avgSuccessRate,
            totalRequests,
            activeSessions
        };
    };

    const clearFilters = () => {
        setHealthFilter('ALL');
        setSearchQuery('');
        setMinDetectionScore('');
        setMaxDetectionScore('');
    };

    const stats = getSessionStats();

    if (loading) {
        return (
            <div className="space-y-4 p-8">
                <Skeleton className="h-[100px] w-full" />
                <Skeleton className="h-[400px] w-full" />
            </div>
        );
    }

    if (error) {
        return (
            <Alert variant="destructive" className="m-8">
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        );
    }

    return (
        <div className="p-8 space-y-6">
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-bold mb-2">
                        {showNewSystem ? 'Session Monitor' : 'Legacy Sessions'}
                    </h1>
                    <p className="text-gray-600">
                        {showNewSystem
                            ? 'Monitor and manage account session health across platforms'
                            : 'Legacy TikTok-specific session monitoring'
                        }
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Button
                        variant={showNewSystem ? "default" : "outline"}
                        size="sm"
                        onClick={() => setShowNewSystem(true)}
                    >
                        <Activity className="h-4 w-4 mr-2" />
                        Actor System
                    </Button>
                    <Button
                        variant={!showNewSystem ? "default" : "outline"}
                        size="sm"
                        onClick={() => setShowNewSystem(false)}
                    >
                        Legacy TikTok
                    </Button>
                    <Button
                        onClick={showNewSystem ? loadActorData : fetchSessions}
                        disabled={loading}
                    >
                        <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                        Refresh
                    </Button>
                </div>
            </div>
            
            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Shield className="h-8 w-8 text-blue-500" />
                        <div>
                            <p className="text-sm text-gray-500">Total Sessions</p>
                            <p className="text-2xl font-bold">{stats.total}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <ShieldCheck className="h-8 w-8 text-green-500" />
                        <div>
                            <p className="text-sm text-gray-500">Healthy Sessions</p>
                            <p className="text-2xl font-bold text-green-600">{stats.healthy}</p>
                            <p className="text-xs text-gray-500">
                                {stats.total > 0 ? Math.round((stats.healthy / stats.total) * 100) : 0}% of total
                            </p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <ShieldAlert className="h-8 w-8 text-red-500" />
                        <div>
                            <p className="text-sm text-gray-500">Unhealthy Sessions</p>
                            <p className="text-2xl font-bold text-red-600">{stats.unhealthy}</p>
                            <p className="text-xs text-gray-500">
                                {stats.total > 0 ? Math.round((stats.unhealthy / stats.total) * 100) : 0}% of total
                            </p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Activity className="h-8 w-8 text-orange-500" />
                        <div>
                            <p className="text-sm text-gray-500">Active Sessions</p>
                            <p className="text-2xl font-bold text-orange-600">{stats.activeSessions}</p>
                            <p className="text-xs text-gray-500">Last 30 minutes</p>
                        </div>
                    </div>
                </Card>
            </div>
            
            {/* Performance Metrics */}
            <div className="grid gap-4 md:grid-cols-3">
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <TrendingUp className="h-6 w-6 text-blue-500" />
                        <div>
                            <p className="text-sm text-gray-500">Avg Success Rate</p>
                            <p className="text-xl font-bold">{stats.avgSuccessRate.toFixed(1)}%</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <ShieldX className="h-6 w-6 text-yellow-500" />
                        <div>
                            <p className="text-sm text-gray-500">Avg Detection Score</p>
                            <p className="text-xl font-bold">{stats.avgDetectionScore.toFixed(3)}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Clock className="h-6 w-6 text-green-500" />
                        <div>
                            <p className="text-sm text-gray-500">Total Requests</p>
                            <p className="text-xl font-bold">{stats.totalRequests.toLocaleString()}</p>
                        </div>
                    </div>
                </Card>
            </div>
            
            {/* Filters */}
            <Card className="p-4">
                <div className="flex flex-wrap items-center gap-4">
                    <div className="flex items-center gap-2">
                        <Filter className="h-4 w-4 text-gray-500" />
                        <span className="text-sm font-medium">Filters:</span>
                    </div>
                    
                    <Input
                        placeholder="Search sessions..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-48"
                    />
                    
                    <Select value={healthFilter} onValueChange={(value: HealthFilter) => setHealthFilter(value)}>
                        <SelectTrigger className="w-32">
                            <SelectValue placeholder="Health" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="ALL">All Sessions</SelectItem>
                            <SelectItem value="HEALTHY">Healthy</SelectItem>
                            <SelectItem value="UNHEALTHY">Unhealthy</SelectItem>
                        </SelectContent>
                    </Select>
                    
                    <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">Detection Score:</span>
                        <Input
                            placeholder="Min"
                            value={minDetectionScore}
                            onChange={(e) => setMinDetectionScore(e.target.value)}
                            className="w-20"
                            type="number"
                            step="0.001"
                            min="0"
                            max="1"
                        />
                        <span className="text-sm text-gray-500">to</span>
                        <Input
                            placeholder="Max"
                            value={maxDetectionScore}
                            onChange={(e) => setMaxDetectionScore(e.target.value)}
                            className="w-20"
                            type="number"
                            step="0.001"
                            min="0"
                            max="1"
                        />
                    </div>
                    
                    <Button variant="outline" onClick={clearFilters} size="sm">
                        Clear Filters
                    </Button>
                    
                    <div className="ml-auto">
                        <Badge variant="outline">
                            {filteredSessions.length} of {sessions.length} sessions
                        </Badge>
                    </div>
                </div>
            </Card>

            {/* Session Content */}
            {showNewSystem ? (
                <EnhancedSessionManager
                    accounts={accounts}
                    onAccountsChange={loadActorData}
                />
            ) : (
                <>
                    {/* Legacy System Filters */}
                    <Card className="p-4">
                        <div className="flex flex-wrap items-center gap-4">
                            <div className="flex items-center gap-2">
                                <Filter className="h-4 w-4 text-gray-500" />
                                <span className="text-sm font-medium">Legacy Filters:</span>
                            </div>

                            <div className="flex items-center gap-2">
                                <Input
                                    placeholder="Search sessions..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="w-48"
                                />
                            </div>

                            <Select value={healthFilter} onValueChange={setHealthFilter}>
                                <SelectTrigger className="w-32">
                                    <SelectValue placeholder="Health" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="ALL">All Health</SelectItem>
                                    <SelectItem value="HEALTHY">Healthy</SelectItem>
                                    <SelectItem value="UNHEALTHY">Unhealthy</SelectItem>
                                </SelectContent>
                            </Select>

                            <Button variant="outline" onClick={clearFilters} size="sm">
                                Clear Filters
                            </Button>
                        </div>
                    </Card>

                    {/* Legacy Session Monitor */}
                    <SessionMonitor />

                    {filteredSessions.length === 0 && sessions.length > 0 && (
                        <Card className="p-12">
                            <div className="text-center text-gray-500">
                                <Shield className="h-16 w-16 mx-auto mb-4 opacity-50" />
                                <p className="text-lg">No sessions match your filters</p>
                                <p className="text-sm">Try adjusting your search criteria</p>
                            </div>
                        </Card>
                    )}

                    {sessions.length === 0 && (
                        <Card className="p-12">
                            <div className="text-center text-gray-500">
                                <Shield className="h-16 w-16 mx-auto mb-4 opacity-50" />
                                <p className="text-lg">No sessions found</p>
                                <p className="text-sm">Sessions will appear here when tasks are running</p>
                            </div>
                        </Card>
                    )}
                </>
            )}
        </div>
    );
}