'use client';

import { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import {
    Database, Search, Eye, Download, Filter, Activity,
    BarChart3, Target, RefreshCw, Calendar, User, Video, MessageCircle,
    Grid, ChevronUp, ChevronDown, ArrowUpDown, MoreHorizontal, Plus, Edit, Trash2
} from "lucide-react";
import {
    getActorAccounts,
    getActorScrapedData,
    getDataLabelingStats,
    createScrapedData,
    updateScrapedData,
    deleteScrapedData,
    bulkDeleteScrapedData,
    getActorTasks,
    type ActorAccount,
    type CreateScrapedDataRequest,
    type UpdateScrapedDataRequest
} from "@/lib/api/actor-system";

// New Actor System Data Types
type ActorDataType = 'video' | 'profile' | 'comment' | 'search_result' | 'user_content' | 'ALL';
type QualityFilter = 'ALL' | 'HIGH' | 'MEDIUM' | 'LOW';
type PlatformFilter = 'ALL' | 'tiktok' | 'instagram' | 'facebook' | 'twitter' | 'youtube';

interface ActorScrapedDataItem {
    id: number;
    task_id: number;
    data_type: string;
    platform: string;
    content: any;
    quality_score: number;
    scraped_at: string;
    is_complete: boolean;
    platform_content_id: string;
    account_username: string;
    task_info?: {
        id: number;
        name: string;
        type: string;
    };
    account_info?: {
        id: number;
        platform: string;
        username: string;
    };
}

interface DataStats {
    total_items: number;
    platforms_used: number;
    accounts_used: number;
    average_quality_score: number;
    complete_items: number;
    completion_rate: number;
    platform_breakdown: Record<string, number>;
    data_type_breakdown: Record<string, number>;
}

export default function ActorDataPage() {
    // Actor system state
    const [data, setData] = useState<ActorScrapedDataItem[]>([]);
    const [filteredData, setFilteredData] = useState<ActorScrapedDataItem[]>([]);
    const [stats, setStats] = useState<DataStats | null>(null);
    const [accounts, setAccounts] = useState<ActorAccount[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [selectedItem, setSelectedItem] = useState<ActorScrapedDataItem | null>(null);

    // CRUD state
    const [selectedItems, setSelectedItems] = useState<number[]>([]);
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [editingItem, setEditingItem] = useState<ActorScrapedDataItem | null>(null);
    const [tasks, setTasks] = useState<any[]>([]);

    // Form state
    const [createForm, setCreateForm] = useState<CreateScrapedDataRequest>({
        task_id: 0,
        data_type: 'VIDEO',
        content: {},
        platform: 'tiktok',
        account_username: '',
        platform_content_id: '',
        is_complete: true
    });

    const [editForm, setEditForm] = useState<UpdateScrapedDataRequest>({
        data_type: '',
        content: {},
        platform_content_id: '',
        account_username: '',
        is_complete: true
    });
    
    // View mode and pagination
    const [viewMode, setViewMode] = useState<'cards' | 'table'>('cards');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalCount, setTotalCount] = useState(0);
    const [pageSize, setPageSize] = useState(20);

    // Sorting
    const [sortField, setSortField] = useState<string>('scraped_at');
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
    
    // Filters
    const [dataTypeFilter, setDataTypeFilter] = useState<ActorDataType>('ALL');
    const [platformFilter, setPlatformFilter] = useState<PlatformFilter>('ALL');
    const [qualityFilter, setQualityFilter] = useState<QualityFilter>('ALL');
    const [accountFilter, setAccountFilter] = useState('ALL');
    const [searchQuery, setSearchQuery] = useState('');
    const [taskIdFilter, setTaskIdFilter] = useState('');

    const loadData = async (page = 1) => {
        try {
            setLoading(true);
            setError(null);
            
            // Build filters for API call
            const filters: any = {
                page,
                page_size: pageSize,
                ordering: sortDirection === 'desc' ? `-${sortField}` : sortField
            };
            
            if (dataTypeFilter !== 'ALL') {
                filters.data_type = dataTypeFilter;
            }
            
            if (platformFilter !== 'ALL') {
                filters.platform = platformFilter;
            }
            
            if (accountFilter !== 'ALL') {
                filters.account_id = parseInt(accountFilter);
            }
            
            if (taskIdFilter) {
                filters.task_id = parseInt(taskIdFilter);
            }
            
            if (searchQuery) {
                filters.search = searchQuery;
            }
            
            // Load scraped data
            const scrapedDataResponse = await getActorScrapedData(filters);
            const scrapedData = scrapedDataResponse.results || [];
            
            setData(scrapedData);
            setTotalCount(scrapedDataResponse.count || 0);
            
        } catch (err: any) {
            console.error('Error loading data:', err);
            setError(err.message || 'Failed to load data');
            setData([]);
            setTotalCount(0);
        } finally {
            setLoading(false);
        }
    };

    const loadStats = async () => {
        try {
            const statsResponse = await getDataLabelingStats();
            if (statsResponse.success && statsResponse.stats) {
                const transformedStats: DataStats = {
                    total_items: statsResponse.stats.total_items || 0,
                    platforms_used: statsResponse.stats.platforms_used || 0,
                    accounts_used: statsResponse.stats.accounts_used || 0,
                    average_quality_score: statsResponse.stats.average_quality_score || 0,
                    complete_items: statsResponse.stats.complete_items || 0,
                    completion_rate: statsResponse.stats.completion_rate || 0,
                    platform_breakdown: statsResponse.stats.platform_breakdown || {},
                    data_type_breakdown: statsResponse.stats.data_type_breakdown || {}
                };
                setStats(transformedStats);
            }
        } catch (err) {
            console.warn('Failed to load stats:', err);
            setStats({
                total_items: 0,
                platforms_used: 0,
                accounts_used: 0,
                average_quality_score: 0,
                complete_items: 0,
                completion_rate: 0,
                platform_breakdown: {},
                data_type_breakdown: {}
            });
        }
    };

    const loadAccounts = async () => {
        try {
            const accountsData = await getActorAccounts();
            setAccounts(accountsData);
        } catch (err) {
            console.warn('Failed to load accounts:', err);
            setAccounts([]);
        }
    };

    const loadTasks = async () => {
        try {
            const tasksData = await getActorTasks();
            setTasks(tasksData);
        } catch (err) {
            console.warn('Failed to load tasks:', err);
            setTasks([]);
        }
    };

    const loadAllData = async () => {
        await Promise.all([
            loadData(currentPage),
            loadStats(),
            loadAccounts(),
            loadTasks()
        ]);
    };

    // CRUD Operations
    const handleCreateData = async () => {
        try {
            setLoading(true);
            await createScrapedData(createForm);
            toast({
                title: "Success",
                description: "Data created successfully",
            });
            setIsCreateModalOpen(false);
            setCreateForm({
                task_id: 0,
                data_type: 'VIDEO',
                content: {},
                platform: 'tiktok',
                account_username: '',
                platform_content_id: '',
                is_complete: true
            });
            await loadAllData();
        } catch (err: any) {
            toast({
                title: "Error",
                description: err.message || "Failed to create data",
                variant: "destructive",
            });
        } finally {
            setLoading(false);
        }
    };

    const handleEditData = async () => {
        if (!editingItem) return;

        try {
            setLoading(true);
            await updateScrapedData(editingItem.id, editForm);
            toast({
                title: "Success",
                description: "Data updated successfully",
            });
            setIsEditModalOpen(false);
            setEditingItem(null);
            await loadAllData();
        } catch (err: any) {
            toast({
                title: "Error",
                description: err.message || "Failed to update data",
                variant: "destructive",
            });
        } finally {
            setLoading(false);
        }
    };

    const handleDeleteData = async (dataId: number) => {
        if (!confirm('Are you sure you want to delete this data item?')) return;

        try {
            setLoading(true);
            console.log('Deleting single item:', dataId);
            const result = await deleteScrapedData(dataId);
            console.log('Delete result:', result);
            toast({
                title: "Success",
                description: "Data deleted successfully",
            });
            await loadAllData();
        } catch (err: any) {
            console.error('Delete error:', err);
            toast({
                title: "Error",
                description: err.response?.data?.error || err.message || "Failed to delete data",
                variant: "destructive",
            });
        } finally {
            setLoading(false);
        }
    };

    const handleBulkDelete = async () => {
        if (selectedItems.length === 0) {
            console.log('No items selected for bulk delete');
            return;
        }
        if (!confirm(`Are you sure you want to delete ${selectedItems.length} data items?`)) return;

        try {
            setLoading(true);
            console.log('Bulk deleting items:', selectedItems);
            const result = await bulkDeleteScrapedData(selectedItems);
            console.log('Bulk delete result:', result);
            toast({
                title: "Success",
                description: `Successfully deleted ${selectedItems.length} data items`,
            });
            setSelectedItems([]);
            await loadAllData();
        } catch (err: any) {
            console.error('Bulk delete error:', err);
            toast({
                title: "Error",
                description: err.response?.data?.error || err.message || "Failed to delete data",
                variant: "destructive",
            });
        } finally {
            setLoading(false);
        }
    };

    const openEditModal = (item: ActorScrapedDataItem) => {
        setEditingItem(item);
        setEditForm({
            data_type: item.data_type,
            content: item.content,
            platform_content_id: item.platform_content_id || '',
            account_username: item.account_username || '',
            is_complete: item.is_complete
        });
        setIsEditModalOpen(true);
    };

    const toggleItemSelection = (itemId: number) => {
        setSelectedItems(prev =>
            prev.includes(itemId)
                ? prev.filter(id => id !== itemId)
                : [...prev, itemId]
        );
    };

    const toggleAllSelection = () => {
        if (selectedItems.length === filteredData.length) {
            setSelectedItems([]);
        } else {
            setSelectedItems(filteredData.map(item => item.id));
        }
    };

    // Apply client-side filtering
    useEffect(() => {
        let filtered = data;
        
        // Apply quality filter
        if (qualityFilter !== 'ALL') {
            filtered = filtered.filter(item => {
                const score = item.quality_score || 0;
                switch (qualityFilter) {
                    case 'HIGH': return score >= 80;
                    case 'MEDIUM': return score >= 50 && score < 80;
                    case 'LOW': return score < 50;
                    default: return true;
                }
            });
        }
        
        // Apply search filter
        if (searchQuery) {
            const query = searchQuery.toLowerCase();
            filtered = filtered.filter(item =>
                item.account_username?.toLowerCase().includes(query) ||
                item.platform_content_id?.toLowerCase().includes(query) ||
                item.data_type?.toLowerCase().includes(query) ||
                JSON.stringify(item.content).toLowerCase().includes(query)
            );
        }
        
        setFilteredData(filtered);
    }, [data, qualityFilter, searchQuery]);

    // Sorting helper
    const handleSort = (field: string) => {
        if (sortField === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
        setCurrentPage(1); // Reset to first page when sorting
    };

    // Page size change handler
    const handlePageSizeChange = (newPageSize: number) => {
        setPageSize(newPageSize);
        setCurrentPage(1); // Reset to first page when changing page size
    };

    // Get sort icon
    const getSortIcon = (field: string) => {
        if (sortField !== field) return <ArrowUpDown className="w-4 h-4" />;
        return sortDirection === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />;
    };

    useEffect(() => {
        loadAllData();
    }, [currentPage, dataTypeFilter, platformFilter, accountFilter, taskIdFilter, sortField, sortDirection, pageSize]);

    useEffect(() => {
        // Reload data when filters change
        if (currentPage === 1) {
            loadData(1);
        } else {
            setCurrentPage(1);
        }
    }, [dataTypeFilter, platformFilter, accountFilter, taskIdFilter]);

    // Helper functions
    const getQualityBadgeColor = (score: number) => {
        if (score >= 80) return 'bg-green-100 text-green-800';
        if (score >= 50) return 'bg-yellow-100 text-yellow-800';
        return 'bg-red-100 text-red-800';
    };

    const getQualityLabel = (score: number) => {
        if (score >= 80) return 'High';
        if (score >= 50) return 'Medium';
        return 'Low';
    };

    const getDataTypeIcon = (type: string) => {
        switch (type.toLowerCase()) {
            case 'video':
                return <Video className="h-4 w-4" />;
            case 'profile':
                return <User className="h-4 w-4" />;
            case 'comment':
                return <MessageCircle className="h-4 w-4" />;
            default:
                return <Database className="h-4 w-4" />;
        }
    };

    const getDataTypeColor = (type: string) => {
        switch (type.toLowerCase()) {
            case 'video':
                return 'bg-purple-100 text-purple-800';
            case 'profile':
                return 'bg-blue-100 text-blue-800';
            case 'comment':
                return 'bg-green-100 text-green-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const clearFilters = () => {
        setDataTypeFilter('ALL');
        setPlatformFilter('ALL');
        setQualityFilter('ALL');
        setAccountFilter('ALL');
        setSearchQuery('');
        setTaskIdFilter('');
        setCurrentPage(1);
    };

    const exportData = () => {
        const dataStr = JSON.stringify(filteredData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `actor_scraped_data_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
    };

    if (loading && data.length === 0) {
        return (
            <div className="p-8 space-y-6">
                <div className="flex justify-between items-center">
                    <div>
                        <Skeleton className="h-8 w-48 mb-2" />
                        <Skeleton className="h-4 w-96" />
                    </div>
                    <Skeleton className="h-10 w-32" />
                </div>
                
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    {[...Array(4)].map((_, i) => (
                        <Card key={i} className="p-4">
                            <Skeleton className="h-16 w-full" />
                        </Card>
                    ))}
                </div>
                
                <Card className="p-6">
                    <Skeleton className="h-64 w-full" />
                </Card>
            </div>
        );
    }

    return (
        <div className="p-8 space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-bold mb-2">My Data</h1>
                    <p className="text-gray-600">
                        View and manage data collected from social media platforms
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
                        <DialogTrigger asChild>
                            <Button>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Data
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                            <DialogHeader>
                                <DialogTitle>Create New Data Entry</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="task_id">Task</Label>
                                        <Select
                                            value={createForm.task_id.toString()}
                                            onValueChange={(value) => setCreateForm({...createForm, task_id: parseInt(value)})}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select task" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {tasks.map((task) => (
                                                    <SelectItem key={task.id} value={task.id.toString()}>
                                                        {task.name} ({task.platform})
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div>
                                        <Label htmlFor="data_type">Data Type</Label>
                                        <Select
                                            value={createForm.data_type}
                                            onValueChange={(value) => setCreateForm({...createForm, data_type: value})}
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="VIDEO">Video</SelectItem>
                                                <SelectItem value="profile">Profile</SelectItem>
                                                <SelectItem value="comment">Comment</SelectItem>
                                                <SelectItem value="search_result">Search Result</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div>
                                        <Label htmlFor="platform">Platform</Label>
                                        <Select
                                            value={createForm.platform}
                                            onValueChange={(value) => setCreateForm({...createForm, platform: value})}
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="tiktok">TikTok</SelectItem>
                                                <SelectItem value="twitter">Twitter</SelectItem>
                                                <SelectItem value="instagram">Instagram</SelectItem>
                                                <SelectItem value="facebook">Facebook</SelectItem>
                                                <SelectItem value="youtube">YouTube</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div>
                                        <Label htmlFor="account_username">Account Username</Label>
                                        <Input
                                            value={createForm.account_username}
                                            onChange={(e) => setCreateForm({...createForm, account_username: e.target.value})}
                                            placeholder="@username"
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="platform_content_id">Content ID</Label>
                                        <Input
                                            value={createForm.platform_content_id}
                                            onChange={(e) => setCreateForm({...createForm, platform_content_id: e.target.value})}
                                            placeholder="Platform-specific content ID"
                                        />
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="is_complete"
                                            checked={createForm.is_complete}
                                            onCheckedChange={(checked) => setCreateForm({...createForm, is_complete: !!checked})}
                                        />
                                        <Label htmlFor="is_complete">Complete</Label>
                                    </div>
                                </div>
                                <div>
                                    <Label htmlFor="content">Content (JSON)</Label>
                                    <Textarea
                                        value={JSON.stringify(createForm.content, null, 2)}
                                        onChange={(e) => {
                                            try {
                                                const content = JSON.parse(e.target.value);
                                                setCreateForm({...createForm, content});
                                            } catch {
                                                // Invalid JSON, keep the text for user to fix
                                            }
                                        }}
                                        placeholder='{"title": "Example content", "author": "username"}'
                                        className="min-h-32"
                                    />
                                </div>
                                <div className="flex justify-end gap-2">
                                    <Button variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                                        Cancel
                                    </Button>
                                    <Button onClick={handleCreateData} disabled={loading}>
                                        Create Data
                                    </Button>
                                </div>
                            </div>
                        </DialogContent>
                    </Dialog>

                    {selectedItems.length > 0 && (
                        <Button variant="destructive" onClick={handleBulkDelete} disabled={loading}>
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Selected ({selectedItems.length})
                        </Button>
                    )}

                    <Button onClick={() => loadAllData()} disabled={loading}>
                        <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                        Refresh
                    </Button>
                    <Button onClick={exportData} disabled={filteredData.length === 0}>
                        <Download className="h-4 w-4 mr-2" />
                        Export Data
                    </Button>
                </div>
            </div>

            {error && (
                <Alert>
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Database className="h-8 w-8 text-blue-500" />
                        <div>
                            <p className="text-sm text-gray-500">Total Items</p>
                            <p className="text-2xl font-bold">{stats?.total_items || 0}</p>
                        </div>
                    </div>
                </Card>

                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Activity className="h-8 w-8 text-green-500" />
                        <div>
                            <p className="text-sm text-gray-500">Platforms</p>
                            <p className="text-2xl font-bold">{stats?.platforms_used || 0}</p>
                        </div>
                    </div>
                </Card>

                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <User className="h-8 w-8 text-purple-500" />
                        <div>
                            <p className="text-sm text-gray-500">Accounts</p>
                            <p className="text-2xl font-bold">{stats?.accounts_used || 0}</p>
                        </div>
                    </div>
                </Card>

                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Target className="h-8 w-8 text-orange-500" />
                        <div>
                            <p className="text-sm text-gray-500">Avg Quality</p>
                            <p className="text-2xl font-bold">{stats?.average_quality_score?.toFixed(1) || '0.0'}</p>
                        </div>
                    </div>
                </Card>

                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <BarChart3 className="h-8 w-8 text-red-500" />
                        <div>
                            <p className="text-sm text-gray-500">Complete</p>
                            <p className="text-2xl font-bold">{stats?.completion_rate?.toFixed(0) || '0'}%</p>
                        </div>
                    </div>
                </Card>
            </div>

            {/* Filters */}
            <Card className="p-4">
                <div className="flex flex-wrap items-center gap-4">
                    <div className="flex items-center gap-2">
                        <Filter className="h-4 w-4 text-gray-500" />
                        <span className="text-sm font-medium">Filters:</span>
                    </div>

                    <div className="flex items-center gap-2">
                        <Search className="h-4 w-4 text-gray-500" />
                        <Input
                            placeholder="Search data..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="w-48"
                        />
                    </div>

                    <Select value={dataTypeFilter} onValueChange={(value: ActorDataType) => setDataTypeFilter(value)}>
                        <SelectTrigger className="w-32">
                            <SelectValue placeholder="Type" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="ALL">All Types</SelectItem>
                            <SelectItem value="video">Video</SelectItem>
                            <SelectItem value="profile">Profile</SelectItem>
                            <SelectItem value="comment">Comment</SelectItem>
                            <SelectItem value="search_result">Search</SelectItem>
                            <SelectItem value="user_content">User Content</SelectItem>
                        </SelectContent>
                    </Select>

                    <Select value={platformFilter} onValueChange={(value: PlatformFilter) => setPlatformFilter(value)}>
                        <SelectTrigger className="w-32">
                            <SelectValue placeholder="Platform" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="ALL">All Platforms</SelectItem>
                            <SelectItem value="tiktok">TikTok</SelectItem>
                            <SelectItem value="instagram">Instagram</SelectItem>
                            <SelectItem value="facebook">Facebook</SelectItem>
                            <SelectItem value="twitter">Twitter</SelectItem>
                            <SelectItem value="youtube">YouTube</SelectItem>
                        </SelectContent>
                    </Select>

                    <Select value={qualityFilter} onValueChange={(value: QualityFilter) => setQualityFilter(value)}>
                        <SelectTrigger className="w-32">
                            <SelectValue placeholder="Quality" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="ALL">All Quality</SelectItem>
                            <SelectItem value="HIGH">High (80+)</SelectItem>
                            <SelectItem value="MEDIUM">Medium (50-79)</SelectItem>
                            <SelectItem value="LOW">Low (&lt;50)</SelectItem>
                        </SelectContent>
                    </Select>

                    <Select value={accountFilter} onValueChange={setAccountFilter}>
                        <SelectTrigger className="w-40">
                            <SelectValue placeholder="Account" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="ALL">All Accounts</SelectItem>
                            {accounts.map((account) => (
                                <SelectItem key={account.id} value={account.id.toString()}>
                                    @{account.username} ({account.platform})
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>

                    <Input
                        placeholder="Task ID..."
                        value={taskIdFilter}
                        onChange={(e) => setTaskIdFilter(e.target.value)}
                        className="w-24"
                        type="number"
                    />

                    <div className="flex items-center gap-2 border-l pl-4">
                        <span className="text-sm text-gray-500">View:</span>
                        <Button
                            variant={viewMode === 'cards' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setViewMode('cards')}
                        >
                            <Grid className="h-4 w-4" />
                        </Button>
                        <Button
                            variant={viewMode === 'table' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setViewMode('table')}
                        >
                            <Database className="h-4 w-4" />
                        </Button>
                    </div>

                    <div className="flex items-center gap-2 border-l pl-4">
                        <span className="text-sm text-gray-500">Show:</span>
                        <Select value={pageSize.toString()} onValueChange={(value) => handlePageSizeChange(parseInt(value))}>
                            <SelectTrigger className="w-20">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="10">10</SelectItem>
                                <SelectItem value="20">20</SelectItem>
                                <SelectItem value="50">50</SelectItem>
                                <SelectItem value="100">100</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <Button variant="outline" onClick={clearFilters}>
                        Clear Filters
                    </Button>

                    <div className="flex items-center gap-2 ml-auto">
                        <Badge variant="secondary">
                            {filteredData.length} of {totalCount} items
                        </Badge>
                    </div>
                </div>
            </Card>

            {/* Data Display */}
            <Card className="p-6">
                {filteredData.length === 0 ? (
                    <div className="text-center py-12">
                        <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No data found</h3>
                        <p className="text-gray-500">
                            {data.length === 0
                                ? "No scraped data available. Execute some tasks to see data here."
                                : "No data matches your current filters. Try adjusting your search criteria."
                            }
                        </p>
                    </div>
                ) : viewMode === 'table' ? (
                    /* Table View */
                    <div className="overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="w-12">
                                        <Checkbox
                                            checked={selectedItems.length === filteredData.length && filteredData.length > 0}
                                            onCheckedChange={toggleAllSelection}
                                        />
                                    </TableHead>
                                    <TableHead className="w-16">
                                        <Button variant="ghost" size="sm" onClick={() => handleSort('id')}>
                                            ID {getSortIcon('id')}
                                        </Button>
                                    </TableHead>
                                    <TableHead>
                                        <Button variant="ghost" size="sm" onClick={() => handleSort('data_type')}>
                                            Type {getSortIcon('data_type')}
                                        </Button>
                                    </TableHead>
                                    <TableHead>
                                        <Button variant="ghost" size="sm" onClick={() => handleSort('platform')}>
                                            Platform {getSortIcon('platform')}
                                        </Button>
                                    </TableHead>
                                    <TableHead>
                                        <Button variant="ghost" size="sm" onClick={() => handleSort('account_username')}>
                                            Account {getSortIcon('account_username')}
                                        </Button>
                                    </TableHead>
                                    <TableHead>
                                        <Button variant="ghost" size="sm" onClick={() => handleSort('quality_score')}>
                                            Quality {getSortIcon('quality_score')}
                                        </Button>
                                    </TableHead>
                                    <TableHead>
                                        <Button variant="ghost" size="sm" onClick={() => handleSort('scraped_at')}>
                                            Scraped At {getSortIcon('scraped_at')}
                                        </Button>
                                    </TableHead>
                                    <TableHead>
                                        <Button variant="ghost" size="sm" onClick={() => handleSort('is_complete')}>
                                            Status {getSortIcon('is_complete')}
                                        </Button>
                                    </TableHead>
                                    <TableHead className="w-20">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {filteredData.map((item) => (
                                    <TableRow key={item.id} className="hover:bg-gray-50">
                                        <TableCell>
                                            <Checkbox
                                                checked={selectedItems.includes(item.id)}
                                                onCheckedChange={() => toggleItemSelection(item.id)}
                                            />
                                        </TableCell>
                                        <TableCell className="font-medium">{item.id}</TableCell>
                                        <TableCell>
                                            <div className="flex items-center gap-2">
                                                {getDataTypeIcon(item.data_type)}
                                                <Badge className={getDataTypeColor(item.data_type)} variant="outline">
                                                    {item.data_type}
                                                </Badge>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <Badge variant="secondary">{item.platform}</Badge>
                                        </TableCell>
                                        <TableCell>@{item.account_username}</TableCell>
                                        <TableCell>
                                            <Badge className={getQualityBadgeColor(item.quality_score || 0)}>
                                                {item.quality_score?.toFixed(1) || '0.0'}
                                            </Badge>
                                        </TableCell>
                                        <TableCell className="text-sm">
                                            {new Date(item.scraped_at).toLocaleDateString()} {new Date(item.scraped_at).toLocaleTimeString()}
                                        </TableCell>
                                        <TableCell>
                                            {item.is_complete ? (
                                                <Badge className="bg-green-100 text-green-800">Complete</Badge>
                                            ) : (
                                                <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
                                            )}
                                        </TableCell>
                                        <TableCell>
                                            <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                    <Button variant="ghost" size="sm">
                                                        <MoreHorizontal className="h-4 w-4" />
                                                    </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end">
                                                    <Dialog>
                                                        <DialogTrigger asChild>
                                                            <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                                                <Eye className="h-4 w-4 mr-2" />
                                                                View Details
                                                            </DropdownMenuItem>
                                                        </DialogTrigger>
                                                        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                                                            <DialogHeader>
                                                                <DialogTitle>
                                                                    {item.data_type} Data - ID: {item.id}
                                                                </DialogTitle>
                                                            </DialogHeader>
                                                            <div className="space-y-4">
                                                                <div className="grid grid-cols-2 gap-4">
                                                                    <div>
                                                                        <label className="text-sm font-medium">Platform</label>
                                                                        <p className="text-sm text-gray-600">{item.platform}</p>
                                                                    </div>
                                                                    <div>
                                                                        <label className="text-sm font-medium">Account</label>
                                                                        <p className="text-sm text-gray-600">@{item.account_username}</p>
                                                                    </div>
                                                                    <div>
                                                                        <label className="text-sm font-medium">Quality Score</label>
                                                                        <p className="text-sm text-gray-600">{item.quality_score?.toFixed(1) || '0.0'}</p>
                                                                    </div>
                                                                    <div>
                                                                        <label className="text-sm font-medium">Complete</label>
                                                                        <p className="text-sm text-gray-600">{item.is_complete ? 'Yes' : 'No'}</p>
                                                                    </div>
                                                                    <div>
                                                                        <label className="text-sm font-medium">Task ID</label>
                                                                        <p className="text-sm text-gray-600">{item.task_id}</p>
                                                                    </div>
                                                                    <div>
                                                                        <label className="text-sm font-medium">Content ID</label>
                                                                        <p className="text-sm text-gray-600">{item.platform_content_id || 'N/A'}</p>
                                                                    </div>
                                                                </div>
                                                                <div>
                                                                    <label className="text-sm font-medium">Content</label>
                                                                    <pre className="mt-1 p-3 bg-gray-50 rounded text-xs overflow-auto max-h-64">
                                                                        {JSON.stringify(item.content, null, 2)}
                                                                    </pre>
                                                                </div>
                                                            </div>
                                                        </DialogContent>
                                                    </Dialog>
                                                    <DropdownMenuItem onClick={() => openEditModal(item)}>
                                                        <Edit className="h-4 w-4 mr-2" />
                                                        Edit
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem
                                                        onClick={() => handleDeleteData(item.id)}
                                                        className="text-red-600"
                                                    >
                                                        <Trash2 className="h-4 w-4 mr-2" />
                                                        Delete
                                                    </DropdownMenuItem>
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                ) : (
                    /* Cards View */
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {filteredData.map((item) => (
                            <Card key={item.id} className="p-4 hover:shadow-md transition-shadow">
                                <div className="flex items-start justify-between mb-3">
                                    <div className="flex items-center gap-2">
                                        {getDataTypeIcon(item.data_type)}
                                        <Badge className={getDataTypeColor(item.data_type)}>
                                            {item.data_type}
                                        </Badge>
                                    </div>
                                    <Badge className={getQualityBadgeColor(item.quality_score || 0)}>
                                        {getQualityLabel(item.quality_score || 0)} ({item.quality_score?.toFixed(1) || '0.0'})
                                    </Badge>
                                </div>

                                <div className="space-y-2 mb-3">
                                    <div className="text-sm">
                                        <span className="font-medium">Platform:</span> {item.platform}
                                    </div>
                                    <div className="text-sm">
                                        <span className="font-medium">Account:</span> @{item.account_username}
                                    </div>
                                    <div className="text-sm">
                                        <span className="font-medium">Content ID:</span> {item.platform_content_id || 'N/A'}
                                    </div>
                                    <div className="text-sm">
                                        <span className="font-medium">Task ID:</span> {item.task_id}
                                    </div>
                                    <div className="text-sm">
                                        <span className="font-medium">Scraped:</span> {new Date(item.scraped_at).toLocaleString()}
                                    </div>
                                    <div className="text-sm">
                                        <span className="font-medium">Complete:</span> {item.is_complete ? '✅ Yes' : '❌ No'}
                                    </div>
                                </div>

                                <div className="flex justify-between items-center">
                                    <Badge variant="outline">
                                        ID: {item.id}
                                    </Badge>
                                    <Dialog>
                                        <DialogTrigger asChild>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => setSelectedItem(item)}
                                            >
                                                <Eye className="h-4 w-4 mr-1" />
                                                View
                                            </Button>
                                        </DialogTrigger>
                                        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                                            <DialogHeader>
                                                <DialogTitle>
                                                    {item.data_type} Data - ID: {item.id}
                                                </DialogTitle>
                                            </DialogHeader>
                                            <div className="space-y-4">
                                                <div className="grid grid-cols-2 gap-4">
                                                    <div>
                                                        <label className="text-sm font-medium">Platform</label>
                                                        <p className="text-sm text-gray-600">{item.platform}</p>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-medium">Account</label>
                                                        <p className="text-sm text-gray-600">@{item.account_username}</p>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-medium">Quality Score</label>
                                                        <p className="text-sm text-gray-600">{item.quality_score?.toFixed(1) || '0.0'}</p>
                                                    </div>
                                                    <div>
                                                        <label className="text-sm font-medium">Complete</label>
                                                        <p className="text-sm text-gray-600">{item.is_complete ? 'Yes' : 'No'}</p>
                                                    </div>
                                                </div>
                                                <div>
                                                    <label className="text-sm font-medium">Content</label>
                                                    <pre className="mt-1 p-3 bg-gray-50 rounded text-xs overflow-auto max-h-64">
                                                        {JSON.stringify(item.content, null, 2)}
                                                    </pre>
                                                </div>
                                            </div>
                                        </DialogContent>
                                    </Dialog>
                                </div>
                            </Card>
                        ))}
                    </div>
                )}
            </Card>

            {/* Pagination */}
            {totalCount > pageSize && (
                <div className="flex justify-center items-center gap-4">
                    <Button
                        variant="outline"
                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                        disabled={currentPage === 1 || loading}
                    >
                        Previous
                    </Button>
                    <span className="text-sm text-gray-600">
                        Page {currentPage} of {Math.ceil(totalCount / pageSize)}
                    </span>
                    <Button
                        variant="outline"
                        onClick={() => setCurrentPage(prev => prev + 1)}
                        disabled={currentPage >= Math.ceil(totalCount / pageSize) || loading}
                    >
                        Next
                    </Button>
                </div>
            )}

            {/* Edit Modal */}
            <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>Edit Data Entry</DialogTitle>
                    </DialogHeader>
                    {editingItem && (
                        <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="edit_data_type">Data Type</Label>
                                    <Select
                                        value={editForm.data_type}
                                        onValueChange={(value) => setEditForm({...editForm, data_type: value})}
                                    >
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="VIDEO">Video</SelectItem>
                                            <SelectItem value="profile">Profile</SelectItem>
                                            <SelectItem value="comment">Comment</SelectItem>
                                            <SelectItem value="search_result">Search Result</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div>
                                    <Label htmlFor="edit_account_username">Account Username</Label>
                                    <Input
                                        value={editForm.account_username}
                                        onChange={(e) => setEditForm({...editForm, account_username: e.target.value})}
                                        placeholder="@username"
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="edit_platform_content_id">Content ID</Label>
                                    <Input
                                        value={editForm.platform_content_id}
                                        onChange={(e) => setEditForm({...editForm, platform_content_id: e.target.value})}
                                        placeholder="Platform-specific content ID"
                                    />
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="edit_is_complete"
                                        checked={editForm.is_complete}
                                        onCheckedChange={(checked) => setEditForm({...editForm, is_complete: !!checked})}
                                    />
                                    <Label htmlFor="edit_is_complete">Complete</Label>
                                </div>
                            </div>
                            <div>
                                <Label htmlFor="edit_content">Content (JSON)</Label>
                                <Textarea
                                    value={JSON.stringify(editForm.content, null, 2)}
                                    onChange={(e) => {
                                        try {
                                            const content = JSON.parse(e.target.value);
                                            setEditForm({...editForm, content});
                                        } catch {
                                            // Invalid JSON, keep the text for user to fix
                                        }
                                    }}
                                    className="min-h-32"
                                />
                            </div>
                            <div className="flex justify-end gap-2">
                                <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                                    Cancel
                                </Button>
                                <Button onClick={handleEditData} disabled={loading}>
                                    Update Data
                                </Button>
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </div>
    );
}
