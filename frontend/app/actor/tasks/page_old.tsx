'use client';

import { useEffect, useState } from "react";
import { EnhancedTaskList } from "@/components/actor/EnhancedTaskList";
import { TaskList } from "@/components/actor/TaskList";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Play, Clock, CheckCircle, XCircle, Square, Search, Filter, Target, Activity, RefreshCw } from "lucide-react";
import { getActorAccounts, getActorTasks, type ActorAccount, type ActorTask } from "@/lib/api/actor-system";

export default function TasksPage() {
    // Actor system state
    const [tasks, setTasks] = useState<ActorTask[]>([]);
    const [accounts, setAccounts] = useState<ActorAccount[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(true);

    // Filters
    const [statusFilter, setStatusFilter] = useState<string>('ALL');
    const [typeFilter, setTypeFilter] = useState<string>('ALL');
    const [searchQuery, setSearchQuery] = useState('');

    const loadData = async () => {
        try {
            setLoading(true);
            setError(null);

            const accountsData = await getActorAccounts();
            setAccounts(accountsData);

            // Load Actor tasks
            try {
                const tasksData = await getActorTasks();
                setTasks(tasksData);
            } catch (taskError) {
                console.warn('Failed to load Actor tasks, using mock data:', taskError);
                // Fallback to mock data if API fails
                const mockTasks: ActorTask[] = accountsData.map((account, index) => ({
                    id: index + 1,
                    name: `Sample Task for @${account.username}`,
                    type: 'CONTENT_SEARCH',
                    platform: account.platform,
                    status: index % 3 === 0 ? 'COMPLETED' : index % 3 === 1 ? 'RUNNING' : 'PENDING',
                    created_at: new Date().toISOString(),
                    keywords: 'sample, keywords',
                    max_items: 50
                }));
                setTasks(mockTasks);
            }

        } catch (err: any) {
            setError(err.response?.data?.error || 'Failed to fetch Actor data');
            console.error('Error fetching Actor data:', err);
        } finally {
            setLoading(false);
        }
    };

    // Apply filters
    useEffect(() => {
        let filtered = tasks;
        
        // Status filter
        if (statusFilter !== 'ALL') {
            filtered = filtered.filter(task => task.status === statusFilter);
        }
        
        // Type filter
        if (typeFilter !== 'ALL') {
            filtered = filtered.filter(task => task.type === typeFilter);
        }
        
        // Search filter
        if (searchQuery) {
            filtered = filtered.filter(task =>
                task.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                task.keywords?.toLowerCase().includes(searchQuery.toLowerCase())
            );
        }
        
        return filtered;
    };

    const filteredTasks = getFilteredTasks();

    useEffect(() => {
        loadData();

        // Refresh data every 30 seconds
        const interval = setInterval(() => {
            loadData();
        }, 30000);
        return () => clearInterval(interval);
    }, []);

    const getTaskStats = () => {
        const total = allTasks.length;
        const pending = allTasks.filter(task => task.status === 'PENDING').length;
        const running = allTasks.filter(task => task.status === 'RUNNING').length;
        const completed = allTasks.filter(task => task.status === 'COMPLETED').length;
        const failed = allTasks.filter(task => task.status === 'FAILED').length;
        
        return { total, pending, running, completed, failed };
    };

    const stats = getTaskStats();

    const clearFilters = () => {
        setStatusFilter('ALL');
        setTypeFilter('ALL');
        setSearchQuery('');
    };

    if (loading) {
        return (
            <div className="space-y-4 p-8">
                <Skeleton className="h-[100px] w-full" />
                <Skeleton className="h-[400px] w-full" />
            </div>
        );
    }

    if (error) {
        return (
            <Alert variant="destructive" className="m-8">
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        );
    }

    return (
        <div className="p-8 space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold mb-2">
                        {showNewSystem ? 'My Tasks' : 'Legacy Tasks'}
                    </h1>
                    <p className="text-gray-600">
                        {showNewSystem
                            ? 'Monitor and manage your social media automation tasks'
                            : 'Legacy TikTok-specific task management'
                        }
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Button
                        variant={showNewSystem ? "default" : "outline"}
                        size="sm"
                        onClick={() => setShowNewSystem(true)}
                    >
                        <Activity className="h-4 w-4 mr-2" />
                        Actor System
                    </Button>
                    <Button
                        variant={!showNewSystem ? "default" : "outline"}
                        size="sm"
                        onClick={() => setShowNewSystem(false)}
                    >
                        Legacy TikTok
                    </Button>
                </div>
            </div>
            
            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Play className="h-8 w-8 text-blue-500" />
                        <div>
                            <p className="text-sm text-gray-500">Total Tasks</p>
                            <p className="text-2xl font-bold">{stats.total}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Clock className="h-8 w-8 text-yellow-500" />
                        <div>
                            <p className="text-sm text-gray-500">Pending</p>
                            <p className="text-2xl font-bold">{stats.pending}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Play className="h-8 w-8 text-blue-500" />
                        <div>
                            <p className="text-sm text-gray-500">Running</p>
                            <p className="text-2xl font-bold">{stats.running}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <CheckCircle className="h-8 w-8 text-green-500" />
                        <div>
                            <p className="text-sm text-gray-500">Completed</p>
                            <p className="text-2xl font-bold">{stats.completed}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <XCircle className="h-8 w-8 text-red-500" />
                        <div>
                            <p className="text-sm text-gray-500">Failed</p>
                            <p className="text-2xl font-bold">{stats.failed}</p>
                        </div>
                    </div>
                </Card>
            </div>
            
            {/* Metrics Card */}
            {metrics && (
                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-4">Performance Metrics</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center">
                            <p className="text-sm text-gray-500">Success Rate</p>
                            <p className="text-xl font-semibold">
                                {(metrics.success_rate * 100).toFixed(1)}%
                            </p>
                        </div>
                        <div className="text-center">
                            <p className="text-sm text-gray-500">Average Duration</p>
                            <p className="text-xl font-semibold">
                                {(metrics.average_completion_time || 0).toFixed(2)}s
                            </p>
                        </div>
                        <div className="text-center">
                            <p className="text-sm text-gray-500">Active Accounts</p>
                            <p className="text-xl font-semibold">{metrics.active_accounts}</p>
                        </div>
                        <div className="text-center">
                            <p className="text-sm text-gray-500">Items Scraped</p>
                            <p className="text-xl font-semibold">{metrics.total_items_scraped}</p>
                        </div>
                    </div>
                </Card>
            )}

            
            {/* Tasks List */}
            {showNewSystem ? (
                <EnhancedTaskList
                    tasks={actorTasks}
                    onTasksChange={loadActorData}
                />
            ) : (
                <>
                    {/* Legacy System Filters */}
                    <Card className="p-4">
                        <div className="flex flex-wrap items-center gap-4">
                            <div className="flex items-center gap-2">
                                <Filter className="h-4 w-4 text-gray-500" />
                                <span className="text-sm font-medium">Legacy Filters:</span>
                            </div>

                            <div className="flex items-center gap-2">
                                <Search className="h-4 w-4 text-gray-500" />
                                <Input
                                    placeholder="Search by username..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="w-48"
                                />
                            </div>

                            <Select value={statusFilter} onValueChange={(value: TaskStatus | 'ALL') => setStatusFilter(value)}>
                                <SelectTrigger className="w-32">
                                    <SelectValue placeholder="Status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="ALL">All Status</SelectItem>
                                    <SelectItem value="PENDING">Pending</SelectItem>
                                    <SelectItem value="RUNNING">Running</SelectItem>
                                    <SelectItem value="COMPLETED">Completed</SelectItem>
                                    <SelectItem value="FAILED">Failed</SelectItem>
                                    <SelectItem value="CANCELLED">Cancelled</SelectItem>
                                </SelectContent>
                            </Select>

                            <Button variant="outline" size="sm" onClick={clearFilters}>
                                Clear Filters
                            </Button>

                            <div className="ml-auto">
                                <Badge variant="outline">
                                    {filteredTasks.length} of {allTasks.length} tasks
                                </Badge>
                            </div>
                        </div>
                    </Card>

                    <Card className="p-6">
                        <TaskList tasks={filteredTasks} onTasksChange={fetchData} />
                    </Card>
                </>
            )}
        </div>
    );
}