'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, Target, Activity, CheckCircle, XCircle, Clock } from 'lucide-react';

// Import the new Actor system components
import { ModernTaskManager } from '@/components/actor/ModernTaskManager';
import { getActorAccounts, type ActorAccount } from '@/lib/api/actor-system';

export default function ActorTasksPage() {
    const [accounts, setAccounts] = useState<ActorAccount[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const loadAccounts = async () => {
        try {
            setLoading(true);
            setError(null);
            const accountsData = await getActorAccounts();
            setAccounts(accountsData);
        } catch (err: any) {
            setError(err.message || 'Failed to load accounts');
            console.error('Error loading accounts:', err);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadAccounts();
    }, []);

    if (loading) {
        return (
            <div className="container mx-auto p-6 space-y-6">
                <div className="space-y-2">
                    <Skeleton className="h-8 w-64" />
                    <Skeleton className="h-4 w-96" />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    {[...Array(5)].map((_, i) => (
                        <Card key={i} className="p-4">
                            <Skeleton className="h-16 w-full" />
                        </Card>
                    ))}
                </div>
                <Card className="p-6">
                    <Skeleton className="h-64 w-full" />
                </Card>
            </div>
        );
    }

    if (error) {
        return (
            <div className="container mx-auto p-6">
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            </div>
        );
    }

    return (
        <div className="container mx-auto p-6">
            <ModernTaskManager
                accounts={accounts}
                onAccountsChange={loadAccounts}
            />
        </div>
    );
}
