'use client';

import { useState, useEffect } from 'react';
import { Category } from '@/lib/types';
import { getCategories, deleteCategory } from '@/lib/api';
import CategoryForm from '@/components/posts/categories/CategoryForm';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { PrivateRoute } from '@/components/PrivateRoute';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchCategories = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getCategories();
      console.log('Categories response:', response);
      setCategories(response.results || []);
    } catch (error: any) {
      console.error('Error fetching categories:', error);
      setError('Failed to load categories. Please try again.');
      toast({
        title: "Error",
        description: "Failed to load categories. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  const handleDeleteCategory = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this category?')) {
      try {
        await deleteCategory(id);
        setCategories(categories.filter(cat => cat.id !== id));
        toast({
          title: "Category deleted",
          description: "The category has been successfully deleted.",
        });
      } catch (error: any) {
        console.error('Error deleting category:', error);
        toast({
          title: "Error",
          description: "Failed to delete the category. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  const handleEditSuccess = () => {
    fetchCategories();
    setEditingCategory(null);
    toast({
      title: "Success",
      description: editingCategory ? "Category updated successfully." : "Category created successfully.",
    });
  };

  if (loading) {
    return (
      <PrivateRoute>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </PrivateRoute>
    );
  }

  return (
    <PrivateRoute>
      <header className="flex h-16 shrink-0 items-center gap-2">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbPage>Categories</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>

      <main className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <section className="rounded-lg border p-4">
          <h2 className="text-lg font-semibold mb-4">
            {editingCategory ? 'Edit Category' : 'Create New Category'}
          </h2>
          <CategoryForm
            category={editingCategory ?? undefined}
            isEditing={!!editingCategory}
            onSuccess={handleEditSuccess}
          />
        </section>

        <section className="rounded-lg border">
          <header className="p-4">
            <h2 className="text-lg font-semibold">Categories</h2>
          </header>
          <div className="border-t">
            {error ? (
              <div className="p-4 text-center text-red-500">{error}</div>
            ) : categories.length > 0 ? (
              <ul className="divide-y">
                {categories.map((category) => (
                  <li key={category.id} className="flex items-center justify-between p-4">
                    <div>
                      <h3 className="font-medium">{category.name}</h3>
                      {category.description && (
                        <p className="text-sm text-muted-foreground mt-1">
                          {category.description}
                        </p>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setEditingCategory(category)}
                      >
                        Edit
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteCategory(category.id)}
                      >
                        Delete
                      </Button>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <div className="p-4 text-center text-muted-foreground">
                No categories found. Create your first category above.
              </div>
            )}
          </div>
        </section>
      </main>
    </PrivateRoute>
  );
}
