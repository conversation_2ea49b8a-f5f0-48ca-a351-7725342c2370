"use client";
import { useRouter, useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useCrawler<PERSON><PERSON> } from "@/hooks/useCrawler<PERSON>pi";
import { PageHeader } from "@/components/themes/page-header";
import { Button } from "@/components/ui/button";

export default function ScheduleDetailPage() {
  const { id } = useParams();
  const { getSchedule } = useCrawlerApi();
  const [schedule, setSchedule] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    async function fetchSchedule() {
      if (!id) return;
      setLoading(true);
      const res = await getSchedule(id);
      setSchedule(res?.data);
      setLoading(false);
    }
    fetchSchedule();
  }, [id]);

  if (loading) return <div>Loading...</div>;
  if (!schedule) return <div>Schedule not found.</div>;

  return (
    <div className="p-6">
      <PageHeader
        breadcrumbs={[
          { href: "/", label: "Home" },
          { href: "/dashboard", label: "Dashboard" },
          { href: "/dashboard/schedules", label: "Schedules" },
          { label: `Detail`, current: true }
        ]}
      />
      <h1 className="text-2xl font-bold mb-4">Schedule Detail</h1>
      <div className="mb-4">
        <div><b>ID:</b> {schedule.id}</div>
        <div><b>Project:</b> {schedule.project?.name || "-"}</div>
        <div><b>Frequency:</b> {schedule.frequency}</div>
        <div><b>Start Time:</b> {schedule.start_time}</div>
        <div><b>End Time:</b> {schedule.end_time}</div>
        <div><b>Active:</b> {schedule.is_active ? "Yes" : "No"}</div>
      </div>
      <Button onClick={() => router.push(`/dashboard/schedules/${id}/edit`)}>Edit</Button>
    </div>
  );
}
