"use client";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useCrawler<PERSON><PERSON> } from "@/hooks/useCrawlerApi";
import { PageHeader } from "@/components/themes/page-header";
import ScheduleForm from "@/components/crawler/ScheduleForm";

export default function EditSchedulePage() {
  const { id } = useParams();
  const { getSchedule, getProjects } = useCrawlerApi();
  const [schedule, setSchedule] = useState<any>(null);
  const [eligibleProjects, setEligibleProjects] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    async function fetchData() {
      if (!id) return;
      setLoading(true);
      const [scheduleRes, projectsRes] = await Promise.all([
        getSchedule(id),
        getProjects({ page_size: 100 })
      ]);
      setSchedule(scheduleRes?.data);
      setEligibleProjects(projectsRes?.data?.results || projectsRes?.data || []);
      setLoading(false);
    }
    fetchData();
  }, [id]);

  if (loading) return <div>Loading...</div>;
  if (!schedule) return <div>Schedule not found.</div>;

  return (
    <div className="p-6">
      <PageHeader
        breadcrumbs={[
          { href: "/", label: "Home" },
          { href: "/dashboard", label: "Dashboard" },
          { href: "/dashboard/schedules", label: "Schedules" },
          { label: `Edit`, current: true }
        ]}
      />
      <h1 className="text-2xl font-bold mb-4">Edit Schedule</h1>
      <ScheduleForm
        projectId={schedule.project?.id || ''}
        schedule={schedule}
        eligibleProjects={eligibleProjects}
        onSuccess={() => router.push(`/dashboard/schedules`)}
      />
    </div>
  );
}
