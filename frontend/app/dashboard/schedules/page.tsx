"use client";
import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { PageHeader } from "@/components/themes/page-header";
import SchedulesTable from "@/components/crawler/SchedulesTable";
import Link from "next/link";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useCrawlerApi } from '@/hooks/useCrawlerApi';

const SchedulesPage = () => {
  const [deleteSchedule, setDeleteSchedule] = useState<any | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [deleteOpen, setDeleteOpen] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [projects, setProjects] = useState<any[]>([]);
  const [schedules, setSchedules] = useState<any[]>([]);
  const [eligibleProjects, setEligibleProjects] = useState<any[]>([]);
  const { getProjects, getSchedules, deleteSchedule: deleteScheduleApi } = useCrawlerApi();
  useEffect(() => {
    async function fetchData() {
      const projectsRes = await getProjects({ page_size: 100 });
      const schedulesRes = await getSchedules({ page_size: 100 });
      const projectsList = Array.isArray(projectsRes?.data?.results) ? projectsRes.data.results : projectsRes.data;
      const schedulesList = Array.isArray(schedulesRes?.data?.results) ? schedulesRes.data.results : schedulesRes.data;
      setProjects(projectsList);
      setSchedules(schedulesList);
      // Only allow projects that do not have a schedule
      const scheduledProjectIds = new Set(schedulesList.map((s: any) => s.project?.id || s.project_id));
      setEligibleProjects(projectsList.filter((p: any) => !scheduledProjectIds.has(p.id)));
    }
    fetchData();
  }, [refreshKey]);
  const handleDelete = (row: any) => {
    setDeleteSchedule(row);
    setDeleteOpen(true);
  };
  const handleDeleteConfirm = async () => {
    if (deleteSchedule) {
      await deleteScheduleApi(deleteSchedule.id);
      setDeleteOpen(false);
      setDeleteSchedule(null);
      setRefreshKey(k => k + 1);
    }
  };
  return (
    <>
      <PageHeader
        breadcrumbs={[
          { href: "/", label: "Home" },
          { href: "/dashboard", label: "Dashboard" },
          { label: "Schedules", current: true }
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold tracking-tight">Crawling Schedules</h1>
              <p className="text-sm text-muted-foreground">
                Manage and monitor your crawling schedules
              </p>
            </div>
            <Link href="/dashboard/schedules/create">
              <Button >
                <Plus className="mr-2 h-4 w-4" />
                Create Schedule
              </Button>
            </Link>
          </div>
          <div className="rounded-xl overflow-hidden">
            <SchedulesTable key={refreshKey} onDelete={handleDelete} />
          </div>
        </div>
      </div>
      {/* Delete Modal */}
      <Dialog open={deleteOpen} onOpenChange={setDeleteOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Schedule</DialogTitle>
          </DialogHeader>
          <div className="py-4">Are you sure you want to delete this schedule?</div>
          <div className="flex gap-2 justify-end">
            <Button variant="outline" onClick={() => setDeleteOpen(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleDeleteConfirm}>Delete</Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default SchedulesPage;