"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useCrawler<PERSON><PERSON> } from "@/hooks/useCrawlerApi";
import { PageHeader } from "@/components/themes/page-header";
import ScheduleForm from "@/components/crawler/ScheduleForm";

export default function CreateSchedulePage() {
  const { getProjects } = useCrawlerApi();
  const [eligibleProjects, setEligibleProjects] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    async function fetchProjects() {
      setLoading(true);
      const projectsRes = await getProjects({ page_size: 100 });
      setEligibleProjects(projectsRes?.data?.results || projectsRes?.data || []);
      setLoading(false);
    }
    fetchProjects();
  }, []);

  if (loading) return <div>Loading...</div>;

  return (
    <div className="p-6">
      <PageHeader
        breadcrumbs={[
          { href: "/", label: "Home" },
          { href: "/dashboard", label: "Dashboard" },
          { href: "/dashboard/schedules", label: "Schedules" },
          { label: `Create`, current: true }
        ]}
      />
      <h1 className="text-2xl font-bold mb-4">Create Schedule</h1>
      <ScheduleForm
        eligibleProjects={eligibleProjects}
        onSuccess={() => router.push(`/dashboard/schedules`)}
      />
    </div>
  );
}
