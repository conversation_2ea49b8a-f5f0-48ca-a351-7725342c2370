"use client";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useCrawler<PERSON><PERSON> } from "@/hooks/useCrawler<PERSON>pi";
import { PageHeader } from "@/components/themes/page-header";
import { Button } from "@/components/ui/button";

export default function TaskDetailPage() {
  const { id } = useParams();
  const { getTask } = useCrawlerApi();
  const [task, setTask] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    async function fetchTask() {
      if (!id) return;
      setLoading(true);
      try {
        const res = await getTask(Array.isArray(id) ? id[0] : id);
        setTask(res?.data);
      } catch {
        setTask(null);
      }
      setLoading(false);
    }
    fetchTask();
  }, [id]);

  if (loading) return <div>Loading...</div>;
  if (!task) return <div className="p-6">Task not found.</div>;

  return (
    <div className="p-6">
      <PageHeader
        breadcrumbs={[
          { href: "/", label: "Home" },
          { href: "/dashboard", label: "Dashboard" },
          { href: "/dashboard/tasks", label: "Tasks" },
          { label: `Detail`, current: true }
        ]}
      />
      <h1 className="text-2xl font-bold mb-4">Task Detail</h1>
      <div className="mb-4 space-y-2">
        <div><b>ID:</b> {task.id}</div>
        <div><b>Status:</b> {task.status}</div>
        <div><b>Project:</b> {task.schedule_project || '-'}</div>
        <div><b>Started At:</b> {task.started_at}</div>
        <div><b>Finished At:</b> {task.finished_at || '-'}</div>
        <div><b>Duration:</b> {task.duration || '-'}</div>
        <div><b>Data Count:</b> {task.crawled_data_count}</div>
        {task.error_message && (
          <div className="text-red-600"><b>Error:</b> {task.error_message}</div>
        )}
      </div>
      <Button variant="outline" onClick={() => router.back()}>
        Back
      </Button>
    </div>
  );
}
