import { ColumnDef } from "@tanstack/react-table"
// Define Task type here if not exported from "@/types"
type Task = {
  id: string
  schedule_project: string
  started_at: string | Date
  duration?: string
  status: string
  crawled_data_count: number
}
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"

export const activeTasksColumns: ColumnDef<Task>[] = [
  {
    accessorKey: "id",
    header: "Task ID",
    cell: ({ row }) => `#${(row.getValue("id") as string).slice(0, 6)}`,
  },
  {
    accessorKey: "schedule_project",
    header: "Project",
    cell: ({ row }) => (
      <div className="font-medium">
        {row.getValue("schedule_project")}
      </div>
    ),
  },
  {
    accessorKey: "started_at",
    header: "Started",
    cell: ({ row }) => (
      <div className="text-sm text-gray-600">
        {format(new Date(row.getValue("started_at")), "PPpp")}
      </div>
    ),
  },
  {
    accessorKey: "duration",
    header: "Duration",
    cell: ({ row }) => (
      <div className="font-mono">
        {row.getValue("duration") || "N/A"}
      </div>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => (
      <Badge 
        variant={
          row.getValue("status") === "running" ? "default" : 
          row.getValue("status") === "completed" ? "secondary" : 
          "destructive"
        }
        className="capitalize"
      >
        {row.getValue("status")}
      </Badge>
    ),
  },
  {
    accessorKey: "crawled_data_count",
    header: "Data Items",
    cell: ({ row }) => (
      <div className="text-center">
        {(row.getValue("crawled_data_count") as number) > 0 ? (
          <span className="text-green-600 font-medium">
            {row.getValue("crawled_data_count")}
          </span>
        ) : (
          <span className="text-gray-400">0</span>
        )}
      </div>
    ),
  },
]

export const storageStatsColumns: ColumnDef<any>[] = [
  {
    accessorKey: "domain",
    header: "Domain",
    cell: ({ row }) => (
      <div className="font-medium">
        {row.getValue("domain")}
      </div>
    ),
  },
  {
    accessorKey: "url_count",
    header: "URLs Crawled",
    cell: ({ row }) => (
      <div className="text-center font-medium">
        {row.getValue("url_count")}
      </div>
    ),
  },
  {
    accessorKey: "original_size",
    header: "Original Size",
    cell: ({ row }) => (
      <div className="text-sm">
        {formatBytes(row.getValue("original_size"))}
      </div>
    ),
  },
  {
    accessorKey: "compressed_size",
    header: "Compressed Size",
    cell: ({ row }) => (
      <div className="text-sm">
        {formatBytes(row.getValue("compressed_size"))}
      </div>
    ),
  },
  {
    accessorKey: "compression_ratio",
    header: "Compression Ratio",
    cell: ({ row }) => (
      <div className="text-right font-medium">
        {Math.round((row.getValue("compression_ratio") as number) * 100)}%
      </div>
    ),
  },
]

export const errorLogsColumns: ColumnDef<any>[] = [
  {
    accessorKey: "timestamp",
    header: "Timestamp",
    cell: ({ row }) => (
      <div className="text-sm text-gray-600">
        {format(new Date(row.getValue("timestamp")), "PPpp")}
      </div>
    ),
  },
  {
    accessorKey: "task_id",
    header: "Task",
    cell: ({ row }) => (
      <div className="font-mono">
        #{(row.getValue("task_id") as string).slice(0, 6)}
      </div>
    ),
  },
  {
    accessorKey: "url",
    header: "URL",
    cell: ({ row }) => (
      <div className="truncate max-w-[200px]">
        {row.getValue("url")}
      </div>
    ),
  },
  {
    accessorKey: "error_type",
    header: "Error Type",
    cell: ({ row }) => (
      <Badge variant="destructive">
        {row.getValue("error_type")}
      </Badge>
    ),
  },
  {
    accessorKey: "message",
    header: "Message",
    cell: ({ row }) => (
      <div className="truncate max-w-[300px]">
        {row.getValue("message")}
      </div>
    ),
  },
]

// Helper function to format bytes
function formatBytes(bytes: number, decimals = 2) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}