'use client';
import React from 'react';
import { useParams } from 'next/navigation';
import { useCrawler<PERSON>pi } from '@/hooks/useCrawlerApi';
import ProjectForm from '@/components/crawler/ProjectForm';
import { ChevronLeftIcon } from 'lucide-react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { PageHeader } from "@/components/themes/page-header";

const EditProjectPage = () => {
  const { id } = useParams();
  const { getProject, loading, error } = useCrawlerApi();
  const [project, setProject] = React.useState<any>(null);

  React.useEffect(() => {
    const fetchProject = async () => {
      try {
        const projectData = await getProject(id as string);
        setProject(projectData.data);
      } catch (err) {
        console.error('Failed to fetch project:', err);
      }
    };

    if (id) {
      fetchProject();
    }
  }, [id]);
  

  return (
    <>
      <PageHeader
        breadcrumbs={[
          { href: "/", label: "Home" },
          { href: "/dashboard", label: "Dashboard" },
          { href: "/dashboard/projects", label: "Projects" },
          { href: `/dashboard/projects/${id}`, label: project?.name || "Project" },
          { label: "Edit", current: true }
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="space-y-6 max-w-3xl mx-auto">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold tracking-tight">Edit Project</h1>
              {project?.keywords && (
                <p className="text-muted-foreground text-xs">{project.keywords}</p>
              )}
            </div>
            <Link href={`/dashboard/projects/${id}`}>
              <Button variant="ghost" className='border'>
                <ChevronLeftIcon className="h-5 w-5 mr-2" />
                Back to Project
              </Button>
            </Link>
          </div>
          <Card>
            <CardContent>
              {loading && !project ? (
                <div className="container mx-auto px-4 py-8">
                  <Skeleton className="h-4 w-64" />
                  <Skeleton className="h-24 w-full" />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Skeleton className="h-10" />
                    <Skeleton className="h-10" />
                  </div>
                  <Skeleton className="h-40 w-full" />
                  <div className="flex justify-end space-x-3 pt-4">
                    <Skeleton className="h-10 w-24" />
                    <Skeleton className="h-10 w-32" />
                  </div>
                </div>
              ) : error ? (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                  {error}
                </div>
              ) : project ? (
                <div className="max-w-3xl">
                  <ProjectForm mode="edit" initialData={project} />
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">Project not found</p>
                  
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default EditProjectPage;