'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import { useCrawler<PERSON>pi } from '@/hooks/useCrawlerApi';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import ProjectInfo from '@/components/crawler/ProjectInfo';
import SchedulesList from '@/components/crawler/SchedulesList';
import CrawledDataList from '@/components/crawler/CrawledDataList';
import TaskList from '@/components/crawler/TaskList';
import Link from 'next/link';
import { ChevronLeftIcon } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { PageHeader } from "@/components/themes/page-header";
import { Plus } from "lucide-react";

const ProjectDetailPage = () => {
  const { id } = useParams();
  const { getProject, getSchedules, loading, error } = useCrawlerApi();
  const [project, setProject] = React.useState<any>(null);
  const [schedules, setSchedules] = React.useState<any[]>([]);
  const [activeTab, setActiveTab] = React.useState('overview');

  React.useEffect(() => {
    const fetchProject = async () => {
      const projectData = await getProject(id as string);
      setProject(projectData.data);
    };

    const fetchSchedules = async () => {
      const schedulesData = await getSchedules({ project_id: id });
      // Support both paginated and non-paginated responses
      let scheduleList = [];
      if (schedulesData && schedulesData.data) {
        if (Array.isArray(schedulesData.data)) {
          scheduleList = schedulesData.data;
        } else if (Array.isArray(schedulesData.data.results)) {
          scheduleList = schedulesData.data.results;
        }
      }
      setSchedules(scheduleList);
    };

    if (id) {
      fetchProject();
      fetchSchedules();
    }
  }, [id]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Skeleton className="h-8 w-48 mb-4" />
          <Skeleton className="h-4 w-64" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-32 rounded-xl" />
          ))}
        </div>
        <Tabs defaultValue="overview">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="schedules">Schedules</TabsTrigger>
            <TabsTrigger value="tasks">Tasks</TabsTrigger>
            <TabsTrigger value="data">Crawled Data</TabsTrigger>
          </TabsList>
          <div className="mt-6">
            <Skeleton className="h-64 w-full rounded-xl" />
          </div>
        </Tabs>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error! </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg mb-4">Project not found</div>
          <Link href="/dashboard/projects">
            <Button>
              Back to Projects
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      <PageHeader
        breadcrumbs={[
          { href: "/", label: "Home" },
          { href: "/dashboard", label: "Dashboard" },
          { href: "/dashboard/projects", label: "Projects" },
          { label: project.name, current: true }
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold tracking-tight">{project.name}</h1>
              <p className="text-sm text-muted-foreground">{project.keywords}</p>
            </div>
            <div className="flex space-x-2">
              <Link href={`/dashboard/projects/${project.id}/edit`}>
                <Button variant="outline">Edit Project</Button>
              </Link>
              
            </div>
          </div>
          <div className="rounded-xl overflow-hidden">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-4 w-full ">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="schedules">Schedules</TabsTrigger>
                <TabsTrigger value="tasks">Tasks</TabsTrigger>
                <TabsTrigger value="data">Crawled Data</TabsTrigger>
              </TabsList>
              <TabsContent value="overview" className="mt-6">
                <ProjectInfo project={project} schedules={schedules} />
              </TabsContent>
              <TabsContent value="schedules" className="mt-6">
                <SchedulesList 
                  projectId={project.id} 
                  schedules={schedules} 
                  onScheduleCreated={async () => {
                    const schedulesData = await getSchedules(id as string);
                    let scheduleList = [];
                    if (schedulesData && schedulesData.data) {
                      if (Array.isArray(schedulesData.data)) {
                        scheduleList = schedulesData.data;
                      } else if (Array.isArray(schedulesData.data.results)) {
                        scheduleList = schedulesData.data.results;
                      }
                    }
                    setSchedules(scheduleList);
                  }}
                />
              </TabsContent>
              <TabsContent value="tasks" className="mt-6">
                <TaskList projectId={project.id} />
              </TabsContent>
              <TabsContent value="data" className="mt-6">
                <CrawledDataList projectId={project.id} />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </>
  );
};

export default ProjectDetailPage;