"use client"
import React from 'react';
import ProjectForm from '../../../../components/crawler/ProjectForm';
import { ChevronLeftIcon } from 'lucide-react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PageHeader } from "@/components/themes/page-header";

const CreateProjectPage = () => {
  return (
    <>
      <PageHeader
        breadcrumbs={[
          { href: "/", label: "Home" },
          { href: "/dashboard", label: "Dashboard" },
          { href: "/dashboard/projects", label: "Projects" },
          { label: "Create", current: true }
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="space-y-6 max-w-3xl mx-auto">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold tracking-tight">Create New Project</h1>
              <p className="text-muted-foreground text-xs">
                Configure a new crawling project by filling in the details below
              </p>
            </div>
            <Link href="/dashboard/projects">
              <Button variant="ghost" className='border'>
                <ChevronLeftIcon className="h-5 w-5 mr-2" />
                Back to Projects
              </Button>
            </Link>
          </div>
          <Card>
            <CardContent>
              <div className="max-w-3xl">
                <ProjectForm />
              </div>
            </CardContent>
          </Card>
          <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6">
            <h3 className="text-lg font-medium text-blue-800 dark:text-blue-200 mb-3">Best Practices</h3>
            <ul className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
              <li className="flex items-start">
                <div className="h-1.5 w-1.5 rounded-full bg-blue-500 mt-1.5 mr-2"></div>
                <span>Use specific keywords for better crawling results</span>
              </li>
              <li className="flex items-start">
                <div className="h-1.5 w-1.5 rounded-full bg-blue-500 mt-1.5 mr-2"></div>
                <span>Select platforms that match your target audience</span>
              </li>
              <li className="flex items-start">
                <div className="h-1.5 w-1.5 rounded-full bg-blue-500 mt-1.5 mr-2"></div>
                <span>Set realistic date ranges for historical data collection</span>
              </li>
              <li className="flex items-start">
                <div className="h-1.5 w-1.5 rounded-full bg-blue-500 mt-1.5 mr-2"></div>
                <span>Use Website platform for RSS/Sitemap-based crawling</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
};

export default CreateProjectPage;