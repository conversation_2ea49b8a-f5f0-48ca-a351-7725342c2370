'use client';

import React from 'react';
import ProjectsTable from '@/components/crawler/ProjectsTable';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { PageHeader } from "@/components/themes/page-header";
import { Plus } from "lucide-react";

const ProjectsPage = () => {
  return (
    <>
      <PageHeader
        breadcrumbs={[
          { href: "/", label: "Home" },
          { href: "/dashboard", label: "Dashboard" },
          { label: "Projects", current: true }
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold tracking-tight">Crawler Projects</h1>
              <p className="text-sm text-muted-foreground">
                Manage and monitor your crawling projects
              </p>
            </div>
            <Link href="/dashboard/projects/create">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                New Project
              </Button>
            </Link>
          </div>
          <div className="rounded-xl overflow-hidden">
            <ProjectsTable />
          </div>
        </div>
      </div>
    </>
  );
};

export default ProjectsPage;