"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus } from "lucide-react"
import Link from "next/link"
import PostsList from "@/components/posts/PostsList"
import { PageHeader } from "@/components/themes/page-header"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"

export default function PostsPage() {
  return (
    <>
      <PageHeader
        breadcrumbs={[
          { href: "/", label: "Home" },
          { label: "Posts", current: true }
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">All Posts</h1>
              <p className="text-muted-foreground">
                Discover and manage blog posts
              </p>
            </div>
            <Link href="/posts/create">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Create Post
              </Button>
            </Link>
          </div>
          <PostsList />
        </div>
      </div>
    </>
  )
}
