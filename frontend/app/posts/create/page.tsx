"use client"

import { useRouter } from "next/navigation"
import { createPost } from "@/lib/api"
import { useToast } from "@/components/ui/use-toast"
import PostForm from "@/components/posts/PostForm"
import { PageHeader } from "@/components/themes/page-header"

export default function CreatePostPage() {
  const router = useRouter()
  const { toast } = useToast()

  return (
    <>
      <PageHeader
        breadcrumbs={[
          { href: "/", label: "Home" },
          { href: "/posts", label: "Posts" },
          { label: "Create Post", current: true }
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Create New Post</h1>
            <p className="text-muted-foreground">
              Share your thoughts and ideas with the world
            </p>
          </div>
          <PostForm 
            onSubmit={async (formData) => {
              try {
                await createPost(formData);
                toast({
                  title: "Success",
                  description: "Post created successfully",
                });
                router.push("/posts");
                router.refresh();
              } catch (error) {
                console.error(error);
                toast({
                  title: "Error",
                  description: "Failed to create post",
                  variant: "destructive",
                });
              }
            }}
            isSubmitting={false}
          />
        </div>
      </div>
    </>
  )
}
