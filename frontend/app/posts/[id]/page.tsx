import PostDetail from '@/components/posts/PostDetail'
import { PageHeader } from '@/components/themes/page-header'

export default async function PostPage({ params }: { params: { id: string } }) {
  return (
    <>
      <PageHeader
        breadcrumbs={[
          { href: "/", label: "Home" },
          { href: "/posts", label: "Posts" },
          { label: "Post Details", current: true }
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <PostDetail postId={params.id} />
      </div>
    </>
  )
}