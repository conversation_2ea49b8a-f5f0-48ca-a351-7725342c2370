"use client";

import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import PostForm from "@/components/posts/PostForm";
import { api } from "@/lib/axios";

export default function CreatePostPage() {
  const { user } = useAuth();
  const router = useRouter();

  if (!user) {
    router.push("/login");
    return null;
  }

  const onSubmit = async (values: { title: string; content: string }) => {
    try {
      const res = await api.post('/posts/', values);
      router.push(`/posts/${res.data.id}`);
    } catch (error) {
      console.error("Failed to create post:", error);
    }
  };

  return (
    <div className="max-w-3xl mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Create Post</h1>
      <PostForm onSubmit={onSubmit} isSubmitting={false} initialValues={undefined} />
    </div>
  );
}