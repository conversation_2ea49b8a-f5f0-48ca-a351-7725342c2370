"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import PostForm from "@/components/posts/PostForm";
import { getPost, updatePost } from "@/lib/api";
import { useToast } from "@/components/ui/use-toast";
import { PrivateRoute } from "@/components/PrivateRoute";
import { PageHeader } from "@/components/themes/page-header";

export default function EditPostPage() {
  const params = useParams();
  const { user } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const id = params.id as string;
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { data: post, isLoading } = useQuery({
    queryKey: ['post', id],
    queryFn: () => getPost(Number(id)),
    retry: false
  });

  useEffect(() => {
    if (post && user && post.author_id !== user.id) {
      toast({
        title: "Unauthorized",
        description: "You can only edit your own posts",
        variant: "destructive"
      });
      router.push(`/posts/${id}`);
    }
  }, [post, user, id, router, toast]);

  if (!user || !post) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="text-center py-8">
        <h2 className="text-2xl font-bold">Post not found</h2>
      </div>
    );
  }

  if (user?.id !== post.author_id) {
    router.push(`/posts/${id}`);
    return null;
  }

  const onSubmit = async (formData: FormData) => {
    setIsSubmitting(true);
    try {
      const updatedPost = await updatePost(Number(id), formData);
      // Update the cache with the new data
      queryClient.setQueryData(['post', id], updatedPost);
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['posts'] });
      toast({
        title: "Success",
        description: "Post updated successfully",
      });
      router.push(`/posts/${id}`);
    } catch (error: any) {
      console.error("Failed to update post:", error);
      toast({
        title: "Error",
        description: error.response?.data?.detail || "Failed to update post",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <PrivateRoute>
      <>
        <PageHeader
          breadcrumbs={[
            { href: "/", label: "Home" },
            { href: "/posts", label: "Posts" },
            { href: `/posts/${id}`, label: post?.title || "Post" },
            { label: "Edit", current: true }
          ]}
        />
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Edit Post</h1>
              <p className="text-muted-foreground">
                Make changes to your post
              </p>
            </div>
            <PostForm
              post={post}
              isEditing={true}
              onSubmit={onSubmit}
              isSubmitting={isSubmitting}
            />
          </div>
        </div>
      </>
    </PrivateRoute>
  );
}