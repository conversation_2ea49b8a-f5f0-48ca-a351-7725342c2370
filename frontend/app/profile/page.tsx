'use client';

import { useAuth } from '@/components/auth/AuthProvider';
import { PrivateRoute } from '@/components/PrivateRoute';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ProfileForm } from '@/components/profile/ProfileForm';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/themes/page-header"

export default function ProfilePage() {
  const { user } = useAuth();

  return (
    <PrivateRoute>
      <PageHeader
        breadcrumbs={[
          { href: "/", label: "Home" },
          { label: "Profile", current: true }
        ]}
      />
      <div className="container mx-auto p-6 space-y-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbPage>Profile</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Profile Settings</CardTitle>
            </CardHeader>
            <CardContent>
              {user && <ProfileForm user={user} />}
            </CardContent>
          </Card>
        </div>
      </div>
    </PrivateRoute>
  );
}
