'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Loader2, Eye, EyeOff, Shield, CheckCircle, AlertCircle, Plus } from 'lucide-react';
import { 
  getAvailablePlatforms, 
  createActorAccount, 
  authenticateActorAccount,
  getPlatformIcon,
  getPlatformColor,
  type Platform 
} from '@/lib/api/actor-system';

interface ActorLoginFormProps {
  onLoginSuccess?: (result: any) => void;
  onLoginError?: (error: string) => void;
  onAccountCreated?: (account: any) => void;
}

export function ActorLoginForm({ onLoginSuccess, onLoginError, onAccountCreated }: ActorLoginFormProps) {
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [formData, setFormData] = useState({
    platform: '',
    username: '',
    password: '',
    email: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [mode, setMode] = useState<'create' | 'authenticate'>('create');

  useEffect(() => {
    loadPlatforms();
  }, []);

  const loadPlatforms = async () => {
    try {
      const availablePlatforms = await getAvailablePlatforms();
      setPlatforms(availablePlatforms);
      
      // Default to TikTok if available
      if (availablePlatforms.length > 0) {
        const tiktok = availablePlatforms.find(p => p.value === 'tiktok');
        if (tiktok) {
          setFormData(prev => ({ ...prev, platform: tiktok.value }));
        } else {
          setFormData(prev => ({ ...prev, platform: availablePlatforms[0].value }));
        }
      }
    } catch (err: any) {
      console.error('Error loading platforms:', err);
      setError('Failed to load available platforms');
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError(null);
    setSuccess(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.platform || !formData.username || !formData.password) {
      setError('Platform, username, and password are required');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      if (mode === 'create') {
        // Create new account
        const createResult = await createActorAccount({
          platform: formData.platform,
          username: formData.username,
          password: formData.password,
          email: formData.email || undefined
        });

        if (createResult.success) {
          setSuccess(`Successfully created ${formData.platform} account!`);
          
          // Automatically authenticate the new account
          const authResult = await authenticateActorAccount(createResult.account_id);
          
          if (authResult.success) {
            setSuccess(`Account created and authenticated successfully!`);
            if (onAccountCreated) {
              onAccountCreated(createResult.account);
            }
            if (onLoginSuccess) {
              onLoginSuccess(authResult);
            }
          } else {
            setError(`Account created but authentication failed: ${authResult.error}`);
          }
        } else {
          setError(createResult.error);
        }
      } else {
        // Authenticate existing account (this would need account selection)
        setError('Authentication mode not yet implemented. Please use create mode.');
      }

    } catch (err: any) {
      console.error('Form submission failed:', err);
      const errorMessage = err.response?.data?.error || err.message || 'Operation failed';
      setError(errorMessage);
      
      if (onLoginError) {
        onLoginError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const selectedPlatform = platforms.find(p => p.value === formData.platform);

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Actor Account Setup
        </CardTitle>
        <CardDescription>
          Create and authenticate accounts across social media platforms
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Platform Selection */}
          <div className="space-y-2">
            <Label htmlFor="platform">Platform</Label>
            <Select
              value={formData.platform}
              onValueChange={(value) => handleInputChange('platform', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a platform">
                  {selectedPlatform && (
                    <div className="flex items-center gap-2">
                      <span>{getPlatformIcon(selectedPlatform.value)}</span>
                      <span>{selectedPlatform.label}</span>
                    </div>
                  )}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {platforms.map((platform) => (
                  <SelectItem key={platform.value} value={platform.value}>
                    <div className="flex items-center gap-2">
                      <span>{getPlatformIcon(platform.value)}</span>
                      <span>{platform.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Username */}
          <div className="space-y-2">
            <Label htmlFor="username">Username</Label>
            <Input
              id="username"
              type="text"
              placeholder={`Your ${selectedPlatform?.label || 'platform'} username`}
              value={formData.username}
              onChange={(e) => handleInputChange('username', e.target.value)}
              disabled={loading}
            />
          </div>

          {/* Password */}
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Your password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                disabled={loading}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
                disabled={loading}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {/* Email (Optional) */}
          <div className="space-y-2">
            <Label htmlFor="email">Email (Optional)</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              disabled={loading}
            />
          </div>

          {/* Platform Badge */}
          {selectedPlatform && (
            <div className="flex justify-center">
              <Badge className={getPlatformColor(selectedPlatform.value)}>
                {getPlatformIcon(selectedPlatform.value)} {selectedPlatform.label} Account
              </Badge>
            </div>
          )}

          {/* Submit Button */}
          <Button
            type="submit"
            className="w-full"
            disabled={loading || !formData.platform}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating Account...
              </>
            ) : (
              <>
                <Plus className="mr-2 h-4 w-4" />
                Create & Authenticate Account
              </>
            )}
          </Button>
        </form>

        {/* Info */}
        <div className="text-sm text-muted-foreground text-center">
          <p>
            Your credentials are encrypted and stored securely. 
            Sessions persist to avoid re-authentication.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
