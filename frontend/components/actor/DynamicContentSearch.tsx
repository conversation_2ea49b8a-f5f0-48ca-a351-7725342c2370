'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { 
    Search, 
    Play, 
    Heart, 
    MessageCircle, 
    Share, 
    ExternalLink, 
    RefreshCw, 
    TrendingUp,
    Users,
    Video,
    BarChart3,
    Filter,
    Clock,
    CheckCircle,
    AlertCircle,
    Plus,
    X
} from 'lucide-react';
import { searchContentByKeywords } from '@/lib/api/actor-system';

interface SearchVideo {
    video_id: string;
    url: string;
    author: string;
    description?: string;
    metrics: {
        likes?: string;
        comments?: string;
        shares?: string;
    };
    extracted_at: string;
    keyword: string;
    category?: string;
}

interface SearchPreset {
    id: string;
    name: string;
    keywords: string[];
    filters: {
        min_likes?: number;
        content_type?: string;
        date_range?: string;
    };
}

interface DynamicContentSearchProps {
    onSearchComplete?: (results: any) => void;
}

export function DynamicContentSearch({
    onSearchComplete
}: DynamicContentSearchProps) {
    const [searchQuery, setSearchQuery] = useState('');
    const [keywords, setKeywords] = useState<string[]>([]);
    const [videos, setVideos] = useState<SearchVideo[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [searchHistory, setSearchHistory] = useState<string[]>([]);
    const [searchPresets, setSearchPresets] = useState<SearchPreset[]>([]);
    const [activeFilters, setActiveFilters] = useState({
        min_likes: '',
        content_type: 'all',
        date_range: 'all',
        author_type: 'all'
    });
    const [stats, setStats] = useState({
        totalVideos: 0,
        totalLikes: 0,
        totalComments: 0,
        totalShares: 0,
        uniqueAuthors: <AUTHORS>
        topKeywords: [] as string[]
    });

    // Predefined search presets
    const defaultPresets: SearchPreset[] = [
        {
            id: 'indonesian-politics',
            name: 'Indonesian Politics',
            keywords: ['politik indonesia', 'pemilu', 'pilpres', 'demokrasi'],
            filters: { content_type: 'news', min_likes: 1000 }
        },
        {
            id: 'trending-indonesia',
            name: 'Trending Indonesia',
            keywords: ['viral indonesia', 'trending', 'berita terkini'],
            filters: { date_range: '7d' }
        },
        {
            id: 'news-outlets',
            name: 'Indonesian News',
            keywords: ['bbc indonesia', 'kompas', 'detik', 'tvri', 'republika'],
            filters: { content_type: 'news' }
        }
    ];

    useEffect(() => {
        setSearchPresets(defaultPresets);
        loadSearchHistory();
    }, []);

    const loadSearchHistory = async () => {
        try {
            const history = await getSearchHistory();
            setSearchHistory(history.slice(0, 10)); // Keep last 10 searches
        } catch (err) {
            console.error('Failed to load search history:', err);
        }
    };

    const addKeyword = () => {
        if (searchQuery.trim() && !keywords.includes(searchQuery.trim())) {
            setKeywords([...keywords, searchQuery.trim()]);
            setSearchQuery('');
        }
    };

    const removeKeyword = (keyword: string) => {
        setKeywords(keywords.filter(k => k !== keyword));
    };

    const applyPreset = (preset: SearchPreset) => {
        setKeywords(preset.keywords);
        setActiveFilters({
            ...activeFilters,
            ...preset.filters,
            min_likes: preset.filters.min_likes?.toString() || '',
            content_type: preset.filters.content_type || 'all',
            date_range: preset.filters.date_range || 'all'
        });
    };

    const performSearch = async () => {
        if (keywords.length === 0) {
            setError('Please add at least one keyword to search');
            return;
        }

        setLoading(true);
        setError(null);

        try {
            const allVideos: SearchVideo[] = [];
            
            // Search for each keyword
            for (const keyword of keywords) {
                const result = await scrapeContentByKeyword({
                    keywords: keyword.trim(),
                    max_videos: 10,
                    filters: activeFilters
                });

                if (result.success) {
                    const keywordVideos = result.videos.map((video: any) => ({
                        ...video,
                        keyword,
                        category: categorizeContent(video.description, video.author)
                    }));
                    allVideos.push(...keywordVideos);
                }
            }

            // Remove duplicates based on video_id
            const uniqueVideos = allVideos.filter((video, index, self) => 
                index === self.findIndex(v => v.video_id === video.video_id)
            );

            setVideos(uniqueVideos);
            calculateStats(uniqueVideos);
            
            // Update search history
            const newHistory = [...new Set([keywords.join(', '), ...searchHistory])];
            setSearchHistory(newHistory.slice(0, 10));

            if (onSearchComplete) {
                onSearchComplete({
                    videos: uniqueVideos,
                    keywords,
                    stats: calculateStatsSync(uniqueVideos)
                });
            }

        } catch (err: any) {
            console.error('Search failed:', err);
            setError(err.response?.data?.error || err.message || 'Search failed');
        } finally {
            setLoading(false);
        }
    };

    const categorizeContent = (description: string, author: string): string => {
        const desc = (description || '').toLowerCase();
        const auth = (author || '').toLowerCase();

        if (auth.includes('bbc') || auth.includes('tvri') || auth.includes('kompas') || 
            auth.includes('detik') || auth.includes('republika')) {
            return 'news';
        }
        
        if (auth.includes('official') || auth.includes('verified')) {
            return 'official';
        }

        if (desc.includes('politik') || desc.includes('pemilu') || desc.includes('pilpres')) {
            return 'politics';
        }

        if (desc.includes('viral') || desc.includes('trending')) {
            return 'viral';
        }

        return 'general';
    };

    const parseMetric = (metric: string): number => {
        if (!metric) return 0;
        const cleanMetric = metric.replace(/[^\d.KMB]/g, '');
        if (cleanMetric.includes('K')) {
            return Math.floor(parseFloat(cleanMetric.replace('K', '')) * 1000);
        } else if (cleanMetric.includes('M')) {
            return Math.floor(parseFloat(cleanMetric.replace('M', '')) * 1000000);
        } else if (cleanMetric.includes('B')) {
            return Math.floor(parseFloat(cleanMetric.replace('B', '')) * 1000000000);
        }
        return parseInt(cleanMetric) || 0;
    };

    const calculateStats = (videoList: SearchVideo[]) => {
        const totalLikes = videoList.reduce((sum, video) => sum + parseMetric(video.metrics.likes || '0'), 0);
        const totalComments = videoList.reduce((sum, video) => sum + parseMetric(video.metrics.comments || '0'), 0);
        const totalShares = videoList.reduce((sum, video) => sum + parseMetric(video.metrics.shares || '0'), 0);
        const uniqueAuthors = new Set(videoList.map(video => video.author)).size;
        
        // Get top keywords by frequency
        const keywordFreq = videoList.reduce((acc, video) => {
            acc[video.keyword] = (acc[video.keyword] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);
        
        const topKeywords = Object.entries(keywordFreq)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([keyword]) => keyword);

        setStats({
            totalVideos: videoList.length,
            totalLikes,
            totalComments,
            totalShares,
            uniqueAuthors,
            topKeywords
        });
    };

    const calculateStatsSync = (videoList: SearchVideo[]) => {
        const totalLikes = videoList.reduce((sum, video) => sum + parseMetric(video.metrics.likes || '0'), 0);
        const totalComments = videoList.reduce((sum, video) => sum + parseMetric(video.metrics.comments || '0'), 0);
        const totalShares = videoList.reduce((sum, video) => sum + parseMetric(video.metrics.shares || '0'), 0);
        const uniqueAuthors = new Set(videoList.map(video => video.author)).size;

        return {
            totalVideos: videoList.length,
            totalLikes,
            totalComments,
            totalShares,
            uniqueAuthors
        };
    };

    const formatNumber = (num: number): string => {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    };

    const getCategoryBadgeVariant = (category: string) => {
        switch (category) {
            case 'news': return 'default';
            case 'official': return 'secondary';
            case 'politics': return 'destructive';
            case 'viral': return 'outline';
            default: return 'outline';
        }
    };

    const getCategoryIcon = (category: string) => {
        switch (category) {
            case 'news': return '📰';
            case 'official': return '✅';
            case 'politics': return '🏛️';
            case 'viral': return '🔥';
            default: return '📱';
        }
    };

    return (
        <div className="space-y-6">
            {/* Search Interface */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Search className="h-5 w-5" />
                        Legacy Content Search
                    </CardTitle>
                    <CardDescription>
                        Search for content using the legacy TikTok-specific search system
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* Keyword Input */}
                    <div className="flex gap-2">
                        <div className="flex-1">
                            <Label htmlFor="search-query">Add Keywords</Label>
                            <Input
                                id="search-query"
                                placeholder="Enter keyword (e.g., viral indonesia, trending, teknologi)"
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                onKeyPress={(e) => e.key === 'Enter' && addKeyword()}
                            />
                        </div>
                        <Button 
                            onClick={addKeyword} 
                            disabled={!searchQuery.trim()}
                            className="mt-6"
                        >
                            <Plus className="h-4 w-4" />
                        </Button>
                    </div>

                    {/* Active Keywords */}
                    <div>
                        <Label>Active Keywords ({keywords.length})</Label>
                        <div className="flex flex-wrap gap-2 mt-2">
                            {keywords.map((keyword, index) => (
                                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                                    {keyword}
                                    <X 
                                        className="h-3 w-3 cursor-pointer" 
                                        onClick={() => removeKeyword(keyword)}
                                    />
                                </Badge>
                            ))}
                        </div>
                    </div>

                    {/* Search Presets */}
                    <div>
                        <Label>Quick Presets</Label>
                        <div className="flex flex-wrap gap-2 mt-2">
                            {searchPresets.map((preset) => (
                                <Button
                                    key={preset.id}
                                    variant="outline"
                                    size="sm"
                                    onClick={() => applyPreset(preset)}
                                >
                                    {preset.name}
                                </Button>
                            ))}
                        </div>
                    </div>

                    {/* Filters */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <Label htmlFor="min-likes">Min Likes</Label>
                            <Input
                                id="min-likes"
                                type="number"
                                placeholder="1000"
                                value={activeFilters.min_likes}
                                onChange={(e) => setActiveFilters({
                                    ...activeFilters,
                                    min_likes: e.target.value
                                })}
                            />
                        </div>
                        <div>
                            <Label htmlFor="content-type">Content Type</Label>
                            <Select 
                                value={activeFilters.content_type} 
                                onValueChange={(value) => setActiveFilters({
                                    ...activeFilters,
                                    content_type: value
                                })}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Content</SelectItem>
                                    <SelectItem value="news">News</SelectItem>
                                    <SelectItem value="official">Official</SelectItem>
                                    <SelectItem value="viral">Viral</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div>
                            <Label htmlFor="date-range">Date Range</Label>
                            <Select 
                                value={activeFilters.date_range} 
                                onValueChange={(value) => setActiveFilters({
                                    ...activeFilters,
                                    date_range: value
                                })}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Time</SelectItem>
                                    <SelectItem value="1d">Last 24 Hours</SelectItem>
                                    <SelectItem value="7d">Last 7 Days</SelectItem>
                                    <SelectItem value="30d">Last 30 Days</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div>
                            <Label htmlFor="author-type">Author Type</Label>
                            <Select 
                                value={activeFilters.author_type} 
                                onValueChange={(value) => setActiveFilters({
                                    ...activeFilters,
                                    author_type: value
                                })}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Authors</SelectItem>
                                    <SelectItem value="verified">Verified Only</SelectItem>
                                    <SelectItem value="news">News Outlets</SelectItem>
                                    <SelectItem value="creators">Content Creators</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {/* Search Button */}
                    <Button 
                        onClick={performSearch} 
                        disabled={loading || keywords.length === 0}
                        className="w-full"
                        size="lg"
                    >
                        {loading ? (
                            <>
                                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                Searching {keywords.length} keywords...
                            </>
                        ) : (
                            <>
                                <Search className="mr-2 h-4 w-4" />
                                Search {keywords.length} Keywords
                            </>
                        )}
                    </Button>

                    {/* Error Display */}
                    {error && (
                        <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>{error}</AlertDescription>
                        </Alert>
                    )}
                </CardContent>
            </Card>

            {/* Search History */}
            {searchHistory.length > 0 && (
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Clock className="h-5 w-5" />
                            Recent Searches
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-wrap gap-2">
                            {searchHistory.map((search, index) => (
                                <Badge 
                                    key={index} 
                                    variant="outline" 
                                    className="cursor-pointer"
                                    onClick={() => {
                                        const searchKeywords = search.split(', ');
                                        setKeywords(searchKeywords);
                                    }}
                                >
                                    {search}
                                </Badge>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Results will be rendered by parent component */}
        </div>
    );
}
