'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
    Wifi, 
    WifiOff, 
    RefreshCw, 
    Shield, 
    Clock, 
    CheckCircle, 
    AlertCircle, 
    XCircle,
    Activity,
    Globe,
    User,
    Settings,
    Trash2,
    Plus,
    Eye
} from 'lucide-react';
// Mock functions for now - replace with actual API calls when available
const getSessions = async () => [];
const createEnhancedSession = async (data: any) => ({ success: true });
const updateSession = async (id: string, data: any) => ({ success: true });
const deleteSession = async (id: string) => ({ success: true });
const testSession = async (id: string) => ({ success: true, valid: true });

interface Session {
    id: string;
    account_username: string;
    status: string; // Changed from union type to string to be more flexible
    health_score: number;
    created_at: string;
    last_used: string;
    expires_at?: string;
    cookies_count: number;
    user_agent: string;
    proxy_info?: {
        ip: string;
        location: string;
        provider: string;
    };
    usage_stats: {
        requests_made: number;
        success_rate: number;
        last_error?: string;
    };
    metadata: {
        login_method: string;
        browser_version: string;
        platform: string;
    };
}

interface SessionManagerProps {
    username: string;
    password: string;
    onSessionChange?: (sessions: Session[]) => void;
}

export function SessionManager({ username, password, onSessionChange }: SessionManagerProps) {
    const [sessions, setSessions] = useState<Session[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [testingSession, setTestingSession] = useState<string | null>(null);
    const [selectedSession, setSelectedSession] = useState<Session | null>(null);

    useEffect(() => {
        loadSessions();
        
        // Set up polling for session updates
        const interval = setInterval(() => {
            loadSessions();
        }, 30000); // Poll every 30 seconds

        return () => clearInterval(interval);
    }, []);

    const loadSessions = async () => {
        try {
            const sessionsData = await getSessions();
            setSessions(sessionsData as Session[]);

            if (onSessionChange) {
                onSessionChange(sessionsData as Session[]);
            }
        } catch (err: any) {
            console.error('Failed to load sessions:', err);
            setError('Failed to load sessions');
        }
    };

    const createNewSession = async () => {
        setLoading(true);
        setError(null);

        try {
            const sessionData = {
                account_username: username,
                login_method: 'simple_login'
            };

            const newSession = await createEnhancedSession(sessionData);
            setSessions([newSession, ...sessions]);
            
            return newSession;
        } catch (err: any) {
            console.error('Failed to create session:', err);
            setError(err.response?.data?.error || 'Failed to create session');
            return null;
        } finally {
            setLoading(false);
        }
    };

    const testSessionHealth = async (sessionId: string) => {
        setTestingSession(sessionId);
        
        try {
            const result = await testSession(sessionId);
            
            // Update session status based on test result
            const updatedSessions = sessions.map(session => 
                session.id === sessionId 
                    ? { 
                        ...session, 
                        status: result.success ? 'active' : 'invalid',
                        health_score: result.health_score || 0,
                        usage_stats: {
                            ...session.usage_stats,
                            last_error: result.error
                        }
                    }
                    : session
            );
            
            setSessions(updatedSessions);
            
            if (onSessionChange) {
                onSessionChange(updatedSessions);
            }
            
        } catch (err: any) {
            console.error('Failed to test session:', err);
            setError('Failed to test session');
        } finally {
            setTestingSession(null);
        }
    };

    const refreshSession = async (sessionId: string) => {
        try {
            const refreshedSession = await updateSession(parseInt(sessionId), { action: 'refresh' });
            
            const updatedSessions = sessions.map(session => 
                session.id === sessionId ? refreshedSession : session
            );
            
            setSessions(updatedSessions);
            
            if (onSessionChange) {
                onSessionChange(updatedSessions);
            }
            
        } catch (err: any) {
            console.error('Failed to refresh session:', err);
            setError('Failed to refresh session');
        }
    };

    const deleteSessionHandler = async (sessionId: string) => {
        try {
            await deleteSession(parseInt(sessionId));
            const updatedSessions = sessions.filter(session => session.id !== sessionId);
            setSessions(updatedSessions);
            
            if (onSessionChange) {
                onSessionChange(updatedSessions);
            }
            
        } catch (err: any) {
            console.error('Failed to delete session:', err);
            setError('Failed to delete session');
        }
    };

    const getStatusIcon = (status: string, healthScore: number) => {
        switch (status) {
            case 'active':
                return healthScore > 80 ? 
                    <Wifi className="h-4 w-4 text-green-500" /> : 
                    <Wifi className="h-4 w-4 text-yellow-500" />;
            case 'expired':
                return <Clock className="h-4 w-4 text-orange-500" />;
            case 'invalid':
                return <WifiOff className="h-4 w-4 text-red-500" />;
            case 'testing':
                return <Activity className="h-4 w-4 text-blue-500 animate-pulse" />;
            default:
                return <WifiOff className="h-4 w-4 text-gray-500" />;
        }
    };

    const getStatusBadgeVariant = (status: string) => {
        switch (status) {
            case 'active':
                return 'default';
            case 'expired':
                return 'secondary';
            case 'invalid':
                return 'destructive';
            case 'testing':
                return 'outline';
            default:
                return 'outline';
        }
    };

    const getHealthColor = (score: number) => {
        if (score >= 80) return 'text-green-600';
        if (score >= 60) return 'text-yellow-600';
        if (score >= 40) return 'text-orange-600';
        return 'text-red-600';
    };

    const formatTimeAgo = (dateString: string): string => {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffHours < 24) return `${diffHours}h ago`;
        return `${diffDays}d ago`;
    };

    const activeSessions = sessions.filter(s => s.status === 'active');
    const expiredSessions = sessions.filter(s => s.status === 'expired');
    const invalidSessions = sessions.filter(s => s.status === 'invalid');

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">Session Management</h2>
                    <p className="text-muted-foreground">
                        Manage TikTok login sessions for {username}
                    </p>
                </div>
                <div className="flex gap-2">
                    <Button 
                        variant="outline" 
                        onClick={loadSessions}
                        disabled={loading}
                    >
                        <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                        Refresh
                    </Button>
                    <Button 
                        onClick={createNewSession}
                        disabled={loading}
                    >
                        <Plus className="h-4 w-4 mr-2" />
                        New Session
                    </Button>
                </div>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <Wifi className="h-8 w-8 text-green-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-muted-foreground">Active Sessions</p>
                                <p className="text-2xl font-bold">{activeSessions.length}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <Clock className="h-8 w-8 text-orange-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-muted-foreground">Expired</p>
                                <p className="text-2xl font-bold">{expiredSessions.length}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <WifiOff className="h-8 w-8 text-red-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-muted-foreground">Invalid</p>
                                <p className="text-2xl font-bold">{invalidSessions.length}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <Shield className="h-8 w-8 text-blue-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-muted-foreground">Avg Health</p>
                                <p className="text-2xl font-bold">
                                    {activeSessions.length > 0 
                                        ? Math.round(activeSessions.reduce((sum, s) => sum + s.health_score, 0) / activeSessions.length)
                                        : 0
                                    }%
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Error Display */}
            {error && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {/* Sessions List */}
            <div className="space-y-4">
                {sessions.length === 0 ? (
                    <Card>
                        <CardContent className="p-8 text-center">
                            <WifiOff className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                            <p className="text-muted-foreground">No sessions found</p>
                            <Button 
                                className="mt-4" 
                                onClick={createNewSession}
                                disabled={loading}
                            >
                                Create First Session
                            </Button>
                        </CardContent>
                    </Card>
                ) : (
                    sessions.map((session) => (
                        <Card key={session.id}>
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        {getStatusIcon(session.status, session.health_score)}
                                        <CardTitle className="text-lg">
                                            {session.account_username}
                                        </CardTitle>
                                        <Badge variant={getStatusBadgeVariant(session.status)}>
                                            {session.status}
                                        </Badge>
                                        <div className="flex items-center gap-1">
                                            <Shield className="h-3 w-3" />
                                            <span className={`text-sm font-medium ${getHealthColor(session.health_score)}`}>
                                                {session.health_score}%
                                            </span>
                                        </div>
                                    </div>
                                    <div className="flex gap-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => testSessionHealth(session.id)}
                                            disabled={testingSession === session.id}
                                        >
                                            {testingSession === session.id ? (
                                                <RefreshCw className="h-4 w-4 animate-spin" />
                                            ) : (
                                                <Activity className="h-4 w-4" />
                                            )}
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => refreshSession(session.id)}
                                        >
                                            <RefreshCw className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => setSelectedSession(session)}
                                        >
                                            <Eye className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => deleteSessionHandler(session.id)}
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                                <CardDescription>
                                    Created: {formatTimeAgo(session.created_at)} • 
                                    Last used: {formatTimeAgo(session.last_used)} • 
                                    {session.cookies_count} cookies
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    {/* Health Score Progress */}
                                    <div>
                                        <div className="flex justify-between text-sm mb-1">
                                            <span>Health Score</span>
                                            <span className={getHealthColor(session.health_score)}>
                                                {session.health_score}%
                                            </span>
                                        </div>
                                        <Progress value={session.health_score} className="w-full" />
                                    </div>

                                    {/* Session Info Grid */}
                                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                        <div>
                                            <p className="text-muted-foreground">Requests</p>
                                            <p className="font-medium">{session.usage_stats.requests_made}</p>
                                        </div>
                                        <div>
                                            <p className="text-muted-foreground">Success Rate</p>
                                            <p className="font-medium">{(session.usage_stats.success_rate * 100).toFixed(1)}%</p>
                                        </div>
                                        <div>
                                            <p className="text-muted-foreground">Login Method</p>
                                            <p className="font-medium">{session.metadata.login_method}</p>
                                        </div>
                                        <div>
                                            <p className="text-muted-foreground">Platform</p>
                                            <p className="font-medium">{session.metadata.platform}</p>
                                        </div>
                                    </div>

                                    {/* Proxy Info */}
                                    {session.proxy_info && (
                                        <div className="flex items-center gap-2 text-sm">
                                            <Globe className="h-4 w-4" />
                                            <span>{session.proxy_info.location} ({session.proxy_info.ip})</span>
                                            <Badge variant="outline" className="text-xs">
                                                {session.proxy_info.provider}
                                            </Badge>
                                        </div>
                                    )}

                                    {/* Last Error */}
                                    {session.usage_stats.last_error && (
                                        <Alert variant="destructive">
                                            <AlertCircle className="h-4 w-4" />
                                            <AlertDescription className="text-xs">
                                                Last error: {session.usage_stats.last_error}
                                            </AlertDescription>
                                        </Alert>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    ))
                )}
            </div>

            {/* Session Detail Modal */}
            {selectedSession && (
                <Card className="mt-6">
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <CardTitle>Session Details</CardTitle>
                            <Button variant="ghost" onClick={() => setSelectedSession(null)}>
                                <XCircle className="h-4 w-4" />
                            </Button>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <p className="text-sm font-medium">Session ID</p>
                                    <p className="text-sm text-muted-foreground font-mono">{selectedSession.id}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium">Account</p>
                                    <p className="text-sm text-muted-foreground">{selectedSession.account_username}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium">Status</p>
                                    <Badge variant={getStatusBadgeVariant(selectedSession.status)}>
                                        {selectedSession.status}
                                    </Badge>
                                </div>
                                <div>
                                    <p className="text-sm font-medium">Health Score</p>
                                    <p className={`text-sm font-medium ${getHealthColor(selectedSession.health_score)}`}>
                                        {selectedSession.health_score}%
                                    </p>
                                </div>
                            </div>
                            
                            <div>
                                <p className="text-sm font-medium">User Agent</p>
                                <p className="text-xs text-muted-foreground font-mono bg-muted p-2 rounded mt-1">
                                    {selectedSession.user_agent}
                                </p>
                            </div>
                            
                            <div>
                                <p className="text-sm font-medium">Metadata</p>
                                <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-auto">
                                    {JSON.stringify(selectedSession.metadata, null, 2)}
                                </pre>
                            </div>
                            
                            <div>
                                <p className="text-sm font-medium">Usage Statistics</p>
                                <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-auto">
                                    {JSON.stringify(selectedSession.usage_stats, null, 2)}
                                </pre>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
