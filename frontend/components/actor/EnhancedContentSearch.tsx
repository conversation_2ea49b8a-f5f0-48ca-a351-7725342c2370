'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Loader2, Search, Calendar, Hash, Target, Settings, Plus, X } from 'lucide-react';
import { AccountSelector } from './AccountSelector';
import { 
  searchContentWithAccount,
  type ActorAccount 
} from '@/lib/api/actor-system';

interface SearchFilters {
  startDate?: string;
  endDate?: string;
  maxItems: number;
  includeHashtags: boolean;
  includeUserMentions: boolean;
  minLikes?: number;
  minViews?: number;
}

interface EnhancedContentSearchProps {
  onSearchComplete?: (results: any) => void;
  onSearchStart?: () => void;
  onError?: (error: string) => void;
  defaultAccount?: ActorAccount;
}

export function EnhancedContentSearch({ 
  onSearchComplete, 
  onSearchStart, 
  onError,
  defaultAccount 
}: EnhancedContentSearchProps) {
  const [selectedAccount, setSelectedAccount] = useState<ActorAccount | null>(defaultAccount || null);
  const [keywords, setKeywords] = useState<string[]>(['']);
  const [filters, setFilters] = useState<SearchFilters>({
    maxItems: 50,
    includeHashtags: true,
    includeUserMentions: true
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [results, setResults] = useState<any>(null);

  const addKeyword = () => {
    setKeywords([...keywords, '']);
  };

  const removeKeyword = (index: number) => {
    if (keywords.length > 1) {
      setKeywords(keywords.filter((_, i) => i !== index));
    }
  };

  const updateKeyword = (index: number, value: string) => {
    const newKeywords = [...keywords];
    newKeywords[index] = value;
    setKeywords(newKeywords);
  };

  const updateFilter = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleSearch = async () => {
    if (!selectedAccount) {
      setError('Please select an account first');
      return;
    }

    const validKeywords = keywords.filter(k => k.trim().length > 0);
    if (validKeywords.length === 0) {
      setError('Please enter at least one keyword');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);
    setResults(null);

    if (onSearchStart) {
      onSearchStart();
    }

    try {
      const searchOptions = {
        max_items: filters.maxItems,
        start_date: filters.startDate,
        end_date: filters.endDate
      };

      const result = await searchContentWithAccount(
        selectedAccount.id,
        validKeywords,
        searchOptions
      );

      if (result.success) {
        setSuccess(`Search completed! Found ${result.items_scraped} items.`);
        setResults(result);
        
        if (onSearchComplete) {
          onSearchComplete(result);
        }
      } else {
        const errorMsg = result.error || 'Search failed';
        setError(errorMsg);
        if (onError) {
          onError(errorMsg);
        }
      }
    } catch (err: any) {
      console.error('Search error:', err);
      const errorMsg = err.response?.data?.error || err.message || 'Search failed';
      setError(errorMsg);
      if (onError) {
        onError(errorMsg);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Account Selection */}
      <AccountSelector
        selectedAccountId={selectedAccount?.id}
        onAccountSelect={setSelectedAccount}
        requireAuthentication={true}
        className="w-full"
      />

      {/* Search Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Content Search Configuration
          </CardTitle>
          <CardDescription>
            Configure your search parameters and filters
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          {/* Keywords */}
          <div className="space-y-3">
            <Label className="flex items-center gap-2">
              <Hash className="h-4 w-4" />
              Search Keywords
            </Label>
            {keywords.map((keyword, index) => (
              <div key={index} className="flex items-center gap-2">
                <Input
                  placeholder={`Keyword ${index + 1}`}
                  value={keyword}
                  onChange={(e) => updateKeyword(index, e.target.value)}
                  disabled={loading}
                />
                {keywords.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeKeyword(index)}
                    disabled={loading}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addKeyword}
              disabled={loading}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Keyword
            </Button>
          </div>

          <Separator />

          {/* Date Range */}
          <div className="space-y-3">
            <Label className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Date Range (Optional)
            </Label>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate" className="text-sm">Start Date</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={filters.startDate || ''}
                  onChange={(e) => updateFilter('startDate', e.target.value)}
                  disabled={loading}
                />
              </div>
              <div>
                <Label htmlFor="endDate" className="text-sm">End Date</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={filters.endDate || ''}
                  onChange={(e) => updateFilter('endDate', e.target.value)}
                  disabled={loading}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Search Limits */}
          <div className="space-y-3">
            <Label className="flex items-center gap-2">
              <Target className="h-4 w-4" />
              Search Limits
            </Label>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="maxItems" className="text-sm">Max Items</Label>
                <Input
                  id="maxItems"
                  type="number"
                  min="1"
                  max="1000"
                  value={filters.maxItems}
                  onChange={(e) => updateFilter('maxItems', parseInt(e.target.value) || 50)}
                  disabled={loading}
                />
              </div>
              <div>
                <Label htmlFor="minLikes" className="text-sm">Min Likes (Optional)</Label>
                <Input
                  id="minLikes"
                  type="number"
                  min="0"
                  value={filters.minLikes || ''}
                  onChange={(e) => updateFilter('minLikes', e.target.value ? parseInt(e.target.value) : undefined)}
                  disabled={loading}
                />
              </div>
            </div>
          </div>

          {/* Search Button */}
          <Button
            onClick={handleSearch}
            disabled={loading || !selectedAccount || keywords.every(k => !k.trim())}
            className="w-full"
            size="lg"
          >
            {loading ? (
              <>
                <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                Searching...
              </>
            ) : (
              <>
                <Search className="h-5 w-5 mr-2" />
                Start Content Search
              </>
            )}
          </Button>

          {/* Search Summary */}
          {selectedAccount && keywords.some(k => k.trim()) && (
            <div className="p-4 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">Search Summary</h4>
              <div className="space-y-1 text-sm">
                <p><strong>Account:</strong> @{selectedAccount.username} ({selectedAccount.platform})</p>
                <p><strong>Keywords:</strong> {keywords.filter(k => k.trim()).join(', ')}</p>
                <p><strong>Max Items:</strong> {filters.maxItems}</p>
                {filters.startDate && <p><strong>Start Date:</strong> {filters.startDate}</p>}
                {filters.endDate && <p><strong>End Date:</strong> {filters.endDate}</p>}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Results Summary */}
      {results && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Search Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{results.items_scraped}</div>
                <div className="text-sm text-muted-foreground">Items Found</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{results.task_id}</div>
                <div className="text-sm text-muted-foreground">Task ID</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
