'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Loader2, RefreshCw, AlertCircle, CheckCircle, User } from 'lucide-react';
import { 
  getActorAccounts, 
  authenticateActorAccount,
  getPlatformIcon,
  getPlatformColor,
  type ActorAccount 
} from '@/lib/api/actor-system';

interface AccountSelectorProps {
  selectedAccountId?: number;
  onAccountSelect?: (account: ActorAccount) => void;
  onAccountAuthenticated?: (account: ActorAccount, result: any) => void;
  platform?: string; // Filter by platform
  requireAuthentication?: boolean; // Whether to auto-authenticate selected account
  className?: string;
}

export function AccountSelector({ 
  selectedAccountId, 
  onAccountSelect, 
  onAccountAuthenticated,
  platform,
  requireAuthentication = false,
  className = ""
}: AccountSelectorProps) {
  const [accounts, setAccounts] = useState<ActorAccount[]>([]);
  const [loading, setLoading] = useState(false);
  const [authenticating, setAuthenticating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    loadAccounts();
  }, [platform]);

  const loadAccounts = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const userAccounts = await getActorAccounts(platform);
      setAccounts(userAccounts);
      
      // If there's only one account and no selection, auto-select it
      if (userAccounts.length === 1 && !selectedAccountId && onAccountSelect) {
        onAccountSelect(userAccounts[0]);
      }
    } catch (err: any) {
      console.error('Error loading accounts:', err);
      setError('Failed to load accounts');
    } finally {
      setLoading(false);
    }
  };

  const handleAccountSelect = async (accountId: string) => {
    const account = accounts.find(a => a.id === parseInt(accountId));
    if (!account) return;

    setError(null);
    setSuccess(null);

    if (onAccountSelect) {
      onAccountSelect(account);
    }

    // Auto-authenticate if required and session is not valid
    if (requireAuthentication && !account.session_valid) {
      await authenticateAccount(account);
    }
  };

  const authenticateAccount = async (account: ActorAccount) => {
    setAuthenticating(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await authenticateActorAccount(account.id);
      
      if (result.success) {
        setSuccess(`Successfully authenticated ${account.platform} account @${account.username}`);
        
        // Update account session status
        const updatedAccount = { ...account, session_valid: true };
        setAccounts(prev => prev.map(a => a.id === account.id ? updatedAccount : a));
        
        if (onAccountAuthenticated) {
          onAccountAuthenticated(updatedAccount, result);
        }
      } else {
        setError(`Authentication failed: ${result.error}`);
      }
    } catch (err: any) {
      console.error('Authentication error:', err);
      setError(`Authentication failed: ${err.message}`);
    } finally {
      setAuthenticating(false);
    }
  };

  const selectedAccount = accounts.find(a => a.id === selectedAccountId);

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-6">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Loading accounts...</span>
        </CardContent>
      </Card>
    );
  }

  if (accounts.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            No Accounts Found
          </CardTitle>
          <CardDescription>
            {platform 
              ? `No ${platform} accounts found. Please create an account first.`
              : 'No accounts found. Please create an account first.'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={loadAccounts} variant="outline" className="w-full">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Select Account
        </CardTitle>
        <CardDescription>
          Choose an account to use for this task
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        {/* Account Selection */}
        <div className="space-y-2">
          <Select
            value={selectedAccountId?.toString() || ''}
            onValueChange={handleAccountSelect}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select an account">
                {selectedAccount && (
                  <div className="flex items-center gap-2">
                    <span>{getPlatformIcon(selectedAccount.platform)}</span>
                    <span>@{selectedAccount.username}</span>
                    <Badge 
                      variant={selectedAccount.session_valid ? "default" : "secondary"}
                      className="ml-auto"
                    >
                      {selectedAccount.session_valid ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                )}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {accounts.map((account) => (
                <SelectItem key={account.id} value={account.id.toString()}>
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                      <span>{getPlatformIcon(account.platform)}</span>
                      <div className="flex flex-col">
                        <span>@{account.username}</span>
                        <span className="text-xs text-muted-foreground">
                          {account.platform.charAt(0).toUpperCase() + account.platform.slice(1)}
                        </span>
                      </div>
                    </div>
                    <Badge 
                      variant={account.session_valid ? "default" : "secondary"}
                      className="ml-2"
                    >
                      {account.session_valid ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Account Details */}
        {selectedAccount && (
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-full ${getPlatformColor(selectedAccount.platform)}`}>
                  <span className="text-lg">{getPlatformIcon(selectedAccount.platform)}</span>
                </div>
                <div>
                  <p className="font-medium">@{selectedAccount.username}</p>
                  <p className="text-sm text-muted-foreground">
                    {selectedAccount.platform.charAt(0).toUpperCase() + selectedAccount.platform.slice(1)}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <Badge 
                  variant={selectedAccount.session_valid ? "default" : "secondary"}
                >
                  {selectedAccount.session_valid ? "Session Active" : "Session Expired"}
                </Badge>
                {selectedAccount.last_login && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Last login: {new Date(selectedAccount.last_login).toLocaleDateString()}
                  </p>
                )}
              </div>
            </div>

            {/* Authentication Button */}
            {!selectedAccount.session_valid && (
              <Button
                onClick={() => authenticateAccount(selectedAccount)}
                disabled={authenticating}
                className="w-full"
                variant="outline"
              >
                {authenticating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Authenticating...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Authenticate Account
                  </>
                )}
              </Button>
            )}
          </div>
        )}

        {/* Refresh Button */}
        <Button onClick={loadAccounts} variant="ghost" size="sm" className="w-full">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh Accounts
        </Button>
      </CardContent>
    </Card>
  );
}
