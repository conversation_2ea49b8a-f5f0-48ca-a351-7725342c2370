'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { 
    Plus, 
    Edit, 
    Trash2, 
    Copy, 
    Play, 
    Pause, 
    Square, 
    RefreshCw,
    Search,
    Filter,
    MoreHorizontal
} from "lucide-react";
import {
    getTasks,
    createTask,
    updateTask,
    deleteTask,
    duplicateTask,
    bulkUpdateTasks,
    bulkDeleteTasks,
    getTaskStatistics,
    getTaskStatusColor,
    getTaskTypeDisplayName,
    formatTaskDuration,
    calculateTaskProgress,
    validateTaskData,
    type Task,
    type TaskCreateData,
    type TaskUpdateData,
    type TaskStatistics
} from "@/lib/api/task-crud";

interface TaskCrudManagerProps {
    className?: string;
}

export function TaskCrudManager({ className }: TaskCrudManagerProps) {
    // State management
    const [tasks, setTasks] = useState<Task[]>([]);
    const [statistics, setStatistics] = useState<TaskStatistics | null>(null);
    const [loading, setLoading] = useState(false);
    const [selectedTasks, setSelectedTasks] = useState<number[]>([]);
    const [showCreateForm, setShowCreateForm] = useState(false);
    const [editingTask, setEditingTask] = useState<Task | null>(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState<string>('');
    const [typeFilter, setTypeFilter] = useState<string>('');

    // Form state
    const [formData, setFormData] = useState<TaskCreateData>({
        task_name: '',
        task_type: 'CONTENT_SEARCH',
        keywords: '',
        max_items: 50,
        use_stealth_mode: true,
        randomize_delays: true,
        scrape_interval: 60
    });

    // Load data on component mount
    useEffect(() => {
        loadTasks();
        loadStatistics();
    }, []);

    // ================================
    // DATA LOADING
    // ================================

    const loadTasks = async () => {
        try {
            setLoading(true);
            const response = await getTasks({
                search: searchTerm || undefined,
                status: statusFilter || undefined,
                task_type: typeFilter || undefined,
                limit: 100
            });
            setTasks(response.results);
        } catch (error) {
            console.error('Error loading tasks:', error);
        } finally {
            setLoading(false);
        }
    };

    const loadStatistics = async () => {
        try {
            const response = await getTaskStatistics();
            setStatistics(response.statistics);
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    };

    // ================================
    // CRUD OPERATIONS
    // ================================

    const handleCreateTask = async (e: React.FormEvent) => {
        e.preventDefault();
        
        // Validate form data
        const errors = validateTaskData(formData);
        if (errors.length > 0) {
            alert('Validation errors:\n' + errors.join('\n'));
            return;
        }

        try {
            setLoading(true);
            await createTask(formData);
            setShowCreateForm(false);
            setFormData({
                task_name: '',
                task_type: 'CONTENT_SEARCH',
                keywords: '',
                max_items: 50,
                use_stealth_mode: true,
                randomize_delays: true,
                scrape_interval: 60
            });
            await loadTasks();
            await loadStatistics();
        } catch (error) {
            console.error('Error creating task:', error);
            alert('Failed to create task');
        } finally {
            setLoading(false);
        }
    };

    const handleUpdateTask = async (taskId: number, updates: TaskUpdateData) => {
        try {
            setLoading(true);
            await updateTask(taskId, updates);
            await loadTasks();
            await loadStatistics();
        } catch (error) {
            console.error('Error updating task:', error);
            alert('Failed to update task');
        } finally {
            setLoading(false);
        }
    };

    const handleDeleteTask = async (taskId: number) => {
        if (!confirm('Are you sure you want to delete this task?')) return;

        try {
            setLoading(true);
            await deleteTask(taskId);
            await loadTasks();
            await loadStatistics();
        } catch (error) {
            console.error('Error deleting task:', error);
            alert('Failed to delete task');
        } finally {
            setLoading(false);
        }
    };

    const handleDuplicateTask = async (taskId: number) => {
        try {
            setLoading(true);
            await duplicateTask(taskId);
            await loadTasks();
            await loadStatistics();
        } catch (error) {
            console.error('Error duplicating task:', error);
            alert('Failed to duplicate task');
        } finally {
            setLoading(false);
        }
    };

    // ================================
    // BULK OPERATIONS
    // ================================

    const handleBulkStatusUpdate = async (status: string) => {
        if (selectedTasks.length === 0) return;

        try {
            setLoading(true);
            await bulkUpdateTasks(selectedTasks, { status });
            setSelectedTasks([]);
            await loadTasks();
            await loadStatistics();
        } catch (error) {
            console.error('Error updating tasks:', error);
            alert('Failed to update tasks');
        } finally {
            setLoading(false);
        }
    };

    const handleBulkDelete = async () => {
        if (selectedTasks.length === 0) return;
        if (!confirm(`Are you sure you want to delete ${selectedTasks.length} tasks?`)) return;

        try {
            setLoading(true);
            await bulkDeleteTasks(selectedTasks);
            setSelectedTasks([]);
            await loadTasks();
            await loadStatistics();
        } catch (error) {
            console.error('Error deleting tasks:', error);
            alert('Failed to delete tasks');
        } finally {
            setLoading(false);
        }
    };

    // ================================
    // UI HELPERS
    // ================================

    const toggleTaskSelection = (taskId: number) => {
        setSelectedTasks(prev => 
            prev.includes(taskId) 
                ? prev.filter(id => id !== taskId)
                : [...prev, taskId]
        );
    };

    const selectAllTasks = () => {
        setSelectedTasks(tasks.map(task => task.id));
    };

    const clearSelection = () => {
        setSelectedTasks([]);
    };

    // Apply filters
    const filteredTasks = tasks.filter(task => {
        const matchesSearch = !searchTerm || 
            task.task_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            task.keywords?.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesStatus = !statusFilter || task.status === statusFilter;
        const matchesType = !typeFilter || task.task_type === typeFilter;
        
        return matchesSearch && matchesStatus && matchesType;
    });

    return (
        <div className={`space-y-6 ${className}`}>
            {/* Statistics Cards */}
            {statistics && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                        <CardContent className="p-4">
                            <div className="text-2xl font-bold">{statistics.total_tasks}</div>
                            <p className="text-sm text-muted-foreground">Total Tasks</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-4">
                            <div className="text-2xl font-bold">{statistics.completion_rate}%</div>
                            <p className="text-sm text-muted-foreground">Completion Rate</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-4">
                            <div className="text-2xl font-bold">{statistics.recent_tasks_7_days}</div>
                            <p className="text-sm text-muted-foreground">Recent Tasks</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-4">
                            <div className="text-2xl font-bold">{Math.round(statistics.average_items_scraped)}</div>
                            <p className="text-sm text-muted-foreground">Avg Items Scraped</p>
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Controls */}
            <Card>
                <CardHeader>
                    <div className="flex justify-between items-center">
                        <div>
                            <CardTitle>Task Management</CardTitle>
                            <CardDescription>Create, manage, and monitor your TikTok scraping tasks</CardDescription>
                        </div>
                        <div className="flex gap-2">
                            <Button onClick={() => setShowCreateForm(true)} className="flex items-center gap-2">
                                <Plus className="h-4 w-4" />
                                Create Task
                            </Button>
                            <Button variant="outline" onClick={loadTasks} disabled={loading}>
                                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                            </Button>
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    {/* Search and Filters */}
                    <div className="flex gap-4 mb-4">
                        <div className="flex-1">
                            <Input
                                placeholder="Search tasks..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full"
                            />
                        </div>
                        <Select value={statusFilter} onValueChange={setStatusFilter}>
                            <SelectTrigger className="w-40">
                                <SelectValue placeholder="Status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="">All Status</SelectItem>
                                <SelectItem value="PENDING">Pending</SelectItem>
                                <SelectItem value="RUNNING">Running</SelectItem>
                                <SelectItem value="COMPLETED">Completed</SelectItem>
                                <SelectItem value="FAILED">Failed</SelectItem>
                                <SelectItem value="CANCELLED">Cancelled</SelectItem>
                            </SelectContent>
                        </Select>
                        <Select value={typeFilter} onValueChange={setTypeFilter}>
                            <SelectTrigger className="w-48">
                                <SelectValue placeholder="Task Type" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="">All Types</SelectItem>
                                <SelectItem value="CONTENT_SEARCH">Content Search</SelectItem>
                                <SelectItem value="HASHTAG_ANALYSIS">Hashtag Analysis</SelectItem>
                                <SelectItem value="COMPETITOR_ANALYSIS">Competitor Analysis</SelectItem>
                                <SelectItem value="TARGETED_USER">Targeted User</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Bulk Actions */}
                    {selectedTasks.length > 0 && (
                        <div className="flex items-center gap-2 mb-4 p-3 bg-blue-50 rounded-lg">
                            <span className="text-sm font-medium">{selectedTasks.length} tasks selected</span>
                            <Button size="sm" variant="outline" onClick={() => handleBulkStatusUpdate('RUNNING')}>
                                <Play className="h-3 w-3 mr-1" />
                                Start
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleBulkStatusUpdate('PAUSED')}>
                                <Pause className="h-3 w-3 mr-1" />
                                Pause
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleBulkStatusUpdate('CANCELLED')}>
                                <Square className="h-3 w-3 mr-1" />
                                Cancel
                            </Button>
                            <Button size="sm" variant="destructive" onClick={handleBulkDelete}>
                                <Trash2 className="h-3 w-3 mr-1" />
                                Delete
                            </Button>
                            <Button size="sm" variant="ghost" onClick={clearSelection}>
                                Clear
                            </Button>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Task List */}
            <Card>
                <CardHeader>
                    <div className="flex justify-between items-center">
                        <CardTitle>Tasks ({filteredTasks.length})</CardTitle>
                        {tasks.length > 0 && (
                            <div className="flex gap-2">
                                <Button size="sm" variant="outline" onClick={selectAllTasks}>
                                    Select All
                                </Button>
                                <Button size="sm" variant="outline" onClick={clearSelection}>
                                    Clear All
                                </Button>
                            </div>
                        )}
                    </div>
                </CardHeader>
                <CardContent>
                    {loading ? (
                        <div className="text-center py-8">
                            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
                            <p>Loading tasks...</p>
                        </div>
                    ) : filteredTasks.length === 0 ? (
                        <div className="text-center py-8">
                            <p className="text-muted-foreground">No tasks found</p>
                            <Button onClick={() => setShowCreateForm(true)} className="mt-2">
                                Create your first task
                            </Button>
                        </div>
                    ) : (
                        <div className="space-y-3">
                            {filteredTasks.map((task) => (
                                <div key={task.id} className="border rounded-lg p-4 hover:bg-gray-50">
                                    <div className="flex items-start justify-between">
                                        <div className="flex items-start gap-3 flex-1">
                                            <Checkbox
                                                checked={selectedTasks.includes(task.id)}
                                                onCheckedChange={() => toggleTaskSelection(task.id)}
                                            />
                                            <div className="flex-1">
                                                <div className="flex items-center gap-2 mb-2">
                                                    <h3 className="font-semibold">{task.task_name}</h3>
                                                    <Badge className={getTaskStatusColor(task.status)}>
                                                        {task.status}
                                                    </Badge>
                                                    <Badge variant="outline">
                                                        {getTaskTypeDisplayName(task.task_type)}
                                                    </Badge>
                                                </div>

                                                <div className="text-sm text-muted-foreground space-y-1">
                                                    {task.keywords && (
                                                        <p><strong>Keywords:</strong> {task.keywords}</p>
                                                    )}
                                                    {task.target_identifier && (
                                                        <p><strong>Target:</strong> {task.target_identifier}</p>
                                                    )}
                                                    <p><strong>Progress:</strong> {task.items_scraped} / {task.max_items} items ({calculateTaskProgress(task)}%)</p>
                                                    <p><strong>Created:</strong> {new Date(task.created_at).toLocaleDateString()}</p>
                                                </div>

                                                {/* Progress Bar */}
                                                <div className="mt-2">
                                                    <div className="w-full bg-gray-200 rounded-full h-2">
                                                        <div
                                                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                                            style={{ width: `${calculateTaskProgress(task)}%` }}
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Action Buttons */}
                                        <div className="flex items-center gap-1 ml-4">
                                            {task.status === 'PENDING' && (
                                                <Button
                                                    size="sm"
                                                    variant="outline"
                                                    onClick={() => handleUpdateTask(task.id, { status: 'RUNNING' })}
                                                >
                                                    <Play className="h-3 w-3" />
                                                </Button>
                                            )}
                                            {task.status === 'RUNNING' && (
                                                <Button
                                                    size="sm"
                                                    variant="outline"
                                                    onClick={() => handleUpdateTask(task.id, { status: 'PAUSED' })}
                                                >
                                                    <Pause className="h-3 w-3" />
                                                </Button>
                                            )}
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => handleDuplicateTask(task.id)}
                                            >
                                                <Copy className="h-3 w-3" />
                                            </Button>
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => setEditingTask(task)}
                                            >
                                                <Edit className="h-3 w-3" />
                                            </Button>
                                            <Button
                                                size="sm"
                                                variant="destructive"
                                                onClick={() => handleDeleteTask(task.id)}
                                            >
                                                <Trash2 className="h-3 w-3" />
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Create Task Form Modal */}
            {showCreateForm && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                        <CardHeader>
                            <CardTitle>Create New Task</CardTitle>
                            <CardDescription>Configure your TikTok scraping task</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleCreateTask} className="space-y-4">
                                <div>
                                    <Label htmlFor="task_name">Task Name *</Label>
                                    <Input
                                        id="task_name"
                                        value={formData.task_name}
                                        onChange={(e) => setFormData(prev => ({ ...prev, task_name: e.target.value }))}
                                        placeholder="Enter task name"
                                        required
                                    />
                                </div>

                                <div>
                                    <Label htmlFor="task_type">Task Type *</Label>
                                    <Select
                                        value={formData.task_type}
                                        onValueChange={(value) => setFormData(prev => ({ ...prev, task_type: value }))}
                                    >
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="CONTENT_SEARCH">Content Search</SelectItem>
                                            <SelectItem value="HASHTAG_ANALYSIS">Hashtag Analysis</SelectItem>
                                            <SelectItem value="COMPETITOR_ANALYSIS">Competitor Analysis</SelectItem>
                                            <SelectItem value="TARGETED_USER">Targeted User Analysis</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <Label htmlFor="keywords">Keywords</Label>
                                    <Input
                                        id="keywords"
                                        value={formData.keywords || ''}
                                        onChange={(e) => setFormData(prev => ({ ...prev, keywords: e.target.value }))}
                                        placeholder="viral, trending, technology"
                                    />
                                </div>

                                <div>
                                    <Label htmlFor="max_items">Max Items</Label>
                                    <Input
                                        id="max_items"
                                        type="number"
                                        value={formData.max_items}
                                        onChange={(e) => setFormData(prev => ({ ...prev, max_items: parseInt(e.target.value) }))}
                                        min="1"
                                        max="10000"
                                    />
                                </div>

                                <div className="flex justify-end gap-2">
                                    <Button type="button" variant="outline" onClick={() => setShowCreateForm(false)}>
                                        Cancel
                                    </Button>
                                    <Button type="submit" disabled={loading}>
                                        {loading ? 'Creating...' : 'Create Task'}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            )}
        </div>
    );
}
