'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import {
    Plus, Play, Pause, Square, Trash2, Edit, Eye, RefreshCw,
    Clock, CheckCircle, XCircle, AlertCircle, Target, Activity,
    Search, Filter, Calendar, User, Settings, BarChart3, Shield
} from 'lucide-react';
import {
    getActorTasks,
    createActorTask,
    executeActorTask,
    updateActorTask,
    deleteActorTask,
    getActorAccounts,
    type ActorTask,
    type ActorAccount
} from '@/lib/api/actor-system';
import TaskDetailModal from './TaskDetailModal';

interface ModernTaskManagerProps {
    accounts: ActorAccount[];
    onAccountsChange?: () => void;
}

interface TaskFormData {
    name: string;
    description: string;
    account_id: number | null;
    task_type: string;
    keywords: string;
    max_items: number;
    date_from: string;
    date_to: string;
    include_metadata: boolean;
}

const TASK_TYPES = [
    { value: 'MY_VIDEOS', label: 'My Videos (Feed)', description: 'Scrape your own video feed' },
    { value: 'CONTENT_SEARCH', label: 'Content Search', description: 'Search for content by keywords' },
    { value: 'USER_PROFILE', label: 'User Profile', description: 'Scrape user profile information' },
    { value: 'HASHTAG_SEARCH', label: 'Hashtag Search', description: 'Search content by hashtags' },
    { value: 'TRENDING_CONTENT', label: 'Trending Content', description: 'Get trending content' }
];



export function ModernTaskManager({ accounts, onAccountsChange }: ModernTaskManagerProps) {
    const [tasks, setTasks] = useState<ActorTask[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('ALL');
    const [platformFilter, setPlatformFilter] = useState('ALL');
    const [showCreateDialog, setShowCreateDialog] = useState(false);
    const [editingTask, setEditingTask] = useState<ActorTask | null>(null);
    const [executingTasks, setExecutingTasks] = useState<Set<number>>(new Set());
    const [selectedTaskId, setSelectedTaskId] = useState<number | null>(null);
    const [showTaskDetail, setShowTaskDetail] = useState(false);

    const [formData, setFormData] = useState<TaskFormData>({
        name: '',
        description: '',
        account_id: null,
        task_type: 'CONTENT_SEARCH',
        keywords: '',
        max_items: 50,
        date_from: '',
        date_to: '',
        include_metadata: true
    });

    const loadTasks = async () => {
        try {
            setLoading(true);
            setError(null);
            console.log('Loading tasks...'); // Debug log
            const tasksData = await getActorTasks();
            console.log('Tasks loaded:', tasksData); // Debug log
            setTasks(tasksData || []);
        } catch (err: any) {
            console.error('Error loading tasks:', err); // Debug log
            setError(err.message || 'Failed to load tasks');
        } finally {
            setLoading(false);
        }
    };

    const resetForm = () => {
        setFormData({
            name: '',
            description: '',
            account_id: null,
            task_type: 'CONTENT_SEARCH',
            keywords: '',
            max_items: 50,
            date_from: '',
            date_to: '',
            include_metadata: true
        });
        setEditingTask(null);
        setError(null);
        setSuccess(null);
    };

    const handleCreateTask = async () => {
        if (!formData.account_id || !formData.name.trim()) {
            setError('Please fill in all required fields');
            return;
        }

        try {
            setLoading(true);
            setError(null);

            const taskData = {
                account_id: formData.account_id,
                task_type: formData.task_type,
                task_name: formData.name,
                description: formData.description,
                max_items: formData.max_items,
                keywords: formData.keywords, // Add keywords at top level
                task_parameters: {
                    keywords: formData.keywords,
                    date_from: formData.date_from,
                    date_to: formData.date_to,
                    include_metadata: formData.include_metadata
                }
            };

            console.log('Creating task with data:', taskData); // Debug log

            const result = await createActorTask(taskData);

            console.log('Task creation result:', result); // Debug log

            if (result.success) {
                setSuccess(`Task "${formData.name}" created successfully!`);
                setShowCreateDialog(false);
                resetForm();
                // Reload tasks after a short delay to ensure database is updated
                setTimeout(() => {
                    loadTasks();
                }, 500);
            } else {
                setError(result.error || 'Failed to create task');
            }
        } catch (err: any) {
            console.error('Task creation error:', err); // Debug log
            setError(err.message || 'Failed to create task');
        } finally {
            setLoading(false);
        }
    };

    const handleExecuteTask = async (taskId: number) => {
        try {
            setExecutingTasks(prev => new Set(prev).add(taskId));
            setError(null);

            const result = await executeActorTask(taskId);

            if (result.success) {
                setSuccess(`Task execution started: ${result.message}`);
                loadTasks(); // Refresh to get updated status
            } else {
                // Check if it's an authentication error
                if (result.error?.includes('Session invalid') || result.error?.includes('re-authenticate')) {
                    setError(
                        `❌ Account authentication required! Please go to the Accounts page and click the "👁️" button next to your account, then click "Re-authenticate Account" to login to the platform first.`
                    );
                } else {
                    setError(result.error || 'Failed to execute task');
                }
            }
        } catch (err: any) {
            console.error('Task execution error:', err);
            setError(err.message || 'Failed to execute task');
        } finally {
            setExecutingTasks(prev => {
                const newSet = new Set(prev);
                newSet.delete(taskId);
                return newSet;
            });
        }
    };

    const handleDeleteTask = async (taskId: number) => {
        if (!confirm('Are you sure you want to delete this task?')) return;

        try {
            setLoading(true);
            const result = await deleteActorTask(taskId);
            
            if (result.success) {
                setSuccess('Task deleted successfully!');
                loadTasks();
            } else {
                setError(result.error || 'Failed to delete task');
            }
        } catch (err: any) {
            setError(err.message || 'Failed to delete task');
        } finally {
            setLoading(false);
        }
    };

    const getFilteredTasks = () => {
        return tasks.filter(task => {
            const matchesSearch = task.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                task.keywords?.toLowerCase().includes(searchTerm.toLowerCase());
            const matchesStatus = statusFilter === 'ALL' || task.status === statusFilter;
            const matchesPlatform = platformFilter === 'ALL' || task.platform === platformFilter;
            
            return matchesSearch && matchesStatus && matchesPlatform;
        });
    };

    const getStatusColor = (status: string) => {
        switch (status?.toUpperCase()) {
            case 'PENDING':
                return 'bg-yellow-100 text-yellow-800';
            case 'RUNNING':
                return 'bg-blue-100 text-blue-800';
            case 'COMPLETED':
                return 'bg-green-100 text-green-800';
            case 'FAILED':
                return 'bg-red-100 text-red-800';
            case 'CANCELLED':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status?.toUpperCase()) {
            case 'PENDING':
                return <Clock className="w-4 h-4" />;
            case 'RUNNING':
                return <Activity className="w-4 h-4" />;
            case 'COMPLETED':
                return <CheckCircle className="w-4 h-4" />;
            case 'FAILED':
                return <XCircle className="w-4 h-4" />;
            case 'CANCELLED':
                return <Square className="w-4 h-4" />;
            default:
                return <AlertCircle className="w-4 h-4" />;
        }
    };

    const getPlatformColor = (platform: string) => {
        switch (platform?.toLowerCase()) {
            case 'tiktok':
                return 'bg-black text-white';
            case 'instagram':
                return 'bg-pink-500 text-white';
            case 'facebook':
                return 'bg-blue-600 text-white';
            case 'twitter':
                return 'bg-blue-400 text-white';
            case 'youtube':
                return 'bg-red-600 text-white';
            default:
                return 'bg-gray-500 text-white';
        }
    };

    const getTaskTypeLabel = (type: string) => {
        const taskType = TASK_TYPES.find(t => t.value === type);
        return taskType ? taskType.label : type;
    };

    useEffect(() => {
        loadTasks();
    }, []);

    useEffect(() => {
        if (success) {
            const timer = setTimeout(() => setSuccess(null), 5000);
            return () => clearTimeout(timer);
        }
    }, [success]);

    const filteredTasks = getFilteredTasks();
    const taskStats = {
        total: tasks.length,
        pending: tasks.filter(t => t.status === 'PENDING').length,
        running: tasks.filter(t => t.status === 'RUNNING').length,
        completed: tasks.filter(t => t.status === 'COMPLETED').length,
        failed: tasks.filter(t => t.status === 'FAILED').length
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">Task Management</h2>
                    <p className="text-gray-600">Create and manage your automation tasks</p>
                    <p className="text-sm text-blue-600 mt-1">
                        💡 <strong>Tip:</strong> Before executing tasks, make sure your accounts are authenticated.
                        Go to <a href="/actor/accounts" className="underline">Accounts</a> → Click 👁️ → Re-authenticate Account
                    </p>
                </div>
                <div className="flex gap-2">
                    <Button onClick={loadTasks} variant="outline" disabled={loading}>
                        <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                        {loading ? 'Loading...' : 'Refresh Tasks'}
                    </Button>
                    <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
                        <DialogTrigger asChild>
                            <Button onClick={() => { resetForm(); setShowCreateDialog(true); }}>
                                <Plus className="w-4 h-4 mr-2" />
                                Create Task
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                            <DialogHeader>
                                <DialogTitle>Create New Task</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4 max-h-96 overflow-y-auto">
                                {/* Task Name */}
                                <div>
                                    <Label htmlFor="name">Task Name *</Label>
                                    <Input
                                        id="name"
                                        value={formData.name}
                                        onChange={(e) => setFormData({...formData, name: e.target.value})}
                                        placeholder="Enter task name"
                                    />
                                </div>

                                {/* Description */}
                                <div>
                                    <Label htmlFor="description">Description</Label>
                                    <Textarea
                                        id="description"
                                        value={formData.description}
                                        onChange={(e) => setFormData({...formData, description: e.target.value})}
                                        placeholder="Enter task description"
                                        rows={2}
                                    />
                                </div>

                                {/* Account Selection */}
                                <div>
                                    <Label htmlFor="account">Account *</Label>
                                    <Select
                                        value={formData.account_id?.toString() || ''}
                                        onValueChange={(value) => setFormData({...formData, account_id: parseInt(value)})}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select account" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {accounts.map((account) => (
                                                <SelectItem key={account.id} value={account.id.toString()}>
                                                    <div className="flex items-center gap-2">
                                                        <Badge className={getPlatformColor(account.platform)}>
                                                            {account.platform}
                                                        </Badge>
                                                        @{account.username}
                                                    </div>
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Task Type */}
                                <div>
                                    <Label htmlFor="task_type">Task Type *</Label>
                                    <Select
                                        value={formData.task_type}
                                        onValueChange={(value) => setFormData({...formData, task_type: value})}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select task type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {TASK_TYPES.map((type) => (
                                                <SelectItem key={type.value} value={type.value}>
                                                    <div>
                                                        <div className="font-medium">{type.label}</div>
                                                        <div className="text-sm text-gray-500">{type.description}</div>
                                                    </div>
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Keywords (for search tasks) */}
                                {(formData.task_type === 'CONTENT_SEARCH' || formData.task_type === 'HASHTAG_SEARCH') && (
                                    <div>
                                        <Label htmlFor="keywords">Keywords</Label>
                                        <Input
                                            id="keywords"
                                            value={formData.keywords}
                                            onChange={(e) => setFormData({...formData, keywords: e.target.value})}
                                            placeholder="Enter keywords separated by commas"
                                        />
                                    </div>
                                )}

                                {/* Max Items */}
                                <div>
                                    <Label htmlFor="max_items">Max Items</Label>
                                    <Input
                                        id="max_items"
                                        type="number"
                                        value={formData.max_items}
                                        onChange={(e) => setFormData({...formData, max_items: parseInt(e.target.value) || 50})}
                                        min="1"
                                        max="1000"
                                    />
                                </div>

                                {/* Quality Filter */}


                                {error && (
                                    <Alert variant="destructive">
                                        <AlertCircle className="h-4 w-4" />
                                        <AlertDescription>{error}</AlertDescription>
                                    </Alert>
                                )}

                                <div className="flex justify-end gap-2 pt-4">
                                    <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                                        Cancel
                                    </Button>
                                    <Button onClick={handleCreateTask} disabled={loading}>
                                        {loading ? 'Creating...' : 'Create Task'}
                                    </Button>
                                </div>
                            </div>
                        </DialogContent>
                    </Dialog>
                </div>
            </div>

            {/* Success Message */}
            {success && (
                <Alert className="border-green-200 bg-green-50">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-green-800">
                        <strong>{success}</strong>
                        <br />
                        <small>The task has been created and will appear in the list below.</small>
                    </AlertDescription>
                </Alert>
            )}

            {/* Error Message */}
            {error && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                <Card className="p-4">
                    <div className="flex items-center gap-2">
                        <Target className="h-5 w-5 text-blue-600" />
                        <div>
                            <p className="text-sm text-gray-600">Total</p>
                            <p className="text-2xl font-bold">{taskStats.total}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-2">
                        <Clock className="h-5 w-5 text-yellow-600" />
                        <div>
                            <p className="text-sm text-gray-600">Pending</p>
                            <p className="text-2xl font-bold">{taskStats.pending}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-2">
                        <Activity className="h-5 w-5 text-blue-600" />
                        <div>
                            <p className="text-sm text-gray-600">Running</p>
                            <p className="text-2xl font-bold">{taskStats.running}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <div>
                            <p className="text-sm text-gray-600">Completed</p>
                            <p className="text-2xl font-bold">{taskStats.completed}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-2">
                        <XCircle className="h-5 w-5 text-red-600" />
                        <div>
                            <p className="text-sm text-gray-600">Failed</p>
                            <p className="text-2xl font-bold">{taskStats.failed}</p>
                        </div>
                    </div>
                </Card>
            </div>

            {/* Filters */}
            <Card className="p-4">
                <div className="flex flex-wrap gap-4">
                    <div className="flex-1 min-w-64">
                        <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                            <Input
                                placeholder="Search tasks..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="pl-10"
                            />
                        </div>
                    </div>
                    
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="w-32">
                            <SelectValue placeholder="Status" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="ALL">All Status</SelectItem>
                            <SelectItem value="PENDING">Pending</SelectItem>
                            <SelectItem value="RUNNING">Running</SelectItem>
                            <SelectItem value="COMPLETED">Completed</SelectItem>
                            <SelectItem value="FAILED">Failed</SelectItem>
                        </SelectContent>
                    </Select>
                    
                    <Select value={platformFilter} onValueChange={setPlatformFilter}>
                        <SelectTrigger className="w-32">
                            <SelectValue placeholder="Platform" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="ALL">All Platforms</SelectItem>
                            <SelectItem value="tiktok">TikTok</SelectItem>
                            <SelectItem value="instagram">Instagram</SelectItem>
                            <SelectItem value="facebook">Facebook</SelectItem>
                            <SelectItem value="twitter">Twitter</SelectItem>
                            <SelectItem value="youtube">YouTube</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
            </Card>

            {/* Tasks List */}
            {loading ? (
                <div className="space-y-4">
                    {[...Array(3)].map((_, i) => (
                        <Card key={i} className="p-6">
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <Skeleton className="h-6 w-1/3" />
                                    <Skeleton className="h-6 w-20" />
                                </div>
                                <Skeleton className="h-4 w-2/3" />
                                <div className="flex gap-2">
                                    <Skeleton className="h-8 w-16" />
                                    <Skeleton className="h-8 w-16" />
                                    <Skeleton className="h-8 w-16" />
                                </div>
                            </div>
                        </Card>
                    ))}
                </div>
            ) : filteredTasks.length === 0 ? (
                <Card className="p-8 text-center">
                    <Target className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium mb-2">No tasks found</h3>
                    <p className="text-gray-600 mb-4">
                        {tasks.length === 0 
                            ? "Create your first automation task to get started."
                            : "No tasks match your current filters."
                        }
                    </p>
                    {tasks.length === 0 && (
                        <Button onClick={() => setShowCreateDialog(true)}>
                            <Plus className="w-4 h-4 mr-2" />
                            Create Your First Task
                        </Button>
                    )}
                </Card>
            ) : (
                <div className="space-y-4">
                    {filteredTasks.map((task) => (
                        <Card key={task.id} className="p-6">
                            <div className="flex items-start justify-between">
                                <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                        <h3 className="text-lg font-semibold">{task.name}</h3>
                                        <Badge className={getStatusColor(task.status)}>
                                            {getStatusIcon(task.status)}
                                            <span className="ml-1">{task.status}</span>
                                        </Badge>
                                        <Badge className={getPlatformColor(task.platform)}>
                                            {task.platform}
                                        </Badge>
                                        <Badge variant="outline">
                                            {getTaskTypeLabel(task.type)}
                                        </Badge>
                                    </div>
                                    
                                    {task.keywords && (
                                        <p className="text-sm text-gray-600 mb-2">
                                            <strong>Keywords:</strong> {task.keywords}
                                        </p>
                                    )}
                                    
                                    <div className="flex items-center gap-4 text-sm text-gray-500">
                                        <span>Created: {new Date(task.created_at).toLocaleString()}</span>
                                        <span>Max Items: {task.max_items || 'N/A'}</span>
                                        {task.updated_at && (
                                            <span>Updated: {new Date(task.updated_at).toLocaleString()}</span>
                                        )}
                                    </div>
                                </div>
                                
                                <div className="flex gap-2 ml-4">
                                    <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => {
                                            setSelectedTaskId(task.id);
                                            setShowTaskDetail(true);
                                        }}
                                        title="View task details"
                                    >
                                        <Eye className="w-4 h-4 mr-1" />
                                        Details
                                    </Button>
                                    
                                    {task.status === 'PENDING' && (
                                        <>
                                            <Button
                                                size="sm"
                                                onClick={() => handleExecuteTask(task.id)}
                                                disabled={executingTasks.has(task.id)}
                                                title="Execute this task"
                                            >
                                                {executingTasks.has(task.id) ? (
                                                    <>
                                                        <RefreshCw className="w-4 h-4 mr-1 animate-spin" />
                                                        Starting...
                                                    </>
                                                ) : (
                                                    <>
                                                        <Play className="w-4 h-4 mr-1" />
                                                        Execute
                                                    </>
                                                )}
                                            </Button>
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => window.open('/actor/accounts', '_blank')}
                                                title="Go to Accounts page to authenticate"
                                            >
                                                <Shield className="w-4 h-4" />
                                            </Button>
                                        </>
                                    )}
                                    
                                    <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => handleDeleteTask(task.id)}
                                        disabled={loading}
                                    >
                                        <Trash2 className="w-4 h-4" />
                                    </Button>
                                </div>
                            </div>
                        </Card>
                    ))}
                </div>
            )}
            
            {/* Task Detail Modal */}
            <TaskDetailModal
                taskId={selectedTaskId}
                isOpen={showTaskDetail}
                onClose={() => {
                    setShowTaskDetail(false);
                    setSelectedTaskId(null);
                }}
                onTaskUpdate={loadTasks}
            />
        </div>
    );
}
