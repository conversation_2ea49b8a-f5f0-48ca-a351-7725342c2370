'use client';

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { 
    Wifi, WifiOff, RefreshCw, Shield, Clock, CheckCircle, AlertCircle, XCircle,
    Activity, Globe, User, Settings, Trash2, Plus, Eye, LogIn
} from "lucide-react";
import { 
    authenticateActorAccount,
    getPlatformIcon,
    getPlatformColor,
    type ActorAccount
} from "@/lib/api/actor-system";

interface SessionInfo {
    id: number;
    account_id: number;
    platform: string;
    username: string;
    is_active: boolean;
    session_valid: boolean;
    last_activity: string;
    created_at: string;
    expires_at?: string;
    user_agent?: string;
    ip_address?: string;
    session_health: number;
    error_count: number;
    success_rate: number;
}

interface EnhancedSessionManagerProps {
    accounts: ActorAccount[];
    onAccountsChange: () => void;
}

export function EnhancedSessionManager({ accounts, onAccountsChange }: EnhancedSessionManagerProps) {
    const [sessions, setSessions] = useState<SessionInfo[]>([]);
    const [loading, setLoading] = useState(false);
    const [authenticating, setAuthenticating] = useState<number | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);

    useEffect(() => {
        loadSessions();
    }, [accounts]);

    const loadSessions = async () => {
        try {
            setLoading(true);
            setError(null);

            // Create mock session data based on accounts
            const mockSessions: SessionInfo[] = accounts.map((account, index) => ({
                id: account.id,
                account_id: account.id,
                platform: account.platform,
                username: account.username,
                is_active: account.is_active,
                session_valid: account.session_valid,
                last_activity: account.last_login || new Date().toISOString(),
                created_at: account.created_at,
                expires_at: account.session_valid ? new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() : undefined,
                user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                ip_address: '192.168.1.' + (100 + index),
                session_health: account.session_valid ? 85 + Math.floor(Math.random() * 15) : 0,
                error_count: account.session_valid ? Math.floor(Math.random() * 3) : 5 + Math.floor(Math.random() * 10),
                success_rate: account.session_valid ? 85 + Math.floor(Math.random() * 15) : 20 + Math.floor(Math.random() * 30)
            }));

            setSessions(mockSessions);
        } catch (err: any) {
            console.error('Error loading sessions:', err);
            setError('Failed to load session data');
        } finally {
            setLoading(false);
        }
    };

    const handleAuthenticate = async (accountId: number) => {
        try {
            setAuthenticating(accountId);
            setError(null);
            
            const result = await authenticateActorAccount(accountId);
            
            if (result.success) {
                const account = accounts.find(a => a.id === accountId);
                setSuccess(`Successfully authenticated ${account?.platform} account @${account?.username}`);
                onAccountsChange(); // Refresh accounts
                loadSessions(); // Refresh sessions
            } else {
                setError(`Authentication failed: ${result.error}`);
            }
        } catch (error: any) {
            console.error('Authentication error:', error);
            setError(`Authentication failed: ${error.message}`);
        } finally {
            setAuthenticating(null);
        }
    };

    const getSessionStatusIcon = (session: SessionInfo) => {
        if (!session.is_active) {
            return <XCircle className="h-4 w-4 text-red-500" />;
        }
        if (session.session_valid && session.session_health > 70) {
            return <CheckCircle className="h-4 w-4 text-green-500" />;
        }
        if (session.session_valid) {
            return <AlertCircle className="h-4 w-4 text-yellow-500" />;
        }
        return <WifiOff className="h-4 w-4 text-red-500" />;
    };

    const getSessionStatusText = (session: SessionInfo) => {
        if (!session.is_active) return 'Inactive';
        if (session.session_valid && session.session_health > 70) return 'Healthy';
        if (session.session_valid) return 'Degraded';
        return 'Expired';
    };

    const getSessionStatusColor = (session: SessionInfo) => {
        if (!session.is_active) return 'bg-gray-100 text-gray-800';
        if (session.session_valid && session.session_health > 70) return 'bg-green-100 text-green-800';
        if (session.session_valid) return 'bg-yellow-100 text-yellow-800';
        return 'bg-red-100 text-red-800';
    };

    const getHealthColor = (health: number) => {
        if (health >= 80) return 'text-green-600';
        if (health >= 60) return 'text-yellow-600';
        return 'text-red-600';
    };

    const getSessionStats = () => {
        const total = sessions.length;
        const active = sessions.filter(s => s.is_active && s.session_valid).length;
        const expired = sessions.filter(s => s.is_active && !s.session_valid).length;
        const inactive = sessions.filter(s => !s.is_active).length;
        const healthy = sessions.filter(s => s.session_health > 70).length;
        const avgHealth = sessions.length > 0 
            ? Math.round(sessions.reduce((sum, s) => sum + s.session_health, 0) / sessions.length)
            : 0;

        return { total, active, expired, inactive, healthy, avgHealth };
    };

    const stats = getSessionStats();

    return (
        <div className="space-y-6">
            {error && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {success && (
                <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>{success}</AlertDescription>
                </Alert>
            )}

            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Globe className="h-8 w-8 text-blue-500" />
                        <div>
                            <p className="text-sm text-gray-500">Total Sessions</p>
                            <p className="text-2xl font-bold">{stats.total}</p>
                        </div>
                    </div>
                </Card>
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Wifi className="h-8 w-8 text-green-500" />
                        <div>
                            <p className="text-sm text-gray-500">Active</p>
                            <p className="text-2xl font-bold">{stats.active}</p>
                        </div>
                    </div>
                </Card>
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Clock className="h-8 w-8 text-yellow-500" />
                        <div>
                            <p className="text-sm text-gray-500">Expired</p>
                            <p className="text-2xl font-bold">{stats.expired}</p>
                        </div>
                    </div>
                </Card>
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <WifiOff className="h-8 w-8 text-gray-500" />
                        <div>
                            <p className="text-sm text-gray-500">Inactive</p>
                            <p className="text-2xl font-bold">{stats.inactive}</p>
                        </div>
                    </div>
                </Card>
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Shield className="h-8 w-8 text-green-500" />
                        <div>
                            <p className="text-sm text-gray-500">Healthy</p>
                            <p className="text-2xl font-bold">{stats.healthy}</p>
                        </div>
                    </div>
                </Card>
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Activity className="h-8 w-8 text-purple-500" />
                        <div>
                            <p className="text-sm text-gray-500">Avg Health</p>
                            <p className="text-2xl font-bold">{stats.avgHealth}%</p>
                        </div>
                    </div>
                </Card>
            </div>

            {/* Controls */}
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Session Status</h3>
                <Button onClick={loadSessions} variant="outline" size="sm" disabled={loading}>
                    <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                    Refresh
                </Button>
            </div>

            {/* Session List */}
            {sessions.length === 0 ? (
                <Card className="p-8 text-center">
                    <Globe className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium mb-2">No sessions found</h3>
                    <p className="text-gray-600 mb-4">
                        Add some accounts to start managing sessions.
                    </p>
                </Card>
            ) : (
                <div className="grid gap-4">
                    {sessions.map((session) => (
                        <Card key={session.id} className="p-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-4">
                                    {/* Platform Icon */}
                                    <div className={`p-3 rounded-full ${getPlatformColor(session.platform)}`}>
                                        <span className="text-lg">{getPlatformIcon(session.platform)}</span>
                                    </div>
                                    
                                    {/* Session Info */}
                                    <div className="flex-1">
                                        <div className="flex items-center gap-2 mb-2">
                                            <h4 className="font-medium">@{session.username}</h4>
                                            <Badge className={getSessionStatusColor(session)}>
                                                {getSessionStatusText(session)}
                                            </Badge>
                                            <Badge variant="outline">
                                                {session.platform.charAt(0).toUpperCase() + session.platform.slice(1)}
                                            </Badge>
                                        </div>
                                        
                                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                            <div>
                                                <p className="text-gray-500">Health</p>
                                                <div className="flex items-center gap-2">
                                                    <Progress value={session.session_health} className="w-16 h-2" />
                                                    <span className={`font-medium ${getHealthColor(session.session_health)}`}>
                                                        {session.session_health}%
                                                    </span>
                                                </div>
                                            </div>
                                            <div>
                                                <p className="text-gray-500">Success Rate</p>
                                                <p className="font-medium">{session.success_rate}%</p>
                                            </div>
                                            <div>
                                                <p className="text-gray-500">Errors</p>
                                                <p className="font-medium">{session.error_count}</p>
                                            </div>
                                            <div>
                                                <p className="text-gray-500">Last Activity</p>
                                                <p className="font-medium">
                                                    {new Date(session.last_activity).toLocaleDateString()}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Actions */}
                                <div className="flex items-center gap-2">
                                    {getSessionStatusIcon(session)}
                                    
                                    {!session.session_valid && session.is_active && (
                                        <Button
                                            size="sm"
                                            onClick={() => handleAuthenticate(session.account_id)}
                                            disabled={authenticating === session.account_id}
                                        >
                                            {authenticating === session.account_id ? (
                                                <RefreshCw className="h-4 w-4 animate-spin" />
                                            ) : (
                                                <LogIn className="h-4 w-4" />
                                            )}
                                        </Button>
                                    )}
                                    
                                    <Button size="sm" variant="outline">
                                        <Eye className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </Card>
                    ))}
                </div>
            )}
        </div>
    );
}
