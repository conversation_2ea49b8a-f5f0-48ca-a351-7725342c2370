'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, Search, Calendar, Hash, Loader2 } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface TwitterAccount {
    id: number;
    platform_username: string;
    is_active: boolean;
    session_expires_at: string;
}

interface SimpleTwitterTaskFormProps {
    onTaskCreated?: () => void;
}

export default function SimpleTwitterTaskForm({ onTaskCreated }: SimpleTwitterTaskFormProps) {
    const [accounts, setAccounts] = useState<TwitterAccount[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);

    // Simple form data
    const [formData, setFormData] = useState({
        account_id: '',
        task_name: '',
        keywords: '',
        start_date: '',
        end_date: '',
        max_items: 10
    });

    // Load Twitter accounts
    useEffect(() => {
        loadTwitterAccounts();
    }, []);

    const loadTwitterAccounts = async () => {
        try {
            const token = localStorage.getItem('access_token');
            const response = await fetch('/api/actor/accounts/', {
                headers: {
                    'Authorization': `JWT ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                const twitterAccounts = data.filter((account: any) => account.platform === 'twitter');
                setAccounts(twitterAccounts);
            }
        } catch (error) {
            console.error('Error loading Twitter accounts:', error);
            setError('Failed to load Twitter accounts');
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError(null);
        setSuccess(null);

        // Simple validation
        if (!formData.account_id) {
            setError('Please select a Twitter account');
            return;
        }

        if (!formData.task_name.trim()) {
            setError('Please enter a task name');
            return;
        }

        if (!formData.keywords.trim()) {
            setError('Please enter search keywords');
            return;
        }

        try {
            setLoading(true);

            const token = localStorage.getItem('access_token');
            const taskData = {
                account_id: parseInt(formData.account_id),
                task_type: 'CONTENT_SEARCH',
                task_name: formData.task_name,
                description: `Simple Twitter search for: ${formData.keywords}`,
                keywords: formData.keywords,
                max_items: formData.max_items,
                start_date: formData.start_date || null,
                end_date: formData.end_date || null,
                task_parameters: {
                    keywords: formData.keywords,
                    simple_search: true
                }
            };

            const response = await fetch('/api/actor/tasks/create/', {
                method: 'POST',
                headers: {
                    'Authorization': `JWT ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(taskData)
            });

            const result = await response.json();

            if (response.ok && result.success) {
                setSuccess(`Twitter task created successfully! Task ID: ${result.task_id}`);
                
                // Reset form
                setFormData({
                    account_id: '',
                    task_name: '',
                    keywords: '',
                    start_date: '',
                    end_date: '',
                    max_items: 10
                });

                // Notify parent component
                if (onTaskCreated) {
                    onTaskCreated();
                }
            } else {
                setError(result.error || 'Failed to create Twitter task');
            }
        } catch (error) {
            console.error('Error creating Twitter task:', error);
            setError('Failed to create Twitter task');
        } finally {
            setLoading(false);
        }
    };

    return (
        <Card className="w-full max-w-2xl mx-auto">
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Search className="h-5 w-5 text-blue-500" />
                    Simple Twitter Search Task
                </CardTitle>
                <p className="text-sm text-gray-600">
                    Create a simple Twitter scraping task with keyword search and date filtering
                </p>
            </CardHeader>
            <CardContent>
                {error && (
                    <Alert className="mb-4" variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{error}</AlertDescription>
                    </Alert>
                )}

                {success && (
                    <Alert className="mb-4" variant="default">
                        <AlertDescription className="text-green-600">{success}</AlertDescription>
                    </Alert>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Twitter Account Selection */}
                    <div>
                        <Label htmlFor="account_id">Twitter Account *</Label>
                        <Select 
                            value={formData.account_id} 
                            onValueChange={(value) => setFormData({ ...formData, account_id: value })}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Select a Twitter account" />
                            </SelectTrigger>
                            <SelectContent>
                                {accounts.map((account) => (
                                    <SelectItem key={account.id} value={account.id.toString()}>
                                        <div className="flex items-center gap-2">
                                            @{account.platform_username}
                                            {account.is_active ? (
                                                <Badge variant="default" className="text-xs">Active</Badge>
                                            ) : (
                                                <Badge variant="secondary" className="text-xs">Inactive</Badge>
                                            )}
                                        </div>
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        {accounts.length === 0 && (
                            <p className="text-sm text-gray-500 mt-1">
                                No Twitter accounts found. Please add a Twitter account first.
                            </p>
                        )}
                    </div>

                    {/* Task Name */}
                    <div>
                        <Label htmlFor="task_name">Task Name *</Label>
                        <Input
                            id="task_name"
                            value={formData.task_name}
                            onChange={(e) => setFormData({ ...formData, task_name: e.target.value })}
                            placeholder="e.g., Twitter Search - Technology News"
                            required
                        />
                    </div>

                    {/* Keywords */}
                    <div>
                        <Label htmlFor="keywords" className="flex items-center gap-2">
                            <Hash className="h-4 w-4" />
                            Search Keywords *
                        </Label>
                        <Input
                            id="keywords"
                            value={formData.keywords}
                            onChange={(e) => setFormData({ ...formData, keywords: e.target.value })}
                            placeholder="e.g., artificial intelligence, technology, innovation"
                            required
                        />
                        <p className="text-sm text-gray-500 mt-1">
                            Enter keywords to search for on Twitter. Use simple terms for best results.
                        </p>
                    </div>

                    {/* Date Range */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <Label htmlFor="start_date" className="flex items-center gap-2">
                                <Calendar className="h-4 w-4" />
                                Start Date (Optional)
                            </Label>
                            <Input
                                id="start_date"
                                type="date"
                                value={formData.start_date}
                                onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
                            />
                        </div>
                        <div>
                            <Label htmlFor="end_date" className="flex items-center gap-2">
                                <Calendar className="h-4 w-4" />
                                End Date (Optional)
                            </Label>
                            <Input
                                id="end_date"
                                type="date"
                                value={formData.end_date}
                                onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
                            />
                        </div>
                    </div>

                    {/* Max Items */}
                    <div>
                        <Label htmlFor="max_items">Maximum Items to Scrape</Label>
                        <Select 
                            value={formData.max_items.toString()} 
                            onValueChange={(value) => setFormData({ ...formData, max_items: parseInt(value) })}
                        >
                            <SelectTrigger>
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="5">5 tweets</SelectItem>
                                <SelectItem value="10">10 tweets</SelectItem>
                                <SelectItem value="25">25 tweets</SelectItem>
                                <SelectItem value="50">50 tweets</SelectItem>
                                <SelectItem value="100">100 tweets</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Submit Button */}
                    <Button 
                        type="submit" 
                        disabled={loading || accounts.length === 0} 
                        className="w-full"
                    >
                        {loading ? (
                            <>
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                Creating Twitter Task...
                            </>
                        ) : (
                            <>
                                <Search className="h-4 w-4 mr-2" />
                                Create Twitter Search Task
                            </>
                        )}
                    </Button>
                </form>

                {/* Info Box */}
                <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <h4 className="font-medium text-blue-900 mb-2">How it works:</h4>
                    <ul className="text-sm text-blue-800 space-y-1">
                        <li>• Enter keywords to search for on Twitter</li>
                        <li>• Optionally set date range to filter results</li>
                        <li>• Task will run in background using Selenium scraper</li>
                        <li>• Real tweets will be scraped and saved to database</li>
                        <li>• View results in the Actor Data page</li>
                    </ul>
                </div>
            </CardContent>
        </Card>
    );
}
