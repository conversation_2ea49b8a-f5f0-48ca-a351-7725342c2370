'use client';

import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
    Plus, Edit, Trash2, User, Activity, LogIn, Users, Eye, EyeOff, 
    CheckCircle, AlertCircle, Clock, Shield, RefreshCw 
} from "lucide-react";
import {
    getActorAccounts,
    authenticateActorAccount,
    getPlatformIcon,
    getPlatformColor,
    type ActorAccount
} from "@/lib/api/actor-system";
import { AccountDetailsModal } from "./AccountDetailsModal";

interface EnhancedAccountListProps {
    accounts: ActorAccount[];
    onAccountsChange: () => void;
}

interface EditAccountData {
    username: string;
    email: string;
    password: string;
}

export function EnhancedAccountList({ accounts, onAccountsChange }: EnhancedAccountListProps) {
    const [editingAccount, setEditingAccount] = useState<ActorAccount | null>(null);
    const [formData, setFormData] = useState<EditAccountData>({
        username: '',
        email: '',
        password: ''
    });
    const [loading, setLoading] = useState(false);
    const [authenticating, setAuthenticating] = useState<number | null>(null);
    const [showPassword, setShowPassword] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);
    const [detailsModalOpen, setDetailsModalOpen] = useState(false);
    const [selectedAccountId, setSelectedAccountId] = useState<number | null>(null);

    const resetForm = () => {
        setFormData({
            username: '',
            email: '',
            password: ''
        });
        setShowPassword(false);
        setError(null);
        setSuccess(null);
    };

    const handleEdit = (account: ActorAccount) => {
        setEditingAccount(account);
        setFormData({
            username: account.username,
            email: account.email || '',
            password: '' // Don't pre-fill password for security
        });
        setError(null);
        setSuccess(null);
    };

    const handleViewDetails = (accountId: number) => {
        setSelectedAccountId(accountId);
        setDetailsModalOpen(true);
    };

    const handleUpdate = async () => {
        if (!editingAccount) return;
        
        try {
            setLoading(true);
            setError(null);
            
            // TODO: Implement update account API
            // await updateActorAccount(editingAccount.id, formData);
            
            setSuccess('Account updated successfully');
            setEditingAccount(null);
            resetForm();
            onAccountsChange();
        } catch (error: any) {
            console.error('Error updating account:', error);
            setError(error.response?.data?.error || 'Failed to update account');
        } finally {
            setLoading(false);
        }
    };

    const handleDelete = async (accountId: number) => {
        if (!confirm('Are you sure you want to delete this account? This action cannot be undone.')) {
            return;
        }

        try {
            setLoading(true);
            setError(null);
            
            // TODO: Implement delete account API
            // await deleteActorAccount(accountId);
            
            setSuccess('Account deleted successfully');
            onAccountsChange();
        } catch (error: any) {
            console.error('Error deleting account:', error);
            setError(error.response?.data?.error || 'Failed to delete account');
        } finally {
            setLoading(false);
        }
    };

    const handleAuthenticate = async (account: ActorAccount) => {
        try {
            setAuthenticating(account.id);
            setError(null);
            
            const result = await authenticateActorAccount(account.id);
            
            if (result.success) {
                setSuccess(`Successfully authenticated ${account.platform} account @${account.username}`);
                onAccountsChange(); // Refresh to update session status
            } else {
                setError(`Authentication failed: ${result.error}`);
            }
        } catch (error: any) {
            console.error('Authentication error:', error);
            setError(`Authentication failed: ${error.message}`);
        } finally {
            setAuthenticating(null);
        }
    };

    const getStatusIcon = (account: ActorAccount) => {
        if (!account.is_active) {
            return <AlertCircle className="h-4 w-4 text-red-500" />;
        }
        if (account.session_valid) {
            return <CheckCircle className="h-4 w-4 text-green-500" />;
        }
        return <Clock className="h-4 w-4 text-yellow-500" />;
    };

    const getStatusText = (account: ActorAccount) => {
        if (!account.is_active) return 'Inactive';
        if (account.session_valid) return 'Active';
        return 'Session Expired';
    };

    const getStatusColor = (account: ActorAccount) => {
        if (!account.is_active) return 'bg-red-100 text-red-800';
        if (account.session_valid) return 'bg-green-100 text-green-800';
        return 'bg-yellow-100 text-yellow-800';
    };

    return (
        <div className="space-y-4">
            {error && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {success && (
                <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>{success}</AlertDescription>
                </Alert>
            )}

            <div className="flex justify-between items-center">
                <p className="text-sm text-gray-600">
                    {accounts.length} account{accounts.length !== 1 ? 's' : ''} across {new Set(accounts.map(a => a.platform)).size} platform{new Set(accounts.map(a => a.platform)).size !== 1 ? 's' : ''}
                </p>
                <div className="flex gap-2">
                    <Button onClick={onAccountsChange} variant="outline" size="sm">
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Refresh
                    </Button>
                    <Link href="/actor/accounts/add">
                        <Button size="sm">
                            <Plus className="h-4 w-4 mr-2" />
                            Add Account
                        </Button>
                    </Link>
                </div>
            </div>

            {accounts.length === 0 ? (
                <Card className="p-8 text-center">
                    <Users className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium mb-2">No accounts found</h3>
                    <p className="text-gray-600 mb-4">
                        Get started by adding your first social media account to the Actor system.
                    </p>
                    <Link href="/actor/accounts/add">
                        <Button>
                            <Plus className="h-4 w-4 mr-2" />
                            Add Your First Account
                        </Button>
                    </Link>
                </Card>
            ) : (
                <div className="grid gap-4">
                    {accounts.map((account) => (
                        <Card key={account.id} className="p-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-4">
                                    {/* Platform Icon */}
                                    <div className={`p-3 rounded-full ${getPlatformColor(account.platform)}`}>
                                        <span className="text-lg">{getPlatformIcon(account.platform)}</span>
                                    </div>
                                    
                                    {/* Account Info */}
                                    <div>
                                        <div className="flex items-center gap-2 mb-1">
                                            <h4 className="font-medium">@{account.username}</h4>
                                            <Badge className={getStatusColor(account)}>
                                                {getStatusText(account)}
                                            </Badge>
                                            <Badge variant="outline">
                                                {account.platform.charAt(0).toUpperCase() + account.platform.slice(1)}
                                            </Badge>
                                        </div>
                                        <div className="flex items-center gap-4 text-sm text-gray-600">
                                            {getStatusIcon(account)}
                                            <span>
                                                {account.last_login 
                                                    ? `Last login: ${new Date(account.last_login).toLocaleDateString()}`
                                                    : 'Never logged in'
                                                }
                                            </span>
                                            {account.email && (
                                                <span>• {account.email}</span>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Actions */}
                                <div className="flex items-center gap-2">
                                    {!account.session_valid && (
                                        <Button
                                            size="sm"
                                            variant="outline"
                                            onClick={() => handleAuthenticate(account)}
                                            disabled={authenticating === account.id}
                                        >
                                            {authenticating === account.id ? (
                                                <RefreshCw className="h-4 w-4 animate-spin" />
                                            ) : (
                                                <LogIn className="h-4 w-4" />
                                            )}
                                        </Button>
                                    )}

                                    <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => handleViewDetails(account.id)}
                                        title="View Account Details"
                                    >
                                        <Eye className="h-4 w-4" />
                                    </Button>

                                    <Dialog>
                                        <DialogTrigger asChild>
                                            <Button size="sm" variant="outline" onClick={() => handleEdit(account)}>
                                                <Edit className="h-4 w-4" />
                                            </Button>
                                        </DialogTrigger>
                                        <DialogContent>
                                            <DialogHeader>
                                                <DialogTitle>Edit Account</DialogTitle>
                                            </DialogHeader>
                                            <div className="space-y-4">
                                                <div>
                                                    <Label htmlFor="edit-username">Username</Label>
                                                    <Input
                                                        id="edit-username"
                                                        value={formData.username}
                                                        onChange={(e) => setFormData({...formData, username: e.target.value})}
                                                        disabled={loading}
                                                    />
                                                </div>
                                                <div>
                                                    <Label htmlFor="edit-email">Email</Label>
                                                    <Input
                                                        id="edit-email"
                                                        type="email"
                                                        value={formData.email}
                                                        onChange={(e) => setFormData({...formData, email: e.target.value})}
                                                        disabled={loading}
                                                    />
                                                </div>
                                                <div>
                                                    <Label htmlFor="edit-password">New Password (optional)</Label>
                                                    <div className="relative">
                                                        <Input
                                                            id="edit-password"
                                                            type={showPassword ? 'text' : 'password'}
                                                            value={formData.password}
                                                            onChange={(e) => setFormData({...formData, password: e.target.value})}
                                                            placeholder="Leave blank to keep current password"
                                                            disabled={loading}
                                                        />
                                                        <Button
                                                            type="button"
                                                            variant="ghost"
                                                            size="sm"
                                                            className="absolute right-0 top-0 h-full px-3"
                                                            onClick={() => setShowPassword(!showPassword)}
                                                        >
                                                            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                                        </Button>
                                                    </div>
                                                </div>
                                                <div className="flex justify-end gap-2">
                                                    <Button variant="outline" onClick={() => setEditingAccount(null)}>
                                                        Cancel
                                                    </Button>
                                                    <Button onClick={handleUpdate} disabled={loading}>
                                                        {loading ? 'Updating...' : 'Update Account'}
                                                    </Button>
                                                </div>
                                            </div>
                                        </DialogContent>
                                    </Dialog>

                                    <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => handleDelete(account.id)}
                                        disabled={loading}
                                        className="text-red-600 hover:text-red-700"
                                    >
                                        <Trash2 className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </Card>
                    ))}
                </div>
            )}

            <AccountDetailsModal
                isOpen={detailsModalOpen}
                onClose={() => setDetailsModalOpen(false)}
                accountId={selectedAccountId}
                onAccountUpdate={onAccountsChange}
            />
        </div>
    );
}
