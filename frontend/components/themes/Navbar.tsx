"use client"

import { But<PERSON> } from '@/components/ui/button'
import { ThemeToggle } from '@/components/themes/theme-toggle'
import Link from 'next/link'
import { useAuth } from '@/components/auth/AuthProvider'

export default function Navbar() {
  const { user, logout } = useAuth()

  return (
    <header className="border-b">
      <div className="container flex items-center justify-between h-16 px-4">
        <Link href="/" className="text-xl font-bold">BlogApp</Link>
        <div className="flex items-center gap-4">
          <ThemeToggle />
          {user ? (
            <>
              <Link href="/posts/create">
                <Button variant="outline">Create Post</Button>
              </Link>
              <Button onClick={logout}>Logout</Button>
            </>
          ) : (
            <>
              <Link href="/login">
                <Button variant="outline">Login</Button>
              </Link>
              <Link href="/register">
                <Button>Sign Up</Button>
              </Link>
            </>
          )}
        </div>
      </div>
    </header>
  )
}