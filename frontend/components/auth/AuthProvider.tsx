'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { User } from '@/lib/types'
import { api } from '@/lib/axios'
import { useRouter } from 'next/navigation'

interface AuthContextType {
  user: User | null
  login: (credentials: { username: string; password: string }) => Promise<void>
  register: (userData: { username: string; email: string; password: string }) => Promise<void>
  logout: () => void
  isLoading: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  const fetchUser = async () => {
    if (typeof window === 'undefined') return;
    
    try {
      const token = localStorage.getItem('access_token')
      if (!token) {
        setUser(null)
        setIsLoading(false)
        return
      }

      // Verify token validity before making request
      try {
        const { exp } = JSON.parse(atob(token.split('.')[1]));
        if (Date.now() >= exp * 1000) {
          // Token expired - silently clear and don't log error
          localStorage.removeItem('access_token')
          localStorage.removeItem('refresh_token')
          setUser(null)
          setIsLoading(false)
          return
        }
      } catch (tokenError) {
        // Invalid token format - silently clear
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')
        setUser(null)
        setIsLoading(false)
        return
      }

      const res = await api.get('/auth/users/me/')
      setUser(res.data)
    } catch (error) {
      // Only log actual API errors, not token issues
      if (error instanceof Error && !error.message.includes('token')) {
        console.error('Error fetching user:', error)
      }
      setUser(null)
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchUser()
  }, [])

  const login = async (credentials: { username: string; password: string }) => {
    try {
      const res = await api.post('/auth/jwt/create/', credentials);
      
      if (!res.data?.access || !res.data?.refresh) {
        throw new Error('Invalid server response - missing tokens');
      }
      
      localStorage.setItem('access_token', res.data.access);
      localStorage.setItem('refresh_token', res.data.refresh);
      
      await fetchUser();
      router.push('/dashboard');
    } catch (error: any) {
      console.error('Login error:', error);
      if (error.response?.data) {
        const errorData = error.response.data;
        if (typeof errorData === 'object') {
          const errorMessages = Object.entries(errorData)
            .map(([key, value]) => `${key}: ${value}`)
            .join('. ');
          throw new Error(errorMessages);
        }
        throw new Error(errorData);
      }
      throw new Error('Authentication failed. Please check your credentials and ensure the server is running.');
    }
  };

  const register = async (userData: { username: string; email: string; password: string }) => {
    try {
      const res = await api.post('/auth/users/', userData);
      
      // After successful registration, automatically log in the user
      await login({ username: userData.username, password: userData.password });
    } catch (error: any) {
      console.error('Registration error:', error);
      if (error.response?.data) {
        const errorMessages = Object.values(error.response.data).flat().join('. ');
        throw new Error(errorMessages);
      }
      throw new Error('Registration failed. Please try again.');
    }
  };

  const logout = () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    setUser(null);
    router.push('/login');
  };

  return (
    <AuthContext.Provider value={{ user, login, register, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthProvider;