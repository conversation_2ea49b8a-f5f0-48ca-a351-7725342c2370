"use client";

import { useState } from "react";
import { useAuth } from "@/components/auth/AuthProvider";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export function LoginForm() {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsSubmitting(true);

    if (!username || !password) {
      setError("Please enter both username and password");
      setIsSubmitting(false);
      return;
    }

    try {
      await login({ username, password });
    } catch (err: any) {
      console.error('Login error:', err);
      if (err.response?.data) {
        // Handle structured error response
        const errorMessage = typeof err.response.data === 'object'
          ? Object.values(err.response.data).flat().join('. ')
          : err.response.data;
        setError(errorMessage || 'Login failed. Please check your credentials.');
      } else {
        setError(err.message || 'Login failed. Please check your credentials and ensure the server is running.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Welcome back</CardTitle>
        <CardDescription>Enter your credentials to sign in</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="username" className="text-sm font-medium">
              Username
            </label>
            <Input
              id="username"
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="Enter your username"
              required
              disabled={isSubmitting}
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="password" className="text-sm font-medium">
              Password
            </label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              required
              disabled={isSubmitting}
            />
          </div>
          <Button type="submit" className="w-full" disabled={isSubmitting}>
            {isSubmitting ? "Signing in..." : "Sign in"}
          </Button>
          {error && (
            <div className="text-sm text-red-600 text-center mt-2">
              {error}
            </div>
          )}
        </form>
        <div className="text-center text-sm text-muted-foreground mt-4">
          Don't have an account?{" "}
          <Link href="/register" className="text-primary underline hover:no-underline">
            Sign up
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}