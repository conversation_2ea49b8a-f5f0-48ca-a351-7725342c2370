import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { HealthStatus as HealthStatusType } from "@/lib/types/crawler";

interface HealthStatusProps {
    health: HealthStatusType;
}

export function HealthStatus({ health }: HealthStatusProps) {
    const statusColors = {
        healthy: "bg-green-500",
        warning: "bg-yellow-500",
        critical: "bg-red-500"
    };

    return (
        <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">System Health</h3>
                <Badge className={`${statusColors[health.status]} text-white`}>
                    {health.status.toUpperCase()}
                </Badge>
            </div>
            
            <p className="text-gray-600 mb-4">{health.message}</p>
            
            <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                    <p className="text-sm text-gray-500">Success Rate</p>
                    <p className="text-xl font-semibold">
                        {(health.metrics.success_rate * 100).toFixed(1)}%
                    </p>
                </div>
                <div className="text-center">
                    <p className="text-sm text-gray-500">Error Rate</p>
                    <p className="text-xl font-semibold">
                        {(health.metrics.error_rate * 100).toFixed(1)}%
                    </p>
                </div>
                <div className="text-center">
                    <p className="text-sm text-gray-500">Avg Response Time</p>
                    <p className="text-xl font-semibold">
                        {health.metrics.average_response_time.toFixed(2)}s
                    </p>
                </div>
            </div>
        </Card>
    );
}