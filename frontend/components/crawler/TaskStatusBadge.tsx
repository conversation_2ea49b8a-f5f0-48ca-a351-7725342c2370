import { Badge } from "@/components/ui/badge";
import { TaskStatus } from "@/lib/types/crawler";

interface TaskStatusBadgeProps {
    status: TaskStatus;
}

export function TaskStatusBadge({ status }: TaskStatusBadgeProps) {
    const variants: Record<TaskStatus, string> = {
        PENDING: "bg-yellow-500",
        RUNNING: "bg-blue-500",
        COMPLETED: "bg-green-500",
        FAILED: "bg-red-500",
        CANCELLED: "bg-gray-500"
    };

    return (
        <Badge className={`${variants[status]} text-white`}>
            {status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()}
        </Badge>
    );
}