import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Edit, Trash2, Play, Pause } from 'lucide-react';

interface Project {
  id: number;
  name: string;
  description: string;
  platform: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

interface ProjectInfoProps {
  project: Project;
  onEdit?: () => void;
  onDelete?: () => void;
  onToggleStatus?: () => void;
}

const ProjectInfo: React.FC<ProjectInfoProps> = ({
  project,
  onEdit,
  onDelete,
  onToggleStatus
}) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'tiktok':
        return 'bg-black text-white';
      case 'instagram':
        return 'bg-pink-500 text-white';
      case 'facebook':
        return 'bg-blue-600 text-white';
      case 'twitter':
        return 'bg-blue-400 text-white';
      case 'youtube':
        return 'bg-red-600 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-2xl">{project.name}</CardTitle>
          <div className="flex gap-2">
            <Badge className={getStatusColor(project.status)}>
              {project.status}
            </Badge>
            <Badge className={getPlatformColor(project.platform)}>
              {project.platform}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-gray-600">{project.description}</p>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium">Created:</span>
            <p className="text-gray-600">{new Date(project.createdAt).toLocaleDateString()}</p>
          </div>
          <div>
            <span className="font-medium">Last Updated:</span>
            <p className="text-gray-600">{new Date(project.updatedAt).toLocaleDateString()}</p>
          </div>
        </div>

        <div className="flex gap-2 pt-4">
          {onEdit && (
            <Button onClick={onEdit} variant="outline" size="sm">
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>
          )}
          {onToggleStatus && (
            <Button onClick={onToggleStatus} variant="outline" size="sm">
              {project.status === 'active' ? (
                <>
                  <Pause className="w-4 h-4 mr-2" />
                  Pause
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-2" />
                  Activate
                </>
              )}
            </Button>
          )}
          {onDelete && (
            <Button onClick={onDelete} variant="destructive" size="sm">
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ProjectInfo;
