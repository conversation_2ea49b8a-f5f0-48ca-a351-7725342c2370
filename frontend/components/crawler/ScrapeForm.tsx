import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { scrapeVideo, scrapeUser, searchTikTok } from "@/lib/api/crawler";
import { useToast } from "@/components/ui/use-toast";

export function ScrapeForm() {
    const [loading, setLoading] = useState(false);
    const [jobName, setJobName] = useState("");
    const [startUrls, setStartUrls] = useState("");
    const [proxyConfig, setProxyConfig] = useState("auto");
    const [maxItems, setMaxItems] = useState<number | "">("");
    const { toast } = useToast();

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        try {
            // Split URLs by newline and filter empty lines
            const urls = startUrls.split('\n').filter(url => url.trim());
            if (urls.length === 0) {
                throw new Error('Please enter at least one URL or keyword');
            }

            // Determine scrape type based on first URL content
            let scrapeType = '';
            const firstUrl = urls[0].trim();

            // Validate and determine scrape type
            if (firstUrl.includes('tiktok.com/video/') || firstUrl.includes('tiktok.com/@') && firstUrl.includes('/video/')) {
                scrapeType = 'VIDEO';
            } else if (firstUrl.includes('tiktok.com/@') && !firstUrl.includes('/video/')) {
                scrapeType = 'USER';
            } else if (firstUrl.startsWith('http://') || firstUrl.startsWith('https://')) {
                // If it's a URL but doesn't match TikTok patterns, it's invalid
                throw new Error('Please enter a valid TikTok URL (video or user profile)');
            } else {
                // If it's not a URL, treat it as a search keyword
                scrapeType = 'SEARCH';
            }

            const params = {
                job_name: jobName || `${scrapeType} Scrape`,
                proxy_config: proxyConfig === 'auto' ? null : proxyConfig,
                max_items: maxItems || null
            };

            let result;
            try {
                // Check authentication before making API calls
                const token = localStorage.getItem('access_token');
                if (!token) {
                    throw new Error('Not authenticated. Please log in.');
                }
                
                // Add a timeout to the API calls to prevent hanging requests
                let result;
                const apiTimeout = 30000; // 30 seconds timeout
                
                const apiCall = async () => {
                    // Clean parameters - remove null/undefined values
                    const cleanParams = {
                        ...params,
                        ...(params.proxy_config !== null && { proxy_config: params.proxy_config }),
                        ...(params.max_items !== null && { max_items: params.max_items })
                    };

                    if (scrapeType === 'VIDEO') {
                        return await scrapeVideo(firstUrl, cleanParams);
                    } else if (scrapeType === 'USER') {
                        return await scrapeUser(firstUrl, cleanParams);
                    } else {
                        return await searchTikTok(firstUrl, cleanParams);
                    }
                };
                
                // Create a promise that rejects after the timeout
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Request timed out. Please try again.')), apiTimeout);
                });
                
                // Race the API call against the timeout
                result = await Promise.race([apiCall(), timeoutPromise]) as { task_id: string };
                console.log('API Response:', result); // Debug log
            } catch (apiError) {
                console.error('API Error:', apiError); // Debug log
                
                // Improved error logging with type checking
                if (apiError && typeof apiError === 'object') {
                    console.error('API Error Details:', {
                        status: (apiError as any).response?.status,
                        data: (apiError as any).response?.data,
                        headers: (apiError as any).response?.headers,
                        message: (apiError as {message?: string}).message
                    });
                } else {
                    console.error('API Error Details: Unknown error format', apiError);
                }
                // Handle different types of API errors with more specific messages
                if ((apiError as any).response?.status === 401) {
                    throw new Error('Authentication failed. Please log in again.');
                } else if ((apiError as any).response?.status === 400) {
                    // Extract validation errors if available
                    const errorData = (apiError as { response?: { data: any } })?.response?.data;
                    let errorMessage = 'Validation error';
                    
                    if (errorData && typeof errorData === 'object') {
                        if (errorData.error) {
                            errorMessage = errorData.error;
                        } else if (errorData.detail) {
                            errorMessage = errorData.detail;
                        } else {
                            // Try to extract field-specific errors
                            const fieldErrors = Object.entries(errorData)
                                .filter(([key, value]) => key !== 'error' && key !== 'detail')
                                .map(([field, errors]) => `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`)
                                .join('; ');
                                
                            if (fieldErrors) {
                                errorMessage = `Validation errors: ${fieldErrors}`;
                            }
                        }
                    }
                    
                    throw new Error(errorMessage);
                } else if ((apiError as any).response?.status === 500) {
                    throw new Error('Server error. Please try again later or contact support.');
                } else if (apiError && typeof apiError === 'object' && 'message' in apiError) {
                    throw new Error(`API request failed: ${apiError.message}`);
                } else {
                    throw new Error('Failed to connect to the server. Please check your internet connection and try again.');
                }
            }

            toast({
                title: "Task Created",
                description: `Scraping task '${params.job_name}' (${scrapeType}) started. Task ID: ${(result as { task_id: string } | undefined)?.task_id || 'Unknown'}`,
            });
            setJobName("");
            setStartUrls("");
            setMaxItems("");
        } catch (error) {
            console.error('Form Error:', error); // Debug log
            
            // Determine the appropriate error message to display
            let errorTitle = "Error";
            let errorMessage = "Failed to start scraping task";
            
            if (error instanceof Error) {
                errorMessage = error.message;
            } else if (typeof error === 'string') {
                errorMessage = error;
            } else if (error && typeof error === 'object' && 'message' in error) {
                errorMessage = String(error.message);
            } else {
                errorMessage = "An unknown error occurred";
            }
            
            // Show toast with the error message
            toast({
                title: errorTitle,
                description: errorMessage,
                variant: "destructive",
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <Card className="p-6">
            <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Left Column */}
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="startUrls">Start URLs or Keywords</Label>
                            <Textarea
                                id="startUrls"
                                placeholder="Enter TikTok video URLs, user profile URLs (@username), or search keywords (one per line)"
                                value={startUrls}
                                onChange={(e) => setStartUrls(e.target.value)}
                                required
                                rows={6}
                            />
                            <p className="text-sm text-gray-500 mt-1">
                                Examples:<br />
                                Video: https://www.tiktok.com/video/1234567890<br />
                                User: https://www.tiktok.com/@username<br />
                                Search: dance challenge
                            </p>
                        </div>
                        <div>
                            <Label htmlFor="jobName">Job Name</Label>
                            <Input
                                id="jobName"
                                placeholder="Enter a name for this job"
                                value={jobName}
                                onChange={(e) => setJobName(e.target.value)}
                            />
                        </div>
                        <div>
                            <Label htmlFor="proxyConfig">Proxy Configuration</Label>
                            <Select value={proxyConfig} onValueChange={setProxyConfig}>
                                <SelectTrigger id="proxyConfig">
                                    <SelectValue placeholder="Select proxy" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="auto">Automatic</SelectItem>
                                    <SelectItem value="datacenter">Datacenter proxies</SelectItem>
                                    <SelectItem value="residential">Residential proxies</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div>
                            <Label htmlFor="maxItems">Max Items</Label>
                            <Input
                                id="maxItems"
                                type="number"
                                placeholder="Maximum number of items to scrape"
                                value={maxItems}
                                onChange={(e) => setMaxItems(e.target.value === '' ? '' : Number(e.target.value))}
                                min={1}
                            />
                        </div>
                    </div>

                    {/* Right Column - Advanced Settings */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Advanced Settings</h3>
                        <p className="text-sm text-gray-500">Configure additional scraping parameters.</p>
                        <div className="flex items-center space-x-2">
                            <Checkbox id="extendOutput" />
                            <Label htmlFor="extendOutput">Include extended metadata</Label>
                        </div>
                    </div>
                </div>

                <Button type="submit" disabled={loading} className="w-full">
                    {loading ? "Creating Task..." : "Create Scraping Task"}
                </Button>
            </form>
        </Card>
    );
}