import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Database, Search, Filter, Download, Eye, ExternalLink } from 'lucide-react';

interface CrawledData {
  id: number;
  title: string;
  url: string;
  platform: string;
  author: string;
  crawledAt: string;
  status: 'success' | 'failed' | 'pending';
  dataType: string;
}

interface CrawledDataListProps {
  data: CrawledData[];
  onView?: (item: CrawledData) => void;
  onExport?: () => void;
}

const CrawledDataList: React.FC<CrawledDataListProps> = ({
  data = [],
  onView,
  onExport
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [platformFilter, setPlatformFilter] = useState('all');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'tiktok':
        return 'bg-black text-white';
      case 'instagram':
        return 'bg-pink-500 text-white';
      case 'facebook':
        return 'bg-blue-600 text-white';
      case 'twitter':
        return 'bg-blue-400 text-white';
      case 'youtube':
        return 'bg-red-600 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const filteredData = data.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.author.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter;
    const matchesPlatform = platformFilter === 'all' || item.platform.toLowerCase() === platformFilter;
    
    return matchesSearch && matchesStatus && matchesPlatform;
  });

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Crawled Data</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Database className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500">No data crawled yet</p>
            <p className="text-sm text-gray-400 mt-2">
              Start a crawling task to see data here
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Crawled Data ({filteredData.length})</CardTitle>
          {onExport && (
            <Button onClick={onExport} variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className="flex gap-4 mb-6">
          <div className="flex-1">
            <Input
              placeholder="Search by title or author..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="success">Success</SelectItem>
              <SelectItem value="failed">Failed</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
            </SelectContent>
          </Select>
          <Select value={platformFilter} onValueChange={setPlatformFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Platform" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Platforms</SelectItem>
              <SelectItem value="tiktok">TikTok</SelectItem>
              <SelectItem value="instagram">Instagram</SelectItem>
              <SelectItem value="facebook">Facebook</SelectItem>
              <SelectItem value="twitter">Twitter</SelectItem>
              <SelectItem value="youtube">YouTube</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Data List */}
        <div className="space-y-4">
          {filteredData.map((item) => (
            <div
              key={item.id}
              className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
            >
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h4 className="font-medium truncate max-w-md">{item.title}</h4>
                  <Badge className={getStatusColor(item.status)}>
                    {item.status}
                  </Badge>
                  <Badge className={getPlatformColor(item.platform)}>
                    {item.platform}
                  </Badge>
                </div>
                
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <span>By: {item.author}</span>
                  <span>Type: {item.dataType}</span>
                  <span>Crawled: {new Date(item.crawledAt).toLocaleString()}</span>
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button
                  onClick={() => window.open(item.url, '_blank')}
                  variant="outline"
                  size="sm"
                >
                  <ExternalLink className="w-4 h-4" />
                </Button>
                {onView && (
                  <Button
                    onClick={() => onView(item)}
                    variant="outline"
                    size="sm"
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>

        {filteredData.length === 0 && data.length > 0 && (
          <div className="text-center py-8">
            <Search className="w-8 h-8 mx-auto text-gray-400 mb-2" />
            <p className="text-gray-500">No data matches your filters</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CrawledDataList;
