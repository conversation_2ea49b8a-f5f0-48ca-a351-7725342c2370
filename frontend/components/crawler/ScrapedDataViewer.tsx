'use client';

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { DataTable } from "@/components/ui/data-table";
import { Heart, MessageCircle, Share, Eye, User, Calendar, Hash, ExternalLink } from "lucide-react";
import { getScrapedDataByTask, getScrapedDataSummary } from "@/lib/api/crawler";
import type { TikTokContent, ScrapedDataSummary, TaskScrapedData } from "@/lib/types/crawler";

interface ScrapedDataViewerProps {
    taskId?: number;
    showSummary?: boolean;
}

export function ScrapedDataViewer({ taskId, showSummary = true }: ScrapedDataViewerProps) {
    const [taskData, setTaskData] = useState<TaskScrapedData | null>(null);
    const [summary, setSummary] = useState<ScrapedDataSummary | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);

                const promises = [];
                if (taskId) {
                    promises.push(getScrapedDataByTask(taskId));
                }
                if (showSummary) {
                    promises.push(getScrapedDataSummary());
                }

                const results = await Promise.all(promises);
                
                if (taskId && results.length > 0) {
                    setTaskData(results[0] as TaskScrapedData);
                }
                if (showSummary) {
                    const summaryIndex = showSummary && taskId ? 1 : 0;
                    if (results[summaryIndex]) {
                        setSummary(results[summaryIndex] as ScrapedDataSummary);
                    }
                }
            } catch (err) {
                if (err instanceof Error && err.message.includes('401')) {
                    setError('Authentication required. Please log in to view scraped data.');
                } else {
                    setError(err instanceof Error ? err.message : 'Failed to fetch scraped data');
                }
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [taskId, showSummary]);





    // Define table columns for TikTok data
    const columns = [
        {
            key: "desc",
            header: "Description",
            sortable: true,
            filterable: true,
            render: (item: TikTokContent) => (
                <div className="max-w-xs truncate" title={item.desc}>
                    {item.desc || "No description"}
                </div>
            ),
        },
        {
            key: "author",
            header: "Author",
            sortable: true,
            filterable: true,
            render: (item: TikTokContent) => (
                <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span className="font-medium">{item.author}</span>
                </div>
            ),
        },
        {
            key: "video_id",
            header: "Video ID",
            sortable: true,
            render: (item: TikTokContent) => (
                <div className="flex items-center gap-2">
                    <Hash className="h-4 w-4" />
                    <code className="text-sm bg-muted px-1 rounded">{item.video_id || item.id}</code>
                </div>
            ),
        },
        {
            key: "stats",
            header: "Stats",
            render: (item: TikTokContent) => (
                <div className="flex flex-wrap gap-1">
                    <Badge variant="secondary" className="text-xs">
                        <Heart className="h-3 w-3 mr-1" />
                        {item.stats?.likes?.toLocaleString() || 0}
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                        <MessageCircle className="h-3 w-3 mr-1" />
                        {item.stats?.comments?.toLocaleString() || 0}
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                        <Share className="h-3 w-3 mr-1" />
                        {item.stats?.shares?.toLocaleString() || 0}
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                        <Eye className="h-3 w-3 mr-1" />
                        {item.stats?.views?.toLocaleString() || 0}
                    </Badge>
                </div>
            ),
        },
        {
            key: "music",
            header: "Music",
            sortable: true,
            filterable: true,
            render: (item: TikTokContent) => 
                item.music?.title ? (
                    <div className="max-w-xs truncate" title={item.music.title}>
                        {item.music.title}
                    </div>
                ) : (
                    <span className="text-muted-foreground">No music</span>
                ),
        },
        {
            key: "created_time",
            header: "Created",
            sortable: true,
            render: (item: TikTokContent) => (
                <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span className="text-sm">
                        {item.created_time ? new Date(item.created_time).toLocaleDateString() : 'N/A'}
                    </span>
                </div>
            ),
        },
    ];

    if (loading) {
        return (
            <div className="space-y-4">
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-64 w-full" />
            </div>
        );
    }

    if (error) {
        return (
            <Alert variant="destructive">
                <AlertDescription>
                    {error}
                    {error.includes('Authentication required') && (
                        <div className="mt-2">
                            <Button 
                                variant="outline" 
                                size="sm" 
                                onClick={() => window.location.href = '/login'}
                            >
                                Go to Login
                            </Button>
                        </div>
                    )}
                </AlertDescription>
            </Alert>
        );
    }

    return (
        <div className="space-y-6">
            {showSummary && summary && (
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Hash className="h-5 w-5" />
                            Data Summary
                        </CardTitle>
                        <CardDescription>
                            Overview of scraped TikTok content
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div className="text-center">
                                <div className="text-2xl font-bold text-primary">
                                    {summary.total_items.toLocaleString()}
                                </div>
                                <div className="text-sm text-muted-foreground">Total Items</div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-green-600">
                                    {summary.by_type?.SEARCH_RESULT || 0}
                                </div>
                                <div className="text-sm text-muted-foreground">Search Results</div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-blue-600">
                                    {summary.by_type?.VIDEO || 0}
                                </div>
                                <div className="text-sm text-muted-foreground">Videos</div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-purple-600">
                                    {summary.by_type?.USER || 0}
                                </div>
                                <div className="text-sm text-muted-foreground">Users</div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}



            {taskData && (
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle className="flex items-center gap-2">
                                    {taskData.task.job_name}
                                    <Badge variant="outline">
                                        {taskData.task.task_type}
                                    </Badge>
                                </CardTitle>
                                <CardDescription>
                                    {taskData.scraped_count} items scraped • {taskData.task.identifier}
                                </CardDescription>
                            </div>
                            <Badge 
                                variant={taskData.task.status === 'COMPLETED' ? 'default' : 'secondary'}
                            >
                                {taskData.task.status}
                            </Badge>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {taskData.data && taskData.data.length > 0 ? (
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <h3 className="text-lg font-semibold">
                                        Scraped Content ({taskData.data.length} items)
                                    </h3>
                                </div>
                                <DataTable 
                                    columns={columns} 
                                    data={taskData.data} 
                                    pageSize={10}
                                />
                            </div>
                        ) : (
                            <div className="text-center py-8 text-muted-foreground">
                                No scraped data available for this task
                            </div>
                        )}
                    </CardContent>
                </Card>
            )}

            {!taskId && !taskData && (
                <Card>
                    <CardContent className="flex flex-col items-center justify-center py-8">
                        <Hash className="h-12 w-12 text-muted-foreground mb-4" />
                        <p className="text-muted-foreground text-center">
                            Select a task to view its scraped data
                        </p>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}