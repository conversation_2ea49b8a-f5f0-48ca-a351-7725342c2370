import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, Play, Pause, Trash2 } from 'lucide-react';

interface Schedule {
  id: number;
  name: string;
  frequency: string;
  nextRun: string;
  status: 'active' | 'paused' | 'inactive';
  lastRun?: string;
}

interface SchedulesListProps {
  schedules: Schedule[];
  onToggle?: (id: number) => void;
  onDelete?: (id: number) => void;
}

const SchedulesList: React.FC<SchedulesListProps> = ({
  schedules = [],
  onToggle,
  onDelete
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (schedules.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Schedules</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Calendar className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500">No schedules configured</p>
            <Button className="mt-4" variant="outline">
              Create Schedule
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Schedules ({schedules.length})</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {schedules.map((schedule) => (
            <div
              key={schedule.id}
              className="flex items-center justify-between p-4 border rounded-lg"
            >
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h4 className="font-medium">{schedule.name}</h4>
                  <Badge className={getStatusColor(schedule.status)}>
                    {schedule.status}
                  </Badge>
                </div>
                
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    <span>{schedule.frequency}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    <span>Next: {new Date(schedule.nextRun).toLocaleString()}</span>
                  </div>
                  {schedule.lastRun && (
                    <div className="flex items-center gap-1">
                      <span>Last: {new Date(schedule.lastRun).toLocaleString()}</span>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="flex gap-2">
                {onToggle && (
                  <Button
                    onClick={() => onToggle(schedule.id)}
                    variant="outline"
                    size="sm"
                  >
                    {schedule.status === 'active' ? (
                      <Pause className="w-4 h-4" />
                    ) : (
                      <Play className="w-4 h-4" />
                    )}
                  </Button>
                )}
                {onDelete && (
                  <Button
                    onClick={() => onDelete(schedule.id)}
                    variant="destructive"
                    size="sm"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default SchedulesList;
