import { useState } from "react";
import { useRouter } from "next/navigation";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { TaskStatusBadge } from "./TaskStatusBadge";
import { TikTokTask, TikTokScrapeType } from "@/lib/types/crawler";
import { Eye, MoreHorizontal, Trash2, RotateCcw, X, Edit } from "lucide-react";
import { deleteTask, retryTask, cancelTask, bulkDeleteTasks } from "@/lib/api/crawler";
import { useToast } from "@/components/ui/use-toast";

interface TaskListProps {
    tasks: TikTokTask[];
    onTasksChange?: () => void;
}

export function TaskList({ tasks, onTasksChange }: TaskListProps) {
    const [searchTerm, setSearchTerm] = useState("");
    const [typeFilter, setTypeFilter] = useState<TikTokScrapeType | 'all'>("all");
    const [selectedTasks, setSelectedTasks] = useState<number[]>([]);
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [taskToDelete, setTaskToDelete] = useState<number | null>(null);
    const [bulkDeleteDialogOpen, setBulkDeleteDialogOpen] = useState(false);
    const [loading, setLoading] = useState<{ [key: number]: boolean }>({});
    const router = useRouter();
    const { toast } = useToast();

    const filteredTasks = tasks.filter(task => {
        const matchesSearch = task.identifier.toLowerCase().includes(searchTerm.toLowerCase()) || 
                            task.job_name.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesType = typeFilter === 'all' || task.task_type === typeFilter;
        return matchesSearch && matchesType;
    });

    const handleViewResults = (taskId: number) => {
        router.push(`/crawler/results?task=${taskId}`);
    };

    const handleSelectTask = (taskId: number, checked: boolean) => {
        if (checked) {
            setSelectedTasks(prev => [...prev, taskId]);
        } else {
            setSelectedTasks(prev => prev.filter(id => id !== taskId));
        }
    };

    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            setSelectedTasks(filteredTasks.map(task => task.id));
        } else {
            setSelectedTasks([]);
        }
    };

    const setTaskLoading = (taskId: number, isLoading: boolean) => {
        setLoading(prev => ({ ...prev, [taskId]: isLoading }));
    };

    const handleRetry = async (taskId: number) => {
        try {
            setTaskLoading(taskId, true);
            await retryTask(taskId);
            toast({
                title: "Success",
                description: "Task retry initiated successfully",
            });
            onTasksChange?.();
        } catch (error) {
            toast({
                title: "Error",
                description: "Failed to retry task",
                variant: "destructive",
            });
        } finally {
            setTaskLoading(taskId, false);
        }
    };

    const handleCancel = async (taskId: number) => {
        try {
            setTaskLoading(taskId, true);
            await cancelTask(taskId);
            toast({
                title: "Success",
                description: "Task cancelled successfully",
            });
            onTasksChange?.();
        } catch (error) {
            toast({
                title: "Error",
                description: "Failed to cancel task",
                variant: "destructive",
            });
        } finally {
            setTaskLoading(taskId, false);
        }
    };

    const handleDelete = async (taskId: number) => {
        try {
            setTaskLoading(taskId, true);
            await deleteTask(taskId);
            toast({
                title: "Success",
                description: "Task deleted successfully",
            });
            onTasksChange?.();
        } catch (error) {
            toast({
                title: "Error",
                description: "Failed to delete task",
                variant: "destructive",
            });
        } finally {
            setTaskLoading(taskId, false);
            setDeleteDialogOpen(false);
            setTaskToDelete(null);
        }
    };

    const handleBulkDelete = async () => {
        try {
            await bulkDeleteTasks(selectedTasks);
            toast({
                title: "Success",
                description: `Successfully deleted ${selectedTasks.length} tasks`,
            });
            setSelectedTasks([]);
            onTasksChange?.();
        } catch (error) {
            toast({
                title: "Error",
                description: "Failed to delete tasks",
                variant: "destructive",
            });
        } finally {
            setBulkDeleteDialogOpen(false);
        }
    };

    const canDeleteTask = (task: TikTokTask) => {
        return !['RUNNING', 'PENDING'].includes(task.status);
    };

    const canRetryTask = (task: TikTokTask) => {
        return ['FAILED', 'COMPLETED'].includes(task.status);
    };

    const canCancelTask = (task: TikTokTask) => {
        return ['PENDING', 'RUNNING'].includes(task.status);
    };

    return (
        <div className="space-y-4">
            <div className="flex gap-4 mb-4">
                <div className="flex-1">
                    <Input
                        placeholder="Search tasks..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="max-w-sm"
                    />
                </div>
                <Select
                    value={typeFilter}
                    onValueChange={(value: string) => setTypeFilter(value as TikTokScrapeType | 'all')}
                >
                    <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Filter by type" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">All Types</SelectItem>
                        <SelectItem value="VIDEO">Video</SelectItem>
                        <SelectItem value="USER">User</SelectItem>
                        <SelectItem value="SEARCH">Search</SelectItem>
                    </SelectContent>
                </Select>
                {selectedTasks.length > 0 && (
                    <Button
                        variant="destructive"
                        onClick={() => setBulkDeleteDialogOpen(true)}
                        className="flex items-center gap-2"
                    >
                        <Trash2 className="h-4 w-4" />
                        Delete Selected ({selectedTasks.length})
                    </Button>
                )}
            </div>

            <div className="border rounded-md">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-12">
                                <Checkbox
                                    checked={selectedTasks.length === filteredTasks.length && filteredTasks.length > 0}
                                    onCheckedChange={handleSelectAll}
                                />
                            </TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead>Parameter</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Created</TableHead>
                            <TableHead>Completed</TableHead>
                            <TableHead>Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {filteredTasks.map((task) => (
                            <TableRow key={task.id}>
                                <TableCell>
                                    <Checkbox
                                        checked={selectedTasks.includes(task.id)}
                                        onCheckedChange={(checked) => handleSelectTask(task.id, checked as boolean)}
                                    />
                                </TableCell>
                                <TableCell className="font-medium">
                                    {task.task_type.charAt(0).toUpperCase() + task.task_type.slice(1).toLowerCase()}
                                </TableCell>
                                <TableCell>{task.identifier}</TableCell>
                                <TableCell>
                                    <TaskStatusBadge status={task.status} />
                                </TableCell>
                                <TableCell>
                                    {new Date(task.created_at).toLocaleString()}
                                </TableCell>
                                <TableCell>
                                    {task.completed_at 
                                        ? new Date(task.completed_at).toLocaleString()
                                        : '-'}
                                </TableCell>
                                <TableCell>
                                    <div className="flex items-center gap-2">
                                        {task.status === 'COMPLETED' && (
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleViewResults(task.id)}
                                                className="flex items-center gap-1"
                                            >
                                                <Eye className="h-3 w-3" />
                                                View Results
                                            </Button>
                                        )}
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                    <MoreHorizontal className="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                {canRetryTask(task) && (
                                                    <DropdownMenuItem
                                                        onClick={() => handleRetry(task.id)}
                                                        disabled={loading[task.id]}
                                                    >
                                                        <RotateCcw className="mr-2 h-4 w-4" />
                                                        Retry
                                                    </DropdownMenuItem>
                                                )}
                                                {canCancelTask(task) && (
                                                    <DropdownMenuItem
                                                        onClick={() => handleCancel(task.id)}
                                                        disabled={loading[task.id]}
                                                    >
                                                        <X className="mr-2 h-4 w-4" />
                                                        Cancel
                                                    </DropdownMenuItem>
                                                )}
                                                {canDeleteTask(task) && (
                                                    <DropdownMenuItem
                                                        onClick={() => {
                                                            setTaskToDelete(task.id);
                                                            setDeleteDialogOpen(true);
                                                        }}
                                                        disabled={loading[task.id]}
                                                        className="text-destructive"
                                                    >
                                                        <Trash2 className="mr-2 h-4 w-4" />
                                                        Delete
                                                    </DropdownMenuItem>
                                                )}
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </div>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>

            <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete Task</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete this task? This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={() => taskToDelete && handleDelete(taskToDelete)}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            Delete
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            <AlertDialog open={bulkDeleteDialogOpen} onOpenChange={setBulkDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete Selected Tasks</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete {selectedTasks.length} selected tasks? This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleBulkDelete}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            Delete All
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
}