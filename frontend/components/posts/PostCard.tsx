"use client"

import Link from "next/link";
import Image from "next/image";
import { Post } from "@/lib/types";
import { formatDate } from "@/lib/utils";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Edit, Trash2 } from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { Button } from "../ui/button";
import { useToast } from "../ui/use-toast";
import { RichTextContent } from "./RichTextContent";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { deletePost } from "@/lib/api";
import { useRouter } from "next/navigation";

interface PostCardProps {
  post: Post;
}

export default function PostCard({ post }: PostCardProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const router = useRouter();
  
  if (!post) return null;

  const isAuthor = user?.id === post.author_id;

  const handleDelete = async () => {
    try {
      await deletePost(post.id);
      toast({
        title: "Success",
        description: "Post deleted successfully",
      });
      router.refresh();
    } catch (error) {
      console.error("Failed to delete post:", error);
      toast({
        title: "Error",
        description: "Failed to delete post",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="h-full flex flex-col">
      {post.image && (
        <div className="relative w-full h-[200px]">
          <Image
            src={post.image}
            alt={post.title}
            fill
            className="object-cover rounded-t-lg"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>
      )}
      <CardHeader>
        <div className="flex justify-between items-start">
          <CardTitle>
            <Link href={`/posts/${post.id}`} className="hover:underline">
              {post.title}
            </Link>
          </CardTitle>
          {isAuthor && (
            <div className="flex gap-2">
              <Link href={`/posts/${post.id}/edit`}>
                <Button variant="ghost" size="icon" title="Edit post">
                  <Edit className="h-4 w-4" />
                </Button>
              </Link>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="ghost" size="icon" title="Delete post">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Post</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete this post? This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="text-gray-500 dark:text-gray-400">
          <RichTextContent content={post.content} truncate />
        </div>
        {post.category && (
          <div className="mt-2">
            <span className="text-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded-full">
              {post.category.name}
            </span>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <span className="text-sm text-muted-foreground">By {post.author}</span>
        <span className="text-sm text-muted-foreground">{formatDate(post.created_at)}</span>
      </CardFooter>
    </Card>
  );
}