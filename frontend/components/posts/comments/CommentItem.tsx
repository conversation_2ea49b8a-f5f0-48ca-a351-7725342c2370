import { Comment } from "@/lib/types";
import { formatDate } from "@/lib/utils";
import { deleteComment } from "@/lib/api";
import { useAuth } from "@/hooks/use-auth";

interface CommentItemProps {
  comment: Comment;
  onDelete: () => void;
}

export default function CommentItem({ comment, onDelete }: CommentItemProps) {
  const { user } = useAuth();
  const isAuthor = user?.id === comment.author_id;

  const handleDelete = async () => {
    if (window.confirm("Are you sure you want to delete this comment?")) {
      try {
        await deleteComment(comment.id);
        onDelete();
      } catch (error) {
        console.error("Failed to delete comment:", error);
      }
    }
  };

  return (
    <div className="border-b pb-4">
      <div className="flex justify-between items-start">
        <div>
          <p className="whitespace-pre-wrap">{comment.content}</p>
          <div className="mt-2 text-sm text-gray-500">
            <span>{comment.author}</span>
            <span> • </span>
            <span>{formatDate(comment.created_at)}</span>
          </div>
        </div>
        {isAuthor && (
          <button
            onClick={handleDelete}
            className="text-red-500 hover:text-red-600 text-sm"
          >
            Delete
          </button>
        )}
      </div>
    </div>
  );
}