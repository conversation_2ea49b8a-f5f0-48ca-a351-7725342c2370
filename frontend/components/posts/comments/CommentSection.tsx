"use client";

import { useState, useEffect } from "react";
import CommentForm from "@/components/posts/comments/CommentForm";
import CommentItem from "@/components/posts/comments/CommentItem";
import { Skeleton } from "@/components/ui/skeleton";
import { Comment } from "@/lib/types";
import { api } from "@/lib/axios";

interface CommentSectionProps {
  postId: number;
  initialComments: Comment[];
}

export default function CommentSection({ postId, initialComments }: CommentSectionProps) {
  const [comments, setComments] = useState<Comment[]>(initialComments);
  const [isLoading, setIsLoading] = useState(false);

  const fetchComments = async () => {
    setIsLoading(true);
    try {
      const response = await api.get(`/posts/${postId}`);
      setComments(response.data.comments);
    } catch (error) {
      console.error("Failed to fetch comments:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    setComments(initialComments);
  }, [initialComments]);

  const handleCommentAdded = (newComment: Comment) => {
    setComments([...comments, newComment]);
  };

  const handleCommentDeleted = (commentId: number) => {
    setComments(comments.filter((comment) => comment.id !== commentId));
  };

  return (
    <div className="mt-6 w-full">
      <h2 className="text-md font-bold mb-4">Comments</h2>
      <CommentForm postId={postId} onCommentAdded={handleCommentAdded} />
      <div className="mt-6 space-y-6">
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-[100px] w-full" />
            <Skeleton className="h-[100px] w-full" />
          </div>
        ) : comments?.length > 0 ? (
          comments.map((comment: Comment) => (
            <CommentItem
              key={comment.id}
              comment={comment}
              onDelete={() => handleCommentDeleted(comment.id)}
            />
          ))
        ) : (
          <p className="text-muted-foreground">No comments yet.</p>
        )}
      </div>
    </div>
  );
}