"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form, FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/hooks/use-auth";
import Link from "next/link";
import { api } from "@/lib/axios";
import { Comment } from "@/lib/types";

const formSchema = z.object({
  content: z.string().min(1, "Comment cannot be empty"),
});

interface CommentFormProps {
  postId: number;
  onCommentPosted?: () => void;
  onCommentAdded: (comment: Comment) => void;
}

export default function CommentForm({ postId, onCommentPosted, onCommentAdded }: CommentFormProps) {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      content: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true);
    try {
      const response = await api.post('/comments/', { ...values, post: postId });
      form.reset();
      toast({
        title: "Success",
        description: "Your comment has been posted.",
      });
      onCommentPosted?.();
      onCommentAdded(response.data);
    } catch (error: any) {
      console.error("Failed to post comment:", error);
      toast({
        title: "Error",
        description: error.response?.data?.detail || "Failed to post comment",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!user) {
    return (
      <p className="text-muted-foreground">
        <Link href="/login" className="text-primary underline">Login</Link> to post a comment.
      </p>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="content"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Textarea
                  placeholder="Write your comment..."
                  className="min-h-[100px]"
                  disabled={isSubmitting}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Posting..." : "Post Comment"}
        </Button>
      </form>
    </Form>
  );
}