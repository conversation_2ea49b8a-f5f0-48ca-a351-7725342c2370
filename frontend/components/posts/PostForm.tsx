import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { useState, useCallback, useEffect } from 'react'
import Image from 'next/image'
import { Loader2, X } from 'lucide-react'
import { useRouter } from 'next/navigation';
import { Post, Category } from '@/lib/types';
import { createPost, updatePost, getCategories } from '@/lib/api';
import { useToast } from '../ui/use-toast';
import { RichTextEditor } from '@/components/posts/rich-text-editor'

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ACCEPTED_IMAGE_TYPES = ["image/jpeg", "image/png", "image/webp"];

const formSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(10, 'Content must be at least 10 characters'),
  categoryId: z.number().nullable().optional(),
  image: z
    .instanceof(File)
    .refine(file => file.size <= MAX_FILE_SIZE, 'File size must be less than 5MB')
    .refine(file => ACCEPTED_IMAGE_TYPES.includes(file.type), 'Only .jpg, .jpeg, .png and .webp files are accepted')
    .optional()
})

interface PostFormProps {
  post?: Post;
  isEditing?: boolean;
  onSubmit: (data: FormData) => Promise<void>
  initialValues?: z.infer<typeof formSchema>
  isSubmitting: boolean
}

// The RichTextEditor component now handles the ReactQuill configuration

export default function PostForm({ post, isEditing, onSubmit, initialValues, isSubmitting }: PostFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [categoryId, setCategoryId] = useState<number | null>(post?.category?.id || null);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [error, setError] = useState('');
  const [previewUrl, setPreviewUrl] = useState<string | null>(post?.image || null);
  const [isImageLoading, setIsImageLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: post?.title || '',
      content: post?.content || '',
      categoryId: post?.category?.id || null,
      image: undefined,
    },
  });

  // Effect to update form values when post data changes
  useEffect(() => {
    if (post) {
      form.reset({
        title: post.title,
        content: post.content,
        categoryId: post.category?.id || null,
        image: undefined // Keep as undefined since we handle image separately
      });
      setPreviewUrl(post.image || null);
    }
  }, [post, form]);

  useEffect(() => {
    // Fetch categories when component mounts
    const fetchCategories = async () => {
      try {
        setCategoriesLoading(true);
        const response = await getCategories();
        const categoryData = response.results || [];
        setCategories(categoryData);
      } catch (err) {
        console.error('Error fetching categories:', err);
        toast({
          title: "Error",
          description: "Failed to load categories",
          variant: "destructive",
        });
      } finally {
        setCategoriesLoading(false);
      }
    };
    fetchCategories();
  }, [toast]);

  const handleImageChange = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      try {
        setIsImageLoading(true);
        form.setValue('image', file);
        const url = URL.createObjectURL(file);
        setPreviewUrl(url);
      } catch (error) {
        console.error('Error handling image:', error);
        form.setError('image', {
          type: 'manual',
          message: 'Error processing image. Please try again.'
        });
      } finally {
        setIsImageLoading(false);
      }
    }
  }, [form]);

  const removeImage = useCallback(() => {
    form.setValue('image', undefined);
    setPreviewUrl(null);
  }, [form]);

  const handleSubmit = async (values: z.infer<typeof formSchema>) => {
    const formData = new FormData();
    formData.append('title', values.title);
    formData.append('content', values.content);
    if (values.image instanceof File) {
      formData.append('image', values.image);
    }
    if (values.categoryId) {
      formData.append('category_id', values.categoryId.toString());
    }

    try {
      setLoading(true);
      setError('');

      if (isEditing && post) {
        await updatePost(post.id, formData);
        toast({
          title: "Success",
          description: "Post updated successfully",
        });
      } else {
        await createPost(formData);
        toast({
          title: "Success",
          description: "Post created successfully",
        });
      }
      router.push('/posts');
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'An error occurred';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title</FormLabel>
              <FormControl>
                <Input placeholder="Post title" {...field} disabled={isSubmitting} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="categoryId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Category</FormLabel>
              <FormControl>
                <select
                  {...field}
                  value={field.value || ''}
                  onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : null)}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2"
                  disabled={categoriesLoading || isSubmitting}
                >
                  <option value="">Select a category</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="content"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Content</FormLabel>
              <FormControl>
                <RichTextEditor
                  value={field.value || ''}
                  onChange={field.onChange}
                  placeholder="Write your content..."
                  disabled={isSubmitting}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="image"
          render={({ field: { value, onChange, ...field } }) => (
            <FormItem>
              <FormLabel>Image</FormLabel>
              <FormControl>
                <div className="space-y-4">
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                    disabled={isSubmitting || isImageLoading}
                    {...field}
                  />
                  {previewUrl && (
                    <div className="relative w-full max-w-[300px]">
                      <Image
                        src={previewUrl}
                        alt="Preview"
                        width={300}
                        height={200}
                        className="rounded-md object-cover"
                      />
                      <button
                        type="button"
                        onClick={removeImage}
                        className="absolute -top-2 -right-2 rounded-full bg-destructive p-1 text-white hover:bg-destructive/90"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  )}
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {isEditing ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            <>{isEditing ? 'Update Post' : 'Create Post'}</>
          )}
        </Button>
      </form>
    </Form>
  )
}