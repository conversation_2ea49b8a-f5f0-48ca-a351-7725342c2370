import { useState } from 'react';
import { Category } from '@/lib/types';
import { createCategory, updateCategory } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';

interface CategoryFormProps {
  category?: Category;
  isEditing?: boolean;
  onSuccess?: () => void;
}

export default function CategoryForm({ category, isEditing, onSuccess }: CategoryFormProps) {
  const [name, setName] = useState(category?.name || '');
  const [description, setDescription] = useState(category?.description || '');
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (isEditing && category) {
        await updateCategory(category.id, { name, description });
      } else {
        await createCategory({ name, description });
      }
      if (onSuccess) {
        onSuccess();
      }
      if (!isEditing) {
        setName('');
        setDescription('');
      }
      toast({
        title: isEditing ? "Category Updated" : "Category Created",
        description: isEditing
          ? "The category has been updated successfully."
          : "New category has been created successfully.",
      });
    } catch (err: any) {
      console.error('Error:', err);
      toast({
        title: "Error",
        description: err.response?.data?.detail || "Failed to save category",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Name</Label>
        <Input
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Enter category name"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Enter category description (optional)"
          rows={3}
        />
      </div>

      <Button type="submit" disabled={loading}>
        {loading ? 'Saving...' : isEditing ? 'Update Category' : 'Create Category'}
      </Button>
    </form>
  );
}
