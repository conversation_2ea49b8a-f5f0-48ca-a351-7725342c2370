"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Post } from "@/lib/types"
import { format } from "date-fns"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { DataTableColumnHeader } from "./data-table-column-header"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { ArrowUpRight, Pencil } from "lucide-react"
import { DeletePostButton } from "./delete-post-button"

export const columns: ColumnDef<Post>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="translate-y-[2px]"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px]"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "title",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Title" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {row.getValue("title")}
          </span>
        </div>
      )
    },
  },
  {
    accessorKey: "category",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Category" />
    ),
    cell: ({ row }) => {
      const category = row.getValue("category")
      return (
        <Badge variant="outline">
          {category ? (category as any).name : "Uncategorized"}
        </Badge>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes((row.getValue(id) as any)?.name || "Uncategorized")
    },
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created At" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex w-[100px] items-center">
          {format(new Date(row.getValue("created_at")), "MMM d, yyyy")}
        </div>
      )
    },
  },
  {
    id: "actions",
    cell: ({ row, table }) => {
      const post = row.original
 
      return (
        <div className="flex items-center justify-end gap-2">
          <Button asChild variant="ghost" size="sm">
            <Link href={`/posts/${post.id}/edit`}>
              <Pencil className="h-4 w-4" />
              <span className="sr-only">Edit post</span>
            </Link>
          </Button>
          <Button asChild variant="ghost" size="sm">
            <Link href={`/posts/${post.id}`}>
              <ArrowUpRight className="h-4 w-4" />
              <span className="sr-only">View post</span>
            </Link>
          </Button>
          <DeletePostButton 
            post={post} 
            onDelete={table.options.meta?.refreshData}
          />
        </div>
      )
    },
  },
]
