import { cn } from '@/lib/utils'

interface RichTextContentProps {
  content: string
  className?: string
  truncate?: boolean
}

export function RichTextContent({ content, className, truncate }: RichTextContentProps) {
  return (
    <div 
      className={cn(
        "prose prose-sm sm:prose dark:prose-invert max-w-none",
        truncate && "line-clamp-3",
        className
      )}
      dangerouslySetInnerHTML={{ __html: content }} 
    />
  )
}
