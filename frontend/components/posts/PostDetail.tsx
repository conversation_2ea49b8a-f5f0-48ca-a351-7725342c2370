"use client"

import { useQuery } from "@tanstack/react-query";
import Image from "next/image";
import { getPost } from "@/lib/api";
import { formatDate } from "@/lib/utils";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth } from "@/hooks/use-auth";
import CommentSection from "@/components/posts/comments/CommentSection";
import { Edit } from "lucide-react";
import { RichTextContent } from "./RichTextContent";
import { Button } from "../ui/button";
import Link from "next/link";

interface PostDetailProps {
  postId: string;
}

export default function PostDetail({ postId }: PostDetailProps) {
  const { user } = useAuth();
  const { data: post, isLoading, error } = useQuery({
    queryKey: ['post', postId],
    queryFn: () => getPost(Number(postId)),
  });

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="h-[300px] w-full relative">
          <Skeleton className="h-full w-full" />
        </div>
        <div className="space-y-4">
          <Skeleton className="h-8 w-3/4" />
          <Skeleton className="h-4 w-1/3" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <h2 className="text-2xl font-bold text-red-500">Error loading post</h2>
        <p className="text-muted-foreground">Please try again later</p>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="text-center py-8">
        <h2 className="text-2xl font-bold">Post not found</h2>
      </div>
    );
  }

  const isAuthor = user?.id === post.author_id;

  return (
    <Card className="max-w-4xl mx-auto">
      {post.image && (
        <div className="relative w-full h-[300px]">
          <Image
            src={post.image}
            alt={post.title}
            fill
            className="object-cover rounded-t-lg"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            priority
          />
        </div>
      )}
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-2xl">{post.title}</CardTitle>
            <CardDescription>
              <span className="text-xs text-muted-foreground">By {post.author}</span>
              <span className="text-xs text-muted-foreground ml-4">{formatDate(post.created_at)}</span>
              {post.category && (
                <>
                  <span className="text-xs text-muted-foreground mx-2">•</span>
                  <span className="bg-gray-100 px-2 py-1 rounded-full text-sm">
                    {post.category.name}
                  </span>
                </>
              )}
            </CardDescription>
          </div>
          {isAuthor && (
            <Link href={`/posts/${post.id}/edit`}>
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </Link>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <RichTextContent 
          content={post.content} 
          className="prose-lg" 
        />
      </CardContent>
      <CardFooter className="flex flex-col border-t">
        <CommentSection postId={post.id} initialComments={post.comments} />
      </CardFooter>
    </Card>
  );
}