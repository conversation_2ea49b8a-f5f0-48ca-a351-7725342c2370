"use client"

import { useEffect, useState } from 'react'
import { Skeleton } from '@/components/ui/skeleton'
import { Post } from '@/lib/types'
import { getPosts } from '@/lib/api'
import { DataTable } from './posts-table/data-table'
import { columns } from './posts-table/columns'

interface PostsResponse {
  results: Post[]
  count: number
}

const PAGE_SIZE = 10

export default function PostsList() {
  const [posts, setPosts] = useState<PostsResponse | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)

  const fetchPosts = async (page = 1) => {
    setLoading(true)
    setError(null)
    try {
      const data = await getPosts({ page })
      setPosts(data)
      // Make the refresh function available globally for the delete button
      ;(window as any).__tableRefresh = () => fetchPosts(currentPage)
    } catch (err) {
      console.error('Error fetching posts:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch posts')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPosts(currentPage)
  }, [currentPage])

  if (loading && !posts) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-12" />
        <Skeleton className="h-[640px]" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p>Error: {error}</p>
        </div>
      </div>
    )
  }

  const totalPages = posts ? Math.ceil(posts.count / PAGE_SIZE) : 0

  return (
    <div>
      <DataTable
        columns={columns}
        data={posts?.results || []}
        onPaginationChange={setCurrentPage}
        totalPages={totalPages}
        currentPage={currentPage}
        onRefresh={() => fetchPosts(currentPage)}
      />
    </div>
  )
}