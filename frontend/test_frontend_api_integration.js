#!/usr/bin/env node

/**
 * Frontend API Integration Test
 * Tests all Actor system API calls from the frontend perspective
 */

const axios = require('axios');

// Configure axios to match frontend configuration
const api = axios.create({
    baseURL: 'http://localhost:8000/api',
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
    }
});

class FrontendAPITest {
    constructor() {
        this.testResults = [];
        console.log('🎭 Frontend API Integration Test');
        console.log('================================');
        console.log(`🔗 Backend API: http://localhost:8000/api`);
        console.log(`📅 Started: ${new Date().toISOString()}`);
        console.log('');
    }

    async testAPI(description, apiCall, expectedStatus = 200) {
        try {
            const result = await apiCall();
            const success = result.status === expectedStatus;
            const icon = success ? '✅' : '❌';
            
            console.log(`${icon} ${description} - ${result.status}`);
            if (result.data && typeof result.data === 'object') {
                const keys = Object.keys(result.data);
                console.log(`   Response keys: ${keys.slice(0, 5).join(', ')}${keys.length > 5 ? '...' : ''}`);
            }
            
            this.testResults.push({
                description,
                success,
                status: result.status,
                expectedStatus,
                responseSize: JSON.stringify(result.data).length
            });
            
            return success;
        } catch (error) {
            const status = error.response ? error.response.status : 0;
            const success = status === expectedStatus;
            const icon = success ? '✅' : '❌';
            
            console.log(`${icon} ${description} - ${status || 'ERROR'}`);
            if (error.response?.data) {
                console.log(`   Error: ${JSON.stringify(error.response.data).substring(0, 100)}...`);
            }
            
            this.testResults.push({
                description,
                success,
                status: status || 0,
                expectedStatus,
                error: error.message
            });
            
            return success;
        }
    }

    async runAllTests() {
        console.log('🧪 Testing Frontend API Integration...\n');
        
        // Test public endpoints (should work without auth)
        console.log('📖 Public Endpoints:');
        await this.testAPI(
            'Get Available Platforms',
            () => api.get('/actor/platforms/')
        );
        
        // Test protected endpoints (should return 401 without auth)
        console.log('\n🔒 Protected Endpoints (expecting 401):');
        await this.testAPI(
            'Get Actor Accounts',
            () => api.get('/actor/accounts/list/'),
            401
        );
        
        await this.testAPI(
            'Get Actor Tasks',
            () => api.get('/actor/tasks/list/'),
            401
        );
        
        await this.testAPI(
            'Get Scraped Data',
            () => api.get('/actor/data/'),
            401
        );
        
        await this.testAPI(
            'Get Data Statistics',
            () => api.get('/actor/data/stats/'),
            401
        );
        
        // Test CRUD endpoints (should return 401 without auth)
        console.log('\n🔧 CRUD Endpoints (expecting 401):');
        await this.testAPI(
            'Create Account',
            () => api.post('/actor/accounts/create/', {
                platform: 'tiktok',
                username: 'test',
                password: 'test'
            }),
            401
        );
        
        await this.testAPI(
            'Create Task',
            () => api.post('/actor/tasks/create/', {
                account_id: 1,
                task_type: 'CONTENT_SEARCH',
                task_name: 'Test Task'
            }),
            401
        );
        
        await this.testAPI(
            'Execute Task',
            () => api.post('/actor/tasks/execute/', {
                task_id: 1
            }),
            401
        );
        
        await this.testAPI(
            'Update Account',
            () => api.put('/actor/accounts/1/', {
                username: 'updated'
            }),
            401
        );
        
        await this.testAPI(
            'Delete Account',
            () => api.delete('/actor/accounts/1/delete/'),
            401
        );
        
        // Test authentication endpoints
        console.log('\n🔐 Authentication Endpoints:');
        await this.testAPI(
            'Authenticate Account',
            () => api.post('/actor/accounts/authenticate/', {
                account_id: 1
            }),
            401
        );
        
        // Generate summary
        this.generateSummary();
    }

    generateSummary() {
        const total = this.testResults.length;
        const passed = this.testResults.filter(r => r.success).length;
        const failed = total - passed;
        const successRate = ((passed / total) * 100).toFixed(1);
        
        console.log('\n' + '='.repeat(50));
        console.log('📊 FRONTEND API INTEGRATION TEST SUMMARY');
        console.log('='.repeat(50));
        console.log(`Total API Calls: ${total}`);
        console.log(`✅ Working Correctly: ${passed}`);
        console.log(`❌ Failed: ${failed}`);
        console.log(`📈 Success Rate: ${successRate}%`);
        
        if (failed > 0) {
            console.log('\n⚠️ Failed API Calls:');
            this.testResults
                .filter(r => !r.success)
                .forEach(r => {
                    console.log(`   • ${r.description} - ${r.error || `Status: ${r.status}`}`);
                });
        }
        
        if (successRate >= 90) {
            console.log('\n🎉 FRONTEND API INTEGRATION SUCCESSFUL!');
            console.log('✅ All API endpoints are accessible from frontend');
            console.log('✅ Authentication is properly configured');
            console.log('✅ Frontend can communicate with backend');
        } else {
            console.log('\n⚠️ Some API integration issues detected');
        }
        
        console.log('\n📋 Next Steps:');
        console.log('1. Test with authentication tokens');
        console.log('2. Test real data creation and retrieval');
        console.log('3. Verify frontend UI components work with APIs');
        
        return successRate >= 90;
    }
}

// Check if backend is running
async function checkBackend() {
    try {
        const response = await axios.get('http://localhost:8000/api/actor/health/', { timeout: 5000 });
        console.log('🟢 Backend server is running');
        return true;
    } catch (error) {
        console.log('🔴 Backend server is not running or not accessible');
        console.log('   Please start the backend server:');
        console.log('   cd backend && source venv/bin/activate && python3 manage.py runserver');
        return false;
    }
}

// Main execution
async function main() {
    const backendRunning = await checkBackend();
    if (!backendRunning) {
        process.exit(1);
    }
    
    console.log('');
    
    const test = new FrontendAPITest();
    const success = await test.runAllTests();
    
    console.log(`\n🏁 Test completed at: ${new Date().toISOString()}`);
    process.exit(success ? 0 : 1);
}

main().catch(error => {
    console.error('❌ Test failed with error:', error.message);
    process.exit(1);
});
