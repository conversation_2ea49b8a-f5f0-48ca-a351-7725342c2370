// Simple test to verify API connectivity from frontend to backend
const axios = require('axios');

const baseURL = 'http://localhost:8000/api';

async function testEndpoints() {
    console.log('🧪 Testing Frontend to Backend Connectivity');
    console.log('='.repeat(50));
    
    const endpoints = [
        '/actor/health/',
        '/actor/scraped-data/',
        '/actor/scraped-data/by_task/?task_id=1'
    ];
    
    for (const endpoint of endpoints) {
        try {
            console.log(`\n🔍 Testing: ${baseURL}${endpoint}`);
            const response = await axios.get(`${baseURL}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json'
                },
                timeout: 5000
            });
            
            console.log(`✅ Status: ${response.status}`);
            console.log(`✅ Data: ${JSON.stringify(response.data).substring(0, 100)}...`);
            
        } catch (error) {
            console.log(`❌ Error: ${error.message}`);
            if (error.response) {
                console.log(`❌ Status: ${error.response.status}`);
                console.log(`❌ Data: ${JSON.stringify(error.response.data)}`);
            }
        }
    }
    
    console.log('\n🏁 Test completed!');
}

testEndpoints();
