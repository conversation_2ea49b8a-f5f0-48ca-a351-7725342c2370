.rich-text-editor .ql-toolbar.ql-snow {
  border: 1px solid hsl(var(--input));
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  background-color: hsl(var(--background));
}

.rich-text-editor .ql-container.ql-snow {
  border: 1px solid hsl(var(--input));
  border-top: 0;
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  min-height: 200px;
}

.rich-text-editor .ql-editor {
  min-height: 200px;
  font-size: 1rem;
  line-height: 1.5;
  padding: 1rem;
}

/* Typography */
.rich-text-editor .ql-editor h1,
.rich-text-editor .ql-editor h2,
.rich-text-editor .ql-editor h3,
.rich-text-editor .ql-editor h4,
.rich-text-editor .ql-editor h5,
.rich-text-editor .ql-editor h6 {
  font-weight: 600;
  line-height: 1.25;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.rich-text-editor .ql-editor p {
  margin-bottom: 1em;
}

.rich-text-editor .ql-editor ul,
.rich-text-editor .ql-editor ol {
  padding-left: 1.5em;
  margin-bottom: 1em;
}

/* Placeholder */
.rich-text-editor .ql-editor.ql-blank::before {
  color: hsl(var(--muted-foreground));
  font-style: normal;
}

/* Icons and controls */
.rich-text-editor .ql-snow .ql-stroke {
  stroke: hsl(var(--foreground));
}

.rich-text-editor .ql-snow .ql-fill {
  fill: hsl(var(--foreground));
}

.rich-text-editor .ql-snow .ql-picker {
  color: hsl(var(--foreground));
}

/* Hover and active states */
.rich-text-editor .ql-snow.ql-toolbar button:hover,
.rich-text-editor .ql-snow .ql-toolbar button:hover,
.rich-text-editor .ql-snow.ql-toolbar button:focus,
.rich-text-editor .ql-snow .ql-toolbar button:focus,
.rich-text-editor .ql-snow.ql-toolbar button.ql-active,
.rich-text-editor .ql-snow .ql-toolbar button.ql-active {
  color: hsl(var(--primary));
}

.rich-text-editor .ql-snow.ql-toolbar button:hover .ql-stroke,
.rich-text-editor .ql-snow .ql-toolbar button:hover .ql-stroke,
.rich-text-editor .ql-snow.ql-toolbar button:focus .ql-stroke,
.rich-text-editor .ql-snow .ql-toolbar button:focus .ql-stroke,
.rich-text-editor .ql-snow.ql-toolbar button.ql-active .ql-stroke,
.rich-text-editor .ql-snow .ql-toolbar button.ql-active .ql-stroke {
  stroke: hsl(var(--primary));
}

.rich-text-editor .ql-snow.ql-toolbar button:hover .ql-fill,
.rich-text-editor .ql-snow .ql-toolbar button:hover .ql-fill,
.rich-text-editor .ql-snow.ql-toolbar button:focus .ql-fill,
.rich-text-editor .ql-snow .ql-toolbar button:focus .ql-fill,
.rich-text-editor .ql-snow.ql-toolbar button.ql-active .ql-fill,
.rich-text-editor .ql-snow .ql-toolbar button.ql-active .ql-fill {
  fill: hsl(var(--primary));
}
