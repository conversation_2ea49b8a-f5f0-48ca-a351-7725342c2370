import { api } from './axios';
import { Post, Category} from './types';

export async function getPosts(params?: { page?: number; search?: string }): Promise<{ results: Post[]; count: number }> {
  try {
    const response = await api.get('/blog/posts/', { params });
    console.log('API Response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error in getPosts:', error);
    throw error;
  }
}

export async function getPost(id: number): Promise<Post> {
  try {
    const response = await api.get(`/blog/posts/${id}/`);
    return response.data;
  } catch (error) {
    console.error(`Error in getPost(${id}):`, error);
    throw error;
  }
}

export async function createPost(data: FormData): Promise<Post> {
  try {
    const response = await api.post('/blog/posts/', data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error in createPost:', error);
    throw error;
  }
}

export async function updatePost(id: number, data: FormData): Promise<Post> {
  try {
    const response = await api.put(`/blog/posts/${id}/`, data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    console.error(`Error in updatePost(${id}):`, error);
    throw error;
  }
}

export async function deletePost(id: number): Promise<void> {
  try {
    await api.delete(`/blog/posts/${id}/`);
  } catch (error) {
    console.error(`Error in deletePost(${id}):`, error);
    throw error;
  }
}

// Category endpoints
export async function getCategories(): Promise<{ results: Category[] }> {
  const response = await api.get('/blog/categories/');
  return response.data;
}

export async function getCategory(id: number): Promise<Category> {
  const response = await api.get(`/blog/categories/${id}/`);
  return response.data;
}

export async function createCategory(data: Pick<Category, 'name' | 'description'>): Promise<Category> {
  const response = await api.post('/blog/categories/', data);
  return response.data;
}

export async function updateCategory(id: number, data: Partial<Pick<Category, 'name' | 'description'>>): Promise<Category> {
  const response = await api.put(`/blog/categories/${id}/`, data);
  return response.data;
}

export async function deleteCategory(id: number): Promise<void> {
  await api.delete(`/blog/categories/${id}/`);
}

// Comment endpoints
export const createComment = (data: { content: string; post: number }) =>
  api.post<Comment>('/blog/comments/', data);

export const deleteComment = (id: number) =>
  api.delete(`/blog/comments/${id}/`);


