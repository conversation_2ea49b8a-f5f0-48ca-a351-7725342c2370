"use client"

import axios from 'axios';

const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';
const apiClient= axios.create({
  baseURL,
  withCredentials: true,
});

// Add a request interceptor to handle content type
apiClient.interceptors.request.use((config) => {
  // Defensive: Ensure data is always an object for POST, PUT, PATCH
  const method = config.method?.toUpperCase();
  if (["POST", "PUT", "PATCH"].includes(method || "") && (config.data === undefined || config.data === null)) {
    config.data = {};
  }
  // Don't set Content-Type for FormData
  if (!(config.data instanceof FormData)) {
    config.headers['Content-Type'] = 'application/json';
  }
  return config;
});

// Only add interceptors in the browser environment
if (typeof window !== 'undefined') {
  apiClient.interceptors.request.use(
    (config) => {
      try {
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers.Authorization = `JWT ${token}`;
        }
      } catch (error) {
        console.error('Error accessing localStorage:', error);
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  apiClient.interceptors.response.use(
    (response) => response,
    async (error) => {
      // Defensive: error.config may be undefined
      const originalRequest = error.config || {};
      if (error.config && error.config.url?.includes('/auth/jwt/create/')) {
        return Promise.reject(error);
      }
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;
        try {
          const refreshToken = localStorage.getItem('refresh_token');
          if (!refreshToken) {
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            window.location.href = '/login';
            throw new Error('No refresh token available');
          }
          const response = await axios.post(`${baseURL}/auth/jwt/refresh/`, {
            refresh: refreshToken,
          });
          localStorage.setItem('access_token', response.data.access);
          originalRequest.headers = originalRequest.headers || {};
          originalRequest.headers.Authorization = `JWT ${response.data.access}`;
          return apiClient(originalRequest);
        } catch (refreshError) {
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          window.location.href = '/login';
          return Promise.reject(refreshError);
        }
      }
      return Promise.reject(error);
    }
  );
}

export const crawlerApi = {
  // Project endpoints
  getProjects: (params: any = {}) => apiClient.get('/crawlers/projects/', { params }),
  createProject: (data: any) => apiClient.post('/crawlers/projects/', data),
  getProject: (id: string) => apiClient.get(`/crawlers/projects/${id}`),
  updateProject: (id: string, data: any) => apiClient.patch(`/crawlers/projects/${id}/`, data),
  deleteProject: (id: string) => apiClient.delete(`/crawlers/projects/${id}`),
  
  // Schedule endpoints
  getSchedules: (params: any = {}) => apiClient.get('/crawlers/schedules/', { params }),
  createSchedule: (projectId: string, data: any) => apiClient.post(`/crawlers/schedules/`, { ...data, project: projectId }),
  updateSchedule: (id: string, data: any) => apiClient.patch(`/crawlers/schedules/${id}/`, data),
  runScheduleNow: (scheduleId: string) => apiClient.post(`/crawlers/schedules/${scheduleId}/run_now/`),
  deleteSchedule: (id: string) => apiClient.delete(`/crawlers/schedules/${id}/`),
  getSchedule: (id: string) => apiClient.get(`/crawlers/schedules/${id}/`),
  
  // Task endpoints
  getTasks: (params: any = {}) => apiClient.get('/crawlers/tasks', { params }),
  getTask: (id: string) => apiClient.get(`/crawlers/tasks/${id}`),
  cancelTask: (id: string) => apiClient.post(`/crawlers/tasks/${id}/cancel`),
  
  // Crawled data endpoints
  getCrawledData: (taskId: string) => apiClient.get(`/crawlers/tasks/${taskId}/data`),
  getDataPreview: (dataId: string) => apiClient.get(`/crawlers/crawled-data/${dataId}/preview`),
  // Get all crawled data for a project
  getProjectCrawledData: ({ projectId, page = 1, page_size = 10 }: { projectId: string, page?: number, page_size?: number }) =>
    apiClient.get(`/crawlers/crawled-data/?project_id=${projectId}&page=${page}&page_size=${page_size}`),
  
  // Monitoring endpoints
  getDashboardStats: () => apiClient.get('/crawlers/monitoring/dashboard'),
  getActiveTasks: () => apiClient.get('/crawlers/monitoring/active-tasks'),
  getPlatformStats: () => apiClient.get('/crawlers/monitoring/platform-stats'),
  getTaskHistory: () => apiClient.get('/crawlers/monitoring/task-history'),
  
  // Authentication
  login: (credentials: { email: string; password: string }) => 
    apiClient.post('/auth/login', credentials)
};

export default apiClient;