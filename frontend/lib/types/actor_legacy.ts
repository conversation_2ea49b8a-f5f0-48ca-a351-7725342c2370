export type ActorTaskType = 'MY_VIDEOS' | 'MY_FOLLOWERS' | 'MY_FOLLOWING' | 'MY_LIKES' | 'FEED_SCRAPE' | 'TARGETED_USER' | 'HASHTAG_ANALYSIS' | 'COMPETITOR_ANALYSIS';

export type TaskStatus = 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | 'PAUSED';

export type AccountStatus = 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'BANNED';

export interface TikTokAccount {
    id: number;
    user_id: number;
    username: string;
    tiktok_username: string;
    email: string | null;
    status: AccountStatus;
    last_login: string | null;
    session_expires_at: string | null;
    created_at: string;
    updated_at: string;
    login_attempts: number;
    is_blocked: boolean;
    blocked_until: string | null;
    session_valid: boolean;
}

// Generic Actor Account interface
export interface ActorAccount {
    id: number;
    user_id: number;
    platform: string;
    username: string;
    email: string | null;
    status: AccountStatus;
    last_login: string | null;
    session_expires_at: string | null;
    created_at: string;
    updated_at: string;
    login_attempts: number;
    is_blocked: boolean;
    blocked_until: string | null;
    session_valid: boolean;
}

export interface ActorTask {
    id: number;
    user_id: number;
    username: string;
    tiktok_account: number;
    tiktok_username: string;
    task_name: string;
    task_type: ActorTaskType;
    target_identifier: string | null;
    status: TaskStatus;
    max_items: number | null;
    scrape_interval: number;
    use_stealth_mode: boolean;
    randomize_delays: boolean;
    start_date: string | null;
    end_date: string | null;
    celery_task_id: string | null;
    created_at: string;
    updated_at: string;
    started_at: string | null;
    completed_at: string | null;
    error_message: string | null;
    items_scraped: number;
    total_items_found: number | null;
    progress_percentage: number;
    scraped_data_count: number;
    estimated_completion: string | null;
}

export interface TikTokSession {
    id: number;
    account: TikTokAccount;
    session_id: string;
    session_data: any | null;
    user_agent: string | null;
    proxy_used: string | null;
    requests_made: number;
    successful_requests: number;
    failed_requests: number;
    captcha_challenges: number;
    rate_limit_hits: number;
    detection_score: number;
    is_healthy: boolean;
    created_at: string;
    expires_at: string | null;
    last_activity: string | null;
}

export interface ScrapedData {
    id: number;
    task: ActorTask;
    data_type: 'PROFILE' | 'VIDEO' | 'FOLLOWER' | 'FOLLOWING';
    tiktok_id: string | null;
    author_username: string | null;
    content: any;
    is_complete: boolean;
    quality_score: number | null;
    scraped_at: string;
}

export interface TaskMetrics {
    success_rate: number;
    average_completion_time: number;
    total_tasks: number;
    pending_tasks: number;
    running_tasks: number;
    completed_tasks: number;
    failed_tasks: number;
    total_items_scraped: number;
    active_accounts: number;
}

export interface HealthStatus {
    status: 'healthy' | 'warning' | 'critical';
    message: string;
    metrics: {
        success_rate: number;
        error_rate: number;
        average_response_time: number;
        active_sessions: number;
        healthy_accounts: number;
    };
}

export interface TikTokProfile {
    id: string;
    username: string;
    display_name: string;
    bio: string;
    follower_count: number;
    following_count: number;
    video_count: number;
    likes_count: number;
    is_verified: boolean;
    profile_image_url: string;
    created_time: string;
}

export interface TikTokVideo {
    id: string;
    author: string;
    description: string;
    video_url: string;
    thumbnail_url: string;
    duration: number;
    view_count: number;
    like_count: number;
    comment_count: number;
    share_count: number;
    created_time: string;
    music?: {
        title: string;
        author: string;
        url: string;
    };
}

export interface TikTokFollower {
    id: string;
    username: string;
    display_name: string;
    profile_image_url: string;
    follower_count: number;
    following_count: number;
    is_verified: boolean;
    followed_at: string;
}

export interface SessionHealth {
    session_id: number;
    account_username: string;
    health_score: number;
    status: 'healthy' | 'warning' | 'critical';
    last_activity: string;
    issues: string[];
}