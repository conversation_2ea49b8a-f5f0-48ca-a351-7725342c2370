export type TikTokScrapeType = 'VIDEO' | 'USER' | 'SEARCH';

export type TaskStatus = 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';

export interface TikTokTask {
    id: number;
    job_name: string;
    task_type: TikTokScrapeType;
    identifier: string;
    status: TaskStatus;
    celery_task_id: string | null;
    created_at: string;
    updated_at: string;
    completed_at: string | null;
    error_message: string | null;
    start_date: string | null;
    end_date: string | null;
}

export interface TikTokVideo {
    id: number;
    video_url: string;
    task_id: string | null;
    status: TaskStatus;
    created_at: string;
    completed_at: string | null;
    data: any | null;
    processed_data: any | null;
    error_message: string | null;
}

export interface TikTokUser {
    id: number;
    username: string;
    user_id: string | null;
    last_scraped: string;
    profile_data: any | null;
}

export interface TaskMetrics {
    success_rate: number;
    average_duration: number;
    total_tasks: number;
    failed_tasks: number;
}

export interface HealthStatus {
    status: 'healthy' | 'warning' | 'critical';
    message: string;
    metrics: {
        success_rate: number;
        error_rate: number;
        average_response_time: number;
    };
}

// Scraped Data Types
export type ScrapedDataType = 'VIDEO' | 'USER' | 'SEARCH_RESULT';

export interface ScrapedData {
    id: number;
    task_id: number;
    data_type: ScrapedDataType;
    content: any;
    scraped_at: string;
}

export interface TikTokContent {
    id: string;
    desc: string;
    author: string;
    caption?: string;
    stats: {
        likes: number | null;
        comments: number | null;
        shares?: number | null;
        views?: number | null;
    };
    video_id?: string;
    created_time?: string;
    duration?: number;
    music?: {
        title: string;
        author: string;
    };
}

export interface ScrapedDataSummary {
    total_items: number;
    by_type: {
        VIDEO: number;
        USER: number;
        SEARCH_RESULT: number;
    };
    recent_items: ScrapedData[];
    tasks_with_data: number;
}

export interface TaskScrapedData {
    task: TikTokTask;
    scraped_count: number;
    data: TikTokContent[];
}