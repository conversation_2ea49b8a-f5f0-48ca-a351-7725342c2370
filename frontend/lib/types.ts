export interface User {
    id: number;
    username: string;
    email: string;
    first_name?: string;
    last_name?: string;
    avatar?: string;
  }
  
  export interface Category {
    id: number;
    name: string;
    description: string;
    created_at: string;
}
  
  export interface Post {
    id: number;
    title: string;
    content: string;
    image: string | null;
    author: string;
    author_id: number;
    category: Category | null;
    category_id?: number;
    created_at: string;
    updated_at: string;
    comments: Comment[];
  }
  
  export interface Comment {
    id: number;
    content: string;
    author: string;
    author_id: number;
    created_at: string;
    post: number;
  }