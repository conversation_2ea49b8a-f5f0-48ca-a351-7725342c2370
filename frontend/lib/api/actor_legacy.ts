import { api } from "@/lib/axios";
import type { HealthStatus, TikTokAccount, ActorTask, ActorTaskType, TaskMetrics, ScrapedData, TikTokSession } from "@/lib/types/actor";

const BASE_URL = "/actor";

// Health Check
export async function getHealthStatus(): Promise<HealthStatus> {
    const response = await api.get(`${BASE_URL}/health/`);
    return response.data;
}

// Account Management
export async function getAccounts(): Promise<TikTokAccount[]> {
    const response = await api.get(`${BASE_URL}/accounts/`);
    return response.data.results || response.data;
}

export async function getAccount(accountId: number): Promise<TikTokAccount> {
    const response = await api.get(`${BASE_URL}/accounts/${accountId}/`);
    return response.data;
}

export async function createAccount(data: {
    tiktok_username: string;
    tiktok_user_id?: string;
    email?: string;
    phone?: string;
    password?: string;
    bio?: string;
    profile_image_url?: string;
}): Promise<TikTokAccount> {
    const response = await api.post(`${BASE_URL}/accounts/`, data);
    return response.data;
}

export async function updateAccount(accountId: number, data: Partial<TikTokAccount>): Promise<TikTokAccount> {
    const response = await api.patch(`${BASE_URL}/accounts/${accountId}/`, data);
    return response.data;
}

export async function deleteAccount(accountId: number): Promise<void> {
    await api.delete(`${BASE_URL}/accounts/${accountId}/`);
}

// Task Management
export async function getTasks(): Promise<ActorTask[]> {
    const response = await api.get(`${BASE_URL}/tasks/`);
    return response.data.results || response.data;
}

export async function getTask(taskId: number): Promise<ActorTask> {
    const response = await api.get(`${BASE_URL}/tasks/${taskId}/`);
    return response.data;
}

export async function createTask(data: {
    account_id: number;
    task_type: ActorTaskType;
    target_username?: string;
    target_user_id?: string;
    keywords?: string;
    start_date?: string;
    end_date?: string;
    max_items?: number;
    config?: any;
}): Promise<ActorTask> {
    // Convert frontend fields to backend fields
    const { account_id, target_username, target_user_id, keywords, start_date, end_date, task_type, ...rest } = data;

    // Use target_username if provided, otherwise target_user_id, or keywords for content search
    const target_identifier = target_username || target_user_id || undefined;
    
    // Generate task name based on task type and target
    let task_name = '';
    switch (task_type) {
        case 'MY_VIDEOS':
            task_name = 'My Videos';
            break;
        case 'MY_FOLLOWERS':
            task_name = 'My Followers';
            break;
        case 'MY_FOLLOWING':
            task_name = 'My Following';
            break;
        case 'MY_LIKES':
            task_name = 'My Liked Videos';
            break;
        case 'FEED_SCRAPE':
            task_name = 'Feed Scraping';
            break;
        case 'TARGETED_USER':
            task_name = `Targeted User: ${target_identifier || 'Unknown'}`;
            break;
        case 'HASHTAG_ANALYSIS':
            task_name = `Hashtag Analysis: ${target_identifier || 'Unknown'}`;
            break;
        case 'COMPETITOR_ANALYSIS':
            task_name = `Competitor Analysis: ${target_identifier || 'Unknown'}`;
            break;
        case 'CONTENT_SEARCH':
            task_name = `Content Search: ${keywords || 'No keywords'}`;
            break;
        default:
            task_name = `${task_type} Task`;
    }
    
    const payload = {
        tiktok_account_id: account_id,
        task_name,
        task_type,
        target_identifier,
        keywords,
        start_date,
        end_date,
        ...rest
    };
    
    // Remove undefined fields
    Object.keys(payload).forEach(key => {
        if ((payload as any)[key] === undefined) {
            delete (payload as any)[key];
        }
    });
    
    const response = await api.post(`${BASE_URL}/tasks/`, payload);
    return response.data;
}

export async function updateTask(taskId: number, data: Partial<ActorTask>): Promise<ActorTask> {
    const response = await api.patch(`${BASE_URL}/tasks/${taskId}/`, data);
    return response.data;
}

export async function retryTask(taskId: number): Promise<{ message: string }> {
    const response = await api.post(`${BASE_URL}/tasks/${taskId}/retry/`);
    return response.data;
}

export async function cancelTask(taskId: number): Promise<{ message: string }> {
    const response = await api.post(`${BASE_URL}/tasks/${taskId}/cancel/`);
    return response.data;
}

export async function deleteTask(taskId: number): Promise<void> {
    await api.delete(`${BASE_URL}/tasks/${taskId}/`);
}

// Task Statistics
export async function getTaskStatistics(): Promise<TaskMetrics> {
    const response = await api.get(`${BASE_URL}/tasks/stats/`);
    return response.data;
}

// Helper function for retrying API calls
async function retryApiCall<T>(apiCall: () => Promise<T>, maxRetries = 3, delay = 1000): Promise<T> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return await apiCall();
        } catch (error: any) {
            if (attempt === maxRetries) {
                throw error;
            }

            // Only retry on network errors or 404s (which might be temporary)
            if (error.code === 'NETWORK_ERROR' || error.response?.status === 404) {
                console.warn(`API call failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
                delay *= 2; // Exponential backoff
            } else {
                throw error;
            }
        }
    }
    throw new Error('Max retries exceeded');
}

// Scraped Data Operations
export async function getScrapedData(params?: {
    task_id?: number;
    data_type?: 'PROFILE' | 'VIDEO' | 'FOLLOWER' | 'FOLLOWING';
    page?: number;
    page_size?: number;
}): Promise<{ results: ScrapedData[]; count: number; next: string | null; previous: string | null }> {
    const queryParams = new URLSearchParams();
    if (params?.task_id) queryParams.append('task_id', params.task_id.toString());
    if (params?.data_type) queryParams.append('data_type', params.data_type);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.page_size) queryParams.append('page_size', params.page_size.toString());

    return retryApiCall(async () => {
        const response = await api.get(`${BASE_URL}/scraped-data/?${queryParams.toString()}`);
        return response.data;
    });
}

export async function getScrapedDataByTask(taskId: number): Promise<{ task: ActorTask; scraped_count: number; data: any[] }> {
    return retryApiCall(async () => {
        const response = await api.get(`${BASE_URL}/scraped-data/by_task/?task_id=${taskId}`);
        return response.data;
    });
}

// Session Management
export async function getSessions(): Promise<TikTokSession[]> {
    const response = await api.get(`${BASE_URL}/sessions/`);
    return response.data.results || response.data;
}

export async function getSession(sessionId: number): Promise<TikTokSession> {
    const response = await api.get(`${BASE_URL}/sessions/${sessionId}/`);
    return response.data;
}

export async function createSession(data: {
    account_id: number;
    user_agent?: string;
    proxy_used?: string;
}): Promise<TikTokSession> {
    const response = await api.post(`${BASE_URL}/sessions/`, data);
    return response.data;
}

export async function updateSession(sessionId: number, data: Partial<TikTokSession>): Promise<TikTokSession> {
    const response = await api.patch(`${BASE_URL}/sessions/${sessionId}/`, data);
    return response.data;
}

export async function deleteSession(sessionId: number): Promise<void> {
    await api.delete(`${BASE_URL}/sessions/${sessionId}/`);
}

// Session Actions
export async function markSessionUnhealthy(sessionId: number): Promise<{ message: string }> {
    const response = await api.post(`${BASE_URL}/sessions/${sessionId}/mark_unhealthy/`);
    return response.data;
}

export async function resetSessionMetrics(sessionId: number): Promise<{ message: string }> {
    const response = await api.post(`${BASE_URL}/sessions/${sessionId}/reset_metrics/`);
    return response.data;
}

// Legacy TikTok Login
export async function loginToTikTok(data: {
    tiktok_username: string;
    tiktok_password: string;
    use_2fa?: boolean;
    two_factor_code?: string;
    remember_session?: boolean;
}): Promise<{ message: string; task_id: string; status: string }> {
    const response = await api.post(`${BASE_URL}/login/`, data);
    return response.data;
}

// Simple TikTok Login (Working Version)
export async function testSimpleLogin(data: {
    username: string;
    password: string;
    create_account?: boolean;
}): Promise<{
    success: boolean;
    error?: string;
    cookies_count?: number;
    videos_found?: number;
    sample_authors?: string[];
    session_info?: any;
}> {
    const response = await api.post(`${BASE_URL}/simple-login/`, data);
    return response.data;
}

// Prabowo-specific function removed - use scrapeContentByKeyword instead

// Dynamic Content Scraping by Keyword (Enhanced with Session Support)
export async function scrapeContentByKeyword(data: {
    keywords: string;
    account_id?: string;
    start_date?: string;
    end_date?: string;
    max_videos?: number;
    filters?: {
        min_likes?: string;
        content_type?: string;
        date_range?: string;
        author_type?: string;
    };
}): Promise<{
    success: boolean;
    videos: any[];
    total_found: number;
    keywords: string;
    session_info?: any;
    error?: string;
}> {
    const response = await api.post(`${BASE_URL}/scrape-keyword/`, data);
    return response.data;
}

// Search History
export async function getSearchHistory(): Promise<string[]> {
    const response = await api.get(`${BASE_URL}/search-history/`);
    return response.data.history || [];
}

// Save Search Preset
export async function saveSearchPreset(data: {
    name: string;
    keywords: string[];
    filters: any;
}): Promise<{ success: boolean; id: string }> {
    const response = await api.post(`${BASE_URL}/search-presets/`, data);
    return response.data;
}

// Get Search Presets
export async function getSearchPresets(): Promise<any[]> {
    const response = await api.get(`${BASE_URL}/search-presets/`);
    return response.data.presets || [];
}

// Session Management (Enhanced)

// Enhanced session management functions
export async function createEnhancedSession(data: {
    account_username: string;
    login_method: string;
}): Promise<any> {
    const response = await api.post(`${BASE_URL}/sessions/`, data);
    return response.data;
}

export async function testSession(sessionId: string): Promise<{
    success: boolean;
    health_score: number;
    error?: string;
}> {
    const response = await api.post(`${BASE_URL}/sessions/${sessionId}/test/`);
    return response.data;
}

// Enhanced task management functions
export async function createEnhancedTask(data: {
    task_type: string;
    parameters: any;
}): Promise<any> {
    const response = await api.post(`${BASE_URL}/tasks/`, data);
    return response.data;
}

// Generic Content Statistics
export async function getContentStats(): Promise<{
    success: boolean;
    stats: {
        total_videos: number;
        total_likes: number;
        total_comments: number;
        total_shares: number;
        unique_authors: number;
        accounts: number;
        active_sessions: number;
        keywords_searched: string[];
        last_updated: string;
    };
    error?: string;
}> {
    const response = await api.get(`${BASE_URL}/content-stats/`);
    return response.data;
}

// Active Sessions Management
export async function getActiveSessions(): Promise<{
    success: boolean;
    results: Array<{
        id: string;
        username: string;
        tiktok_username: string;
        status: string;
        login_time: string;
        last_activity: string;
        expires_at: string;
        cookies_count: number;
        is_healthy: boolean;
        health_score: number;
    }>;
    total_active: number;
}> {
    const response = await api.get(`${BASE_URL}/active-sessions/`);
    return response.data;
}