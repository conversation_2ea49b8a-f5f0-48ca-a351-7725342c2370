/**
 * Task CRUD API Client
 * 
 * Complete Create, Read, Update, Delete operations for TikTok Actor Tasks
 */

import { api } from '../axios';

const BASE_URL = '/api/actor';

// ================================
// TYPES
// ================================

export interface TaskCreateData {
    task_name: string;
    task_type: string;
    tiktok_account_id?: number;
    target_identifier?: string;
    keywords?: string;
    max_items?: number;
    start_date?: string;
    end_date?: string;
    use_stealth_mode?: boolean;
    randomize_delays?: boolean;
    scrape_interval?: number;
    task_parameters?: Record<string, any>;
}

export interface TaskUpdateData {
    task_name?: string;
    target_identifier?: string;
    keywords?: string;
    max_items?: number;
    scrape_interval?: number;
    use_stealth_mode?: boolean;
    randomize_delays?: boolean;
    task_parameters?: Record<string, any>;
    status?: string;
    progress?: number;
    items_scraped?: number;
    total_items_found?: number;
    error_message?: string;
    start_date?: string;
    end_date?: string;
}

export interface Task {
    id: number;
    task_name: string;
    task_type: string;
    status: string;
    target_identifier?: string;
    keywords?: string;
    max_items: number;
    progress: number;
    items_scraped: number;
    total_items_found: number;
    progress_percentage: number;
    created_at: string;
    updated_at: string;
    // Detailed fields (only in detailed view)
    tiktok_account_id?: number;
    tiktok_account_username?: string;
    scrape_interval?: number;
    use_stealth_mode?: boolean;
    randomize_delays?: boolean;
    start_date?: string;
    end_date?: string;
    task_parameters?: Record<string, any>;
    celery_task_id?: string;
    started_at?: string;
    completed_at?: string;
    error_message?: string;
}

export interface TaskListResponse {
    success: boolean;
    results: Task[];
    total_count: number;
    limit: number;
    offset: number;
}

export interface TaskResponse {
    success: boolean;
    task: Task;
    message?: string;
}

export interface TaskStatistics {
    total_tasks: number;
    status_counts: Record<string, number>;
    type_counts: Record<string, number>;
    recent_tasks_7_days: number;
    completion_rate: number;
    average_items_scraped: number;
    most_used_task_type?: string;
}

// ================================
// CREATE OPERATIONS
// ================================

export async function createTask(data: TaskCreateData): Promise<TaskResponse> {
    const response = await api.post(`${BASE_URL}/tasks/create/`, data);
    return response.data;
}

// ================================
// READ OPERATIONS
// ================================

export async function getTasks(params?: {
    status?: string;
    task_type?: string;
    limit?: number;
    offset?: number;
    search?: string;
}): Promise<TaskListResponse> {
    const response = await api.get(`${BASE_URL}/tasks/`, { params });
    return response.data;
}

export async function getTaskById(taskId: number): Promise<TaskResponse> {
    const response = await api.get(`${BASE_URL}/tasks/${taskId}/`);
    return response.data;
}

export async function getTaskStatistics(): Promise<{ success: boolean; statistics: TaskStatistics }> {
    const response = await api.get(`${BASE_URL}/tasks/statistics/`);
    return response.data;
}

// ================================
// UPDATE OPERATIONS
// ================================

export async function updateTask(taskId: number, data: TaskUpdateData): Promise<TaskResponse> {
    const response = await api.patch(`${BASE_URL}/tasks/${taskId}/update/`, data);
    return response.data;
}

export async function updateTaskStatus(taskId: number, status: string): Promise<TaskResponse> {
    return updateTask(taskId, { status });
}

export async function updateTaskProgress(
    taskId: number, 
    progress: number, 
    itemsScraped?: number, 
    totalItems?: number
): Promise<TaskResponse> {
    return updateTask(taskId, {
        progress,
        items_scraped: itemsScraped,
        total_items_found: totalItems
    });
}

// ================================
// DELETE OPERATIONS
// ================================

export async function deleteTask(taskId: number): Promise<{ success: boolean; message: string }> {
    const response = await api.delete(`${BASE_URL}/tasks/${taskId}/delete/`);
    return response.data;
}

// ================================
// BULK OPERATIONS
// ================================

export async function bulkUpdateTasks(
    taskIds: number[], 
    updates: TaskUpdateData
): Promise<{ success: boolean; message: string; updated_count: number }> {
    const response = await api.post(`${BASE_URL}/tasks/bulk-update/`, {
        task_ids: taskIds,
        updates
    });
    return response.data;
}

export async function bulkDeleteTasks(
    taskIds: number[]
): Promise<{ success: boolean; message: string; deleted_count: number }> {
    const response = await api.delete(`${BASE_URL}/tasks/bulk-delete/`, {
        data: { task_ids: taskIds }
    });
    return response.data;
}

export async function bulkUpdateTaskStatus(
    taskIds: number[], 
    status: string
): Promise<{ success: boolean; message: string; updated_count: number }> {
    return bulkUpdateTasks(taskIds, { status });
}

// ================================
// ADVANCED OPERATIONS
// ================================

export async function duplicateTask(
    taskId: number, 
    newName?: string
): Promise<{ success: boolean; message: string; original_task_id: number; new_task: Task }> {
    const response = await api.post(`${BASE_URL}/tasks/${taskId}/duplicate/`, {
        task_name: newName
    });
    return response.data;
}

// ================================
// UTILITY FUNCTIONS
// ================================

export function getTaskStatusColor(status: string): string {
    const statusColors: Record<string, string> = {
        'PENDING': 'bg-yellow-100 text-yellow-800',
        'RUNNING': 'bg-blue-100 text-blue-800',
        'COMPLETED': 'bg-green-100 text-green-800',
        'FAILED': 'bg-red-100 text-red-800',
        'CANCELLED': 'bg-gray-100 text-gray-800',
        'PAUSED': 'bg-orange-100 text-orange-800'
    };
    return statusColors[status] || 'bg-gray-100 text-gray-800';
}

export function getTaskTypeDisplayName(taskType: string): string {
    const typeNames: Record<string, string> = {
        'MY_VIDEOS': 'My Videos',
        'MY_FOLLOWERS': 'My Followers',
        'MY_FOLLOWING': 'My Following',
        'MY_LIKES': 'My Liked Videos',
        'FEED_SCRAPE': 'Feed Scraping',
        'TARGETED_USER': 'Targeted User Analysis',
        'HASHTAG_ANALYSIS': 'Hashtag Analysis',
        'COMPETITOR_ANALYSIS': 'Competitor Analysis',
        'CONTENT_SEARCH': 'Content Search'
    };
    return typeNames[taskType] || taskType;
}

export function formatTaskDuration(startedAt?: string, completedAt?: string): string {
    if (!startedAt) return 'Not started';
    
    const start = new Date(startedAt);
    const end = completedAt ? new Date(completedAt) : new Date();
    const duration = end.getTime() - start.getTime();
    
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
        return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
    } else {
        return `${seconds}s`;
    }
}

export function calculateTaskProgress(task: Task): number {
    if (task.total_items_found > 0) {
        return Math.round((task.items_scraped / task.total_items_found) * 100);
    }
    return task.progress || 0;
}

// ================================
// TASK VALIDATION
// ================================

export function validateTaskData(data: TaskCreateData): string[] {
    const errors: string[] = [];
    
    if (!data.task_name?.trim()) {
        errors.push('Task name is required');
    }
    
    if (!data.task_type) {
        errors.push('Task type is required');
    }
    
    if (data.max_items && (data.max_items < 1 || data.max_items > 10000)) {
        errors.push('Max items must be between 1 and 10,000');
    }
    
    if (data.scrape_interval && (data.scrape_interval < 1 || data.scrape_interval > 3600)) {
        errors.push('Scrape interval must be between 1 and 3,600 seconds');
    }
    
    if (data.start_date && data.end_date) {
        const start = new Date(data.start_date);
        const end = new Date(data.end_date);
        if (start > end) {
            errors.push('Start date must be before end date');
        }
    }
    
    return errors;
}
