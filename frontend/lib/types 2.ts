export interface User {
    id: number;
    username: string;
    email: string;
    first_name?: string;
    last_name?: string;
    avatar?: string;
  }
  
  export interface Category {
    id: number;
    name: string;
    description: string;
    created_at: string;
}
  
  export interface Post {
    id: number;
    title: string;
    content: string;
    image: string | null;
    author: string;
    author_id: number;
    category: Category | null;
    category_id?: number;
    created_at: string;
    updated_at: string;
    comments: Comment[];
  }
  
  export interface Comment {
    id: number;
    content: string;
    author: string;
    author_id: number;
    created_at: string;
    post: number;
  }

  // for crawlers
  export interface Project {
  id: string;
  name: string;
  keywords: string;
  start_date: string;
  end_date: string;
  platforms: string[];
  created_at: string;
  updated_at: string;
  active_schedules: number;
  total_tasks: number;
}

export interface CrawlerSchedule {
  id: string;
  project_id: string;
  frequency: string;
  start_time: string;
  end_time: string;
  next_run: string | null;
  is_active: boolean;
  success_tasks: number;
  failed_tasks: number;
}

export interface CrawlerTask {
  id: string;
  schedule_id: string;
  schedule_project: string;
  status: string;
  started_at: string;
  finished_at: string | null;
  duration: string;
}

export interface CrawledData {
  id: string;
  url: string;
  domain: string;
  content_type: string;
  original_size: number;
  compressed_size: number;
  compression_ratio: number;
  created_at: string;
}

export interface DashboardStats {
  task_stats: {
    total: number;
    running: number;
    pending: number;
    completed: number;
    failed: number;
    avg_duration: string;
  };
  storage_stats: {
    total_size: number;
    total_size_mb: number;
    avg_ratio: number;
  };
}

export interface PlatformStats {
  [platform: string]: {
    tasks: number;
    data: number;
    avg_size: number;
  };
}