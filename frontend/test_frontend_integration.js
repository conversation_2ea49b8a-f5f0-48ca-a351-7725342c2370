#!/usr/bin/env node

/**
 * Frontend Integration Test
 * Tests the Actor system frontend integration with backend APIs
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

class FrontendIntegrationTest {
    constructor() {
        this.backendUrl = 'http://localhost:8000';
        this.frontendUrl = 'http://localhost:3000';
        this.testResults = [];
        
        console.log('🎭 Frontend Integration Test');
        console.log('============================');
        console.log(`🔗 Backend: ${this.backendUrl}`);
        console.log(`🌐 Frontend: ${this.frontendUrl}`);
        console.log(`📅 Started: ${new Date().toISOString()}`);
        console.log('');
    }

    async testEndpoint(description, url, expectedStatus = 200) {
        try {
            const response = await axios.get(url, { timeout: 10000 });
            const success = response.status === expectedStatus;
            const icon = success ? '✅' : '❌';
            
            console.log(`${icon} ${description} - ${response.status}`);
            
            this.testResults.push({
                description,
                url,
                status: response.status,
                expectedStatus,
                success,
                timestamp: new Date().toISOString()
            });
            
            return success;
        } catch (error) {
            const status = error.response ? error.response.status : 0;
            const icon = status === expectedStatus ? '✅' : '❌';
            
            console.log(`${icon} ${description} - ${status || 'ERROR'} (${error.message})`);
            
            this.testResults.push({
                description,
                url,
                status: status || 0,
                expectedStatus,
                success: status === expectedStatus,
                error: error.message,
                timestamp: new Date().toISOString()
            });
            
            return status === expectedStatus;
        }
    }

    async testBackendAPIs() {
        console.log('🔧 Testing Backend APIs...');
        console.log('-'.repeat(30));
        
        await this.testEndpoint('System Health', `${this.backendUrl}/api/actor/health/`);
        await this.testEndpoint('Available Platforms', `${this.backendUrl}/api/actor/platforms/`);
        await this.testEndpoint('Account List (No Auth)', `${this.backendUrl}/api/actor/accounts/list/`, 401);
        await this.testEndpoint('Task List (No Auth)', `${this.backendUrl}/api/actor/tasks/list/`, 401);
        await this.testEndpoint('Data Stats (No Auth)', `${this.backendUrl}/api/actor/data/stats/`, 401);
        
        console.log('');
    }

    async testFrontendPages() {
        console.log('🌐 Testing Frontend Pages...');
        console.log('-'.repeat(30));
        
        await this.testEndpoint('Frontend Home', `${this.frontendUrl}/`);
        await this.testEndpoint('Actor Dashboard', `${this.frontendUrl}/actor`);
        await this.testEndpoint('Actor Accounts', `${this.frontendUrl}/actor/accounts`);
        await this.testEndpoint('Add Account', `${this.frontendUrl}/actor/accounts/add`);
        await this.testEndpoint('Actor Tasks', `${this.frontendUrl}/actor/tasks`);
        await this.testEndpoint('Actor Data', `${this.frontendUrl}/actor/data`);
        await this.testEndpoint('Actor Sessions', `${this.frontendUrl}/actor/sessions`);
        
        console.log('');
    }

    async testAPIIntegration() {
        console.log('🔗 Testing API Integration...');
        console.log('-'.repeat(30));
        
        // Test if frontend can reach backend APIs
        try {
            // This simulates what the frontend does
            const response = await axios.get(`${this.backendUrl}/api/actor/platforms/`);
            console.log('✅ Frontend can reach backend APIs');
            console.log(`   Platforms available: ${response.data.platforms ? response.data.platforms.length : 'Unknown'}`);
        } catch (error) {
            console.log('❌ Frontend cannot reach backend APIs');
            console.log(`   Error: ${error.message}`);
        }
        
        console.log('');
    }

    generateSummary() {
        const total = this.testResults.length;
        const passed = this.testResults.filter(r => r.success).length;
        const failed = total - passed;
        const successRate = total > 0 ? ((passed / total) * 100).toFixed(1) : '0';
        
        console.log('='.repeat(50));
        console.log('📊 FRONTEND INTEGRATION TEST SUMMARY');
        console.log('='.repeat(50));
        console.log(`Total Tests: ${total}`);
        console.log(`✅ Passed: ${passed}`);
        console.log(`❌ Failed: ${failed}`);
        console.log(`📈 Success Rate: ${successRate}%`);
        
        if (failed > 0) {
            console.log('\n⚠️ Failed Tests:');
            this.testResults
                .filter(r => !r.success)
                .forEach(r => {
                    console.log(`   • ${r.description} - ${r.error || `Status: ${r.status}`}`);
                });
        }
        
        return { total, passed, failed, successRate };
    }

    saveResults() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const resultsFile = `frontend_integration_test_results_${timestamp}.json`;
        
        const summary = this.generateSummary();
        
        const output = {
            timestamp: new Date().toISOString(),
            backendUrl: this.backendUrl,
            frontendUrl: this.frontendUrl,
            summary,
            results: this.testResults
        };
        
        fs.writeFileSync(resultsFile, JSON.stringify(output, null, 2));
        console.log(`\n💾 Results saved to: ${resultsFile}`);
    }

    async runAllTests() {
        console.log('🚀 Starting Frontend Integration Tests...\n');
        
        await this.testBackendAPIs();
        await this.testFrontendPages();
        await this.testAPIIntegration();
        
        this.generateSummary();
        this.saveResults();
        
        console.log('\n🎉 Frontend Integration Test Complete!');
    }
}

// Check if servers are running
async function checkServers() {
    const backendRunning = await axios.get('http://localhost:8000/api/actor/health/')
        .then(() => true)
        .catch(() => false);
    
    const frontendRunning = await axios.get('http://localhost:3000')
        .then(() => true)
        .catch(() => false);
    
    console.log(`🔧 Backend Server: ${backendRunning ? '🟢 Running' : '🔴 Not Running'}`);
    console.log(`🌐 Frontend Server: ${frontendRunning ? '🟢 Running' : '🔴 Not Running'}`);
    console.log('');
    
    if (!backendRunning) {
        console.log('❌ Backend server is not running. Please start it:');
        console.log('   cd backend && python3 manage.py runserver');
        process.exit(1);
    }
    
    if (!frontendRunning) {
        console.log('⚠️ Frontend server is not running. Some tests will fail.');
        console.log('   cd frontend && npm run dev');
        console.log('');
    }
}

// Main execution
async function main() {
    await checkServers();
    
    const test = new FrontendIntegrationTest();
    await test.runAllTests();
}

main().catch(console.error);
