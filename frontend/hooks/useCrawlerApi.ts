import { useState, useEffect } from 'react';

// Mock data for crawler API
const mockStats = {
  totalTasks: 156,
  activeTasks: 12,
  completedTasks: 144,
  failedTasks: 8,
  totalAccounts: 24,
  activeAccounts: 18,
  totalData: 45678,
  todayData: 1234
};

const mockChartData = [
  { name: 'Jan', value: 400 },
  { name: 'Feb', value: 300 },
  { name: 'Mar', value: 600 },
  { name: 'Apr', value: 800 },
  { name: 'May', value: 500 },
  { name: 'Jun', value: 700 }
];

const mockTableData = [
  { id: 1, name: 'Task 1', status: 'completed', progress: 100, createdAt: '2024-01-01' },
  { id: 2, name: 'Task 2', status: 'running', progress: 75, createdAt: '2024-01-02' },
  { id: 3, name: 'Task 3', status: 'pending', progress: 0, createdAt: '2024-01-03' },
  { id: 4, name: 'Task 4', status: 'failed', progress: 25, createdAt: '2024-01-04' },
  { id: 5, name: 'Task 5', status: 'completed', progress: 100, createdAt: '2024-01-05' }
];

export const useCrawlerApi = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState(mockStats);
  const [chartData, setChartData] = useState(mockChartData);
  const [tableData, setTableData] = useState(mockTableData);

  useEffect(() => {
    // Simulate API call
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const refreshData = async () => {
    setLoading(true);
    // Simulate API refresh
    setTimeout(() => {
      setLoading(false);
    }, 500);
  };

  return {
    loading,
    error,
    stats,
    chartData,
    tableData,
    refreshData
  };
};
