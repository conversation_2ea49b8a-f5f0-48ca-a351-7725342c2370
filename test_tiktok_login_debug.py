#!/usr/bin/env python
import os
import sys
import django
import time

# Add the backend directory to the Python path
sys.path.append('/Users/<USER>/Documents/fullstax/backend')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

def debug_tiktok_login():
    print("🔍 Debugging TikTok login process...")
    
    username = "grafisone"
    password = "Puyol@102410"
    
    driver = None
    try:
        # Setup Chrome driver with debugging options
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Use desktop user agent instead of mobile
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        chrome_options.add_argument("--window-size=1280,720")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        wait = WebDriverWait(driver, 15)
        
        # Remove webdriver property
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("✅ WebDriver initialized")
        
        # Navigate to TikTok login page
        print("\n📍 Navigating to TikTok login page...")
        driver.get("https://www.tiktok.com/login/phone-or-email/email")
        time.sleep(5)
        
        print(f"Current URL: {driver.current_url}")
        print(f"Page title: {driver.title}")
        
        # Take screenshot
        screenshot_path = "/Users/<USER>/Documents/fullstax/tiktok_login_page.png"
        driver.save_screenshot(screenshot_path)
        print(f"📸 Screenshot saved: {screenshot_path}")
        
        # Check page source for debugging
        page_source = driver.page_source
        print(f"\n📄 Page source length: {len(page_source)} characters")
        
        # Look for form elements
        print("\n🔍 Looking for form elements...")
        
        # Find all input elements
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"Found {len(inputs)} input elements:")
        for i, inp in enumerate(inputs):
            try:
                input_type = inp.get_attribute("type") or "text"
                input_name = inp.get_attribute("name") or "no-name"
                input_placeholder = inp.get_attribute("placeholder") or "no-placeholder"
                input_id = inp.get_attribute("id") or "no-id"
                print(f"  {i+1}. Type: {input_type}, Name: {input_name}, Placeholder: {input_placeholder}, ID: {input_id}")
            except Exception as e:
                print(f"  {i+1}. Error getting attributes: {e}")
        
        # Find all button elements
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"\nFound {len(buttons)} button elements:")
        for i, btn in enumerate(buttons):
            try:
                btn_text = btn.text or "no-text"
                btn_type = btn.get_attribute("type") or "no-type"
                btn_class = btn.get_attribute("class") or "no-class"
                print(f"  {i+1}. Text: '{btn_text}', Type: {btn_type}, Class: {btn_class}")
            except Exception as e:
                print(f"  {i+1}. Error getting button attributes: {e}")
        
        # Try to find username field with multiple strategies
        print("\n📝 Attempting to find and fill username field...")
        username_field = None
        
        username_strategies = [
            (By.NAME, "username"),
            (By.NAME, "email"),
            (By.CSS_SELECTOR, 'input[placeholder*="email"]'),
            (By.CSS_SELECTOR, 'input[placeholder*="Email"]'),
            (By.CSS_SELECTOR, 'input[type="email"]'),
            (By.CSS_SELECTOR, 'input[type="text"]'),
            (By.XPATH, "//input[contains(@placeholder, 'email') or contains(@placeholder, 'Email')]"),
        ]
        
        for strategy_name, (by, selector) in enumerate(username_strategies):
            try:
                print(f"  Strategy {strategy_name + 1}: {by} = '{selector}'")
                username_field = driver.find_element(by, selector)
                if username_field.is_displayed() and username_field.is_enabled():
                    print(f"  ✅ Found username field with strategy {strategy_name + 1}")
                    break
                else:
                    print(f"  ❌ Field found but not visible/enabled")
                    username_field = None
            except NoSuchElementException:
                print(f"  ❌ No element found")
            except Exception as e:
                print(f"  ❌ Error: {e}")
        
        if username_field:
            try:
                username_field.clear()
                username_field.send_keys(username)
                print(f"  ✅ Username '{username}' entered successfully")
                time.sleep(2)
            except Exception as e:
                print(f"  ❌ Error entering username: {e}")
                return
        else:
            print("  ❌ Could not find username field")
            return
        
        # Try to find password field
        print("\n🔐 Attempting to find and fill password field...")
        password_field = None
        
        password_strategies = [
            (By.NAME, "password"),
            (By.CSS_SELECTOR, 'input[type="password"]'),
            (By.CSS_SELECTOR, 'input[placeholder*="password"]'),
            (By.CSS_SELECTOR, 'input[placeholder*="Password"]'),
            (By.XPATH, "//input[@type='password']"),
        ]
        
        for strategy_name, (by, selector) in enumerate(password_strategies):
            try:
                print(f"  Strategy {strategy_name + 1}: {by} = '{selector}'")
                password_field = driver.find_element(by, selector)
                if password_field.is_displayed() and password_field.is_enabled():
                    print(f"  ✅ Found password field with strategy {strategy_name + 1}")
                    break
                else:
                    print(f"  ❌ Field found but not visible/enabled")
                    password_field = None
            except NoSuchElementException:
                print(f"  ❌ No element found")
            except Exception as e:
                print(f"  ❌ Error: {e}")
        
        if password_field:
            try:
                password_field.clear()
                password_field.send_keys(password)
                print(f"  ✅ Password entered successfully")
                time.sleep(2)
            except Exception as e:
                print(f"  ❌ Error entering password: {e}")
                return
        else:
            print("  ❌ Could not find password field")
            return
        
        # Try to find and click login button
        print("\n🚀 Attempting to find and click login button...")
        login_button = None
        
        login_strategies = [
            (By.CSS_SELECTOR, 'button[type="submit"]'),
            (By.CSS_SELECTOR, 'button[data-e2e="login-button"]'),
            (By.CSS_SELECTOR, 'div[role="button"]'),
            (By.CSS_SELECTOR, '.login-button'),
            (By.XPATH, "//button[contains(text(), 'Log in') or contains(text(), 'Login') or contains(text(), 'Sign in')]"),
            (By.XPATH, "//div[@role='button'][contains(text(), 'Log in') or contains(text(), 'Login')]"),
        ]
        
        for strategy_name, (by, selector) in enumerate(login_strategies):
            try:
                print(f"  Strategy {strategy_name + 1}: {by} = '{selector}'")
                login_button = driver.find_element(by, selector)
                if login_button.is_displayed() and login_button.is_enabled():
                    print(f"  ✅ Found login button with strategy {strategy_name + 1}")
                    print(f"  Button text: '{login_button.text}'")
                    break
                else:
                    print(f"  ❌ Button found but not visible/enabled")
                    login_button = None
            except NoSuchElementException:
                print(f"  ❌ No element found")
            except Exception as e:
                print(f"  ❌ Error: {e}")
        
        if login_button:
            try:
                print(f"  🖱️ Clicking login button...")
                login_button.click()
                print(f"  ✅ Login button clicked")
                time.sleep(8)  # Wait longer for login process
            except Exception as e:
                print(f"  ❌ Error clicking login button: {e}")
                return
        else:
            print("  ❌ Could not find login button")
            return
        
        # Check result
        print("\n📊 Checking login result...")
        final_url = driver.current_url
        final_title = driver.title
        
        print(f"Final URL: {final_url}")
        print(f"Final title: {final_title}")
        
        # Take final screenshot
        final_screenshot_path = "/Users/<USER>/Documents/fullstax/tiktok_after_login.png"
        driver.save_screenshot(final_screenshot_path)
        print(f"📸 Final screenshot saved: {final_screenshot_path}")
        
        # Check for success indicators
        if "login" not in final_url.lower():
            print("✅ Login appears successful - no longer on login page")
        else:
            print("❌ Login failed - still on login page")
            
            # Look for error messages
            print("\n🔍 Looking for error messages...")
            error_selectors = [
                '.error-message',
                '[data-e2e="login-error"]',
                '.login-error',
                '.form-error',
                '.error',
                '[class*="error"]'
            ]
            
            for selector in error_selectors:
                try:
                    error_elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for error_element in error_elements:
                        if error_element.is_displayed():
                            print(f"  ❌ Error found: {error_element.text}")
                except Exception as e:
                    print(f"  Error checking selector {selector}: {e}")
        
        # Wait a bit before closing
        print("\n⏳ Waiting 10 seconds before closing...")
        time.sleep(10)
        
    except Exception as e:
        print(f"❌ Debug error: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        if driver:
            try:
                driver.quit()
                print("\n🔧 WebDriver closed")
            except:
                pass

if __name__ == "__main__":
    debug_tiktok_login()