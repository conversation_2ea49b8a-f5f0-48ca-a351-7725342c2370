#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append('/Users/<USER>/Documents/fullstax/backend')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor.models import ActorTask, ActorAccount
from actor.services import ActorService
from actor.engines.tiktok_engine import TikTokEngine
from actor.utils.simple_tiktok_auth import SimpleTikTokAuthenticator
from django.contrib.auth.models import User

def test_tiktok_reauth_and_execute():
    print("Testing TikTok re-authentication and task execution...")
    
    try:
        # Get the account (get the most recent one if multiple exist)
        account = ActorAccount.objects.filter(platform='tiktok', platform_username='grafisone').order_by('-created_at').first()
        if not account:
            print("No TikTok account 'grafisone' found")
            return
        print(f"Found account: {account.platform_username} on {account.platform}")
        
        # Test credentials
        username = "grafisone"
        password = "Puyol@102410"
        
        print(f"\n=== Testing SimpleTikTokAuthenticator ===")
        authenticator = SimpleTikTokAuthenticator()
        auth_result = authenticator.login(username, password)
        print(f"SimpleTikTokAuthenticator result: {auth_result}")
        
        if auth_result.get('success'):
            session_info = auth_result.get('session_info', {})
            print(f"Session info keys: {list(session_info.keys())}")
            
            # Extract cookies from session_info
            cookies = session_info.get('cookies', [])
            print(f"Session cookies count: {len(cookies)}")
            
            # Save session data to account
            if cookies:
                account.encrypt_session_data(session_info)
                account.save()
                print("Session data saved to account")
                
                # Verify session data was saved
                saved_session = account.decrypt_session_data()
                print(f"Verified saved session data: {bool(saved_session)}")
            else:
                print("No cookies found in session info")
            
            print(f"\n=== Testing TikTokEngine authentication ===")
            engine = TikTokEngine()
            credentials = {
                'username': username,
                'password': password
            }
            engine_auth_result = engine.authenticate(account, credentials)
            print(f"TikTokEngine authentication result: {engine_auth_result}")
            
            if engine_auth_result.get('success'):
                print(f"\n=== Testing task execution ===")
                # Get the task
                task = ActorTask.objects.get(id=131)
                print(f"Executing task: {task.task_name}")
                
                # Execute the task
                service = ActorService()
                result = service.execute_task(task.id)
                print(f"Task execution result: {result}")
                
                # Check task status after execution
                task.refresh_from_db()
                print(f"\nTask status after execution: {task.status}")
                print(f"Items scraped: {task.items_scraped}")
                print(f"Progress: {task.progress_percentage}%")
                
                if task.error_message:
                    print(f"Error message: {task.error_message}")
                    
                # Check scraped data
                scraped_data = task.scraped_data.all()
                print(f"Scraped data count: {scraped_data.count()}")
                
                if scraped_data.exists():
                    for data in scraped_data[:3]:  # Show first 3 items
                        print(f"Data type: {data.data_type}")
                        print(f"Content keys: {list(data.content.keys()) if data.content else 'No content'}")
            else:
                print(f"TikTokEngine authentication failed: {engine_auth_result}")
        else:
            print(f"SimpleTikTokAuthenticator failed: {auth_result}")
            
    except ActorAccount.DoesNotExist:
        print("TikTok account 'grafisone' not found")
    except ActorTask.DoesNotExist:
        print("Task with ID 131 not found")
    except Exception as e:
        print(f"Error during test: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tiktok_reauth_and_execute()