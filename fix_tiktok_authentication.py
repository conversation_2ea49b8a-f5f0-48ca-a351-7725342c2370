#!/usr/bin/env python3
"""
Fix TikTok Authentication Issues
Re-authenticate TikTok accounts and test the system
"""

import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from actor.models import ActorAccount, ActorTask, ActorScrapedData
from actor.services.actor_service import ActorService
from datetime import datetime, timedelta
from django.utils import timezone

def fix_tiktok_authentication():
    """
    Fix TikTok authentication issues
    """
    print("=== Fixing TikTok Authentication ===")
    print(f"Started at: {datetime.now()}")
    
    # 1. Find the working account (ID 21 with valid session)
    print("\n1. Finding working TikTok account...")
    working_account = ActorAccount.objects.filter(
        platform='tiktok',
        id=21,  # This account has valid session data
        is_active=True
    ).first()
    
    if working_account:
        print(f"Found working account: {working_account.platform_username} (ID: {working_account.id})")
        print(f"Last login: {working_account.last_login}")
        print(f"Session expires: {working_account.session_expires_at}")
        
        # Check if session is still valid
        if working_account.session_expires_at and working_account.session_expires_at > timezone.now():
            print("✅ Session is still valid!")
            
            # 2. Test creating a new task with this account
            print("\n2. Testing task creation with working account...")
            
            # Get or create test user
            test_user, created = User.objects.get_or_create(
                username='tiktok_test_user',
                defaults={'email': '<EMAIL>'}
            )
            
            service = ActorService()
            
            try:
                # Create a test task
                task_result = service.create_task(
                    user=test_user,
                    task_name="TikTok System Test",
                    task_type='CONTENT_SEARCH',
                    platform='tiktok',
                    account_id=working_account.id,
                    keywords='indonesia',
                    max_items=3
                )
                
                print(f"Task creation result: {task_result}")
                
                if task_result.get('success'):
                    task_id = task_result.get('task_id')
                    print(f"✅ Created test task with ID: {task_id}")
                    
                    # 3. Execute the task
                    print("\n3. Executing test task...")
                    try:
                        execution_result = service.execute_task(task_id)
                        print(f"Task execution result: {execution_result}")
                        
                        if execution_result.get('success'):
                            print("✅ Task executed successfully!")
                            
                            # Check the task status
                            task = ActorTask.objects.get(id=task_id)
                            print(f"Task status: {task.status}")
                            print(f"Items scraped: {task.items_scraped}")
                            print(f"Progress: {task.progress_percentage}%")
                            
                            if task.error_message:
                                print(f"Error message: {task.error_message}")
                        else:
                            print(f"❌ Task execution failed: {execution_result.get('error')}")
                            
                    except Exception as e:
                        print(f"❌ Task execution error: {str(e)}")
                        import traceback
                        traceback.print_exc()
                        
                else:
                    print(f"❌ Task creation failed: {task_result.get('error')}")
                    
            except Exception as e:
                print(f"❌ Task creation error: {str(e)}")
                import traceback
                traceback.print_exc()
                
        else:
            print("❌ Session has expired, need to re-authenticate")
            
            # 4. Re-authenticate the account
            print("\n4. Re-authenticating account...")
            try:
                service = ActorService()
                auth_result = service.authenticate_account(working_account.id)
                print(f"Authentication result: {auth_result}")
                
                if auth_result.get('success'):
                    print("✅ Account re-authenticated successfully!")
                else:
                    print(f"❌ Re-authentication failed: {auth_result.get('error')}")
                    
            except Exception as e:
                print(f"❌ Re-authentication error: {str(e)}")
                import traceback
                traceback.print_exc()
    else:
        print("❌ No working TikTok account found")
        
        # 5. Show all accounts and their status
        print("\n5. All TikTok accounts status:")
        all_accounts = ActorAccount.objects.filter(platform='tiktok')
        
        for account in all_accounts:
            print(f"\nAccount ID {account.id}: {account.platform_username}")
            print(f"  Active: {account.is_active}")
            print(f"  Last login: {account.last_login}")
            print(f"  Session expires: {account.session_expires_at}")
            print(f"  Has session data: {bool(account.encrypted_session_data)}")
            print(f"  Login attempts: {account.login_attempts}")
            print(f"  Blocked: {account.is_blocked}")
            
            # Check if session is valid
            if account.session_expires_at:
                if account.session_expires_at > timezone.now():
                    print(f"  ✅ Session valid until {account.session_expires_at}")
                else:
                    print(f"  ❌ Session expired on {account.session_expires_at}")
            else:
                print(f"  ⚠️ No session expiry set")
    
    # 6. Show recent task performance
    print("\n6. Recent task performance:")
    recent_tasks = ActorTask.objects.filter(
        actor_account__platform='tiktok'
    ).order_by('-created_at')[:5]
    
    for task in recent_tasks:
        print(f"\nTask {task.id}: {task.task_name}")
        print(f"  Status: {task.status}")
        print(f"  Keywords: {task.keywords}")
        print(f"  Items scraped: {task.items_scraped}")
        print(f"  Created: {task.created_at}")
        print(f"  Account: {task.actor_account.platform_username if task.actor_account else 'None'}")
        
        if task.error_message:
            print(f"  Error: {task.error_message}")
    
    # 7. Show scraped data statistics
    print("\n7. Scraped data statistics:")
    total_data = ActorScrapedData.objects.filter(actor_account__platform='tiktok').count()
    recent_data = ActorScrapedData.objects.filter(
        actor_account__platform='tiktok',
        scraped_at__gte=timezone.now() - timedelta(days=1)
    ).count()
    
    print(f"Total TikTok data items: {total_data}")
    print(f"Data scraped in last 24h: {recent_data}")
    
    # Show data by account
    accounts_with_data = ActorScrapedData.objects.filter(
        actor_account__platform='tiktok'
    ).values('account_username').distinct()
    
    print("\nData by account:")
    for account_data in accounts_with_data:
        username = account_data['account_username']
        count = ActorScrapedData.objects.filter(
            actor_account__platform='tiktok',
            account_username=username
        ).count()
        print(f"  {username}: {count} items")
    
    print("\n=== Authentication Fix Complete ===")
    print(f"Completed at: {datetime.now()}")

if __name__ == "__main__":
    fix_tiktok_authentication()