#!/usr/bin/env python3
"""
Comprehensive TikTok System Verification
Tests the complete TikTok task workflow in the Actor system
"""

import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from actor.models import ActorAccount, ActorTask, ActorScrapedData
from actor.services.actor_service import ActorService
from datetime import datetime

def test_tiktok_system():
    """
    Test the complete TikTok system functionality
    """
    print("=== TikTok System Verification ===")
    print(f"Test started at: {datetime.now()}")
    
    # 1. Check existing accounts
    print("\n1. Checking existing TikTok accounts...")
    tiktok_accounts = ActorAccount.objects.filter(platform='tiktok')
    print(f"Found {tiktok_accounts.count()} TikTok accounts:")
    
    for account in tiktok_accounts:
        print(f"  - ID: {account.id}, Username: {account.platform_username}")
        print(f"    Active: {account.is_active}, Last login: {account.last_login}")
        print(f"    Session expires: {account.session_expires_at}")
        print(f"    Has session data: {bool(account.encrypted_session_data)}")
    
    # 2. Check recent tasks
    print("\n2. Checking recent TikTok tasks...")
    recent_tasks = ActorTask.objects.filter(
        actor_account__platform='tiktok'
    ).order_by('-created_at')[:5]
    
    print(f"Found {recent_tasks.count()} recent TikTok tasks:")
    for task in recent_tasks:
        print(f"  - Task ID: {task.id}, Status: {task.status}")
        print(f"    Keywords: {task.keywords}")
        print(f"    Items scraped: {task.items_scraped}")
        print(f"    Created: {task.created_at}")
        if task.error_message:
            print(f"    Error: {task.error_message}")
    
    # 3. Check scraped data
    print("\n3. Checking scraped data...")
    scraped_data = ActorScrapedData.objects.filter(
        actor_account__platform='tiktok'
    ).order_by('-scraped_at')[:3]
    
    print(f"Found {scraped_data.count()} recent scraped items:")
    for data in scraped_data:
        print(f"  - Platform Content ID: {data.platform_content_id}")
        print(f"    Data Type: {data.data_type}")
        print(f"    Platform: {data.platform}")
        print(f"    Account: {data.account_username}")
        print(f"    Quality Score: {data.quality_score}")
        print(f"    Scraped: {data.scraped_at}")
        # Show content preview
        content = data.content
        if isinstance(content, dict):
            print(f"    Content preview: {str(content)[:100]}...")
    
    # 4. Test account authentication
    print("\n4. Testing account authentication...")
    if tiktok_accounts.exists():
        test_account = tiktok_accounts.first()
        print(f"Testing authentication for account: {test_account.platform_username}")
        
        service = ActorService()
        try:
            auth_result = service.authenticate_account(test_account.id)
            print(f"Authentication result: {auth_result}")
        except Exception as e:
            print(f"Authentication error: {str(e)}")
    else:
        print("No TikTok accounts available for testing")
    
    # 5. Test task creation
    print("\n5. Testing task creation...")
    if tiktok_accounts.exists():
        test_account = tiktok_accounts.first()
        
        # Get or create a test user
        test_user, created = User.objects.get_or_create(
            username='test_user',
            defaults={'email': '<EMAIL>'}
        )
        
        try:
            service = ActorService()
            
            task_result = service.create_task(
                user=test_user,
                account_id=test_account.id,
                task_type='CONTENT_SEARCH',
                task_name='Test Verification Task',
                keywords='test verification',
                max_items=5
            )
            print(f"Task creation result: {task_result}")
            
            if task_result.get('success'):
                task_id = task_result.get('task_id')
                print(f"Created test task with ID: {task_id}")
                
                # Try to execute the task
                print("\n6. Testing task execution...")
                try:
                    execution_result = service.execute_task(task_id)
                    print(f"Task execution result: {execution_result}")
                except Exception as e:
                    print(f"Task execution error: {str(e)}")
                    import traceback
                    traceback.print_exc()
            
        except Exception as e:
            print(f"Task creation error: {str(e)}")
            import traceback
            traceback.print_exc()
    else:
        print("No TikTok accounts available for task creation")
    
    # 7. System health check
    print("\n7. System health check...")
    try:
        service = ActorService()
        
        # Check available platforms
        print("Available platforms:")
        platforms = service.get_available_platforms()
        for platform in platforms:
            engine = service.get_engine(platform)
            print(f"  - {platform}: {engine.__class__.__name__ if engine else 'Not available'}")
        
        # Check database connectivity
        total_accounts = ActorAccount.objects.count()
        total_tasks = ActorTask.objects.count()
        total_data = ActorScrapedData.objects.count()
        
        print(f"\nDatabase status:")
        print(f"  - Total accounts: {total_accounts}")
        print(f"  - Total tasks: {total_tasks}")
        print(f"  - Total scraped data: {total_data}")
        
        # Test TikTok engine specifically
        print("\nTikTok engine test:")
        tiktok_engine = service.get_engine('tiktok')
        if tiktok_engine:
            print(f"  - TikTok engine loaded: {tiktok_engine.__class__.__name__}")
        else:
            print("  - TikTok engine not available")
        
    except Exception as e:
        print(f"Health check error: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # 8. Authentication issue analysis
    print("\n8. Authentication issue analysis...")
    if tiktok_accounts.exists():
        for account in tiktok_accounts:
            print(f"\nAnalyzing account: {account.platform_username}")
            print(f"  - Has encrypted password: {bool(account.password)}")
            print(f"  - Has session data: {bool(account.encrypted_session_data)}")
            print(f"  - Session expires: {account.session_expires_at}")
            print(f"  - Last login: {account.last_login}")
            
            # Try to decrypt password to check if it's valid
            try:
                decrypted_password = account.decrypt_password()
                print(f"  - Password decryption: Success (length: {len(decrypted_password)})")
            except Exception as e:
                print(f"  - Password decryption: Failed - {str(e)}")
    
    print("\n=== Verification Complete ===")
    print(f"Test completed at: {datetime.now()}")
    
    # Summary of findings
    print("\n=== SUMMARY ===")
    print(f"- TikTok accounts found: {tiktok_accounts.count()}")
    print(f"- Recent tasks: {recent_tasks.count()}")
    print(f"- Recent scraped data: {scraped_data.count()}")
    
    # Count tasks with 0 items
    zero_item_tasks = ActorTask.objects.filter(
        actor_account__platform='tiktok',
        status='COMPLETED',
        items_scraped=0
    ).count()
    print(f"- Completed tasks with 0 items: {zero_item_tasks}")
    
    # Authentication status
    auth_issues = 0
    for account in tiktok_accounts:
        if not account.is_session_valid():
            auth_issues += 1
    print(f"- Accounts with authentication issues: {auth_issues}")

if __name__ == "__main__":
    test_tiktok_system()