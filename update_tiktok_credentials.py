#!/usr/bin/env python3
"""
Update TikTok Account Credentials
Updates the grafisone account with the correct password
"""

import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor.models import ActorAccount
from actor.services.actor_service import ActorService
from django.contrib.auth.models import User

def update_tiktok_credentials():
    """
    Update the TikTok account credentials for grafisone
    """
    print("=== Updating TikTok Account Credentials ===")
    
    # Find all grafisone accounts
    try:
        accounts = ActorAccount.objects.filter(
            platform='tiktok',
            platform_username='grafisone'
        ).order_by('-created_at')
        
        print(f"Found {accounts.count()} accounts with username 'grafisone':")
        for i, acc in enumerate(accounts):
            print(f"  {i+1}. ID: {acc.id}, User: {acc.user.username}, Created: {acc.created_at}, Active: {acc.is_active}")
        
        if not accounts.exists():
            raise ActorAccount.DoesNotExist("No accounts found")
        
        # Update all accounts or just the most recent active one
        active_accounts = accounts.filter(is_active=True)
        if active_accounts.exists():
            account = active_accounts.first()
            print(f"\nUpdating most recent active account: {account.id}")
        else:
            account = accounts.first()
            print(f"\nUpdating most recent account: {account.id}")
        
        # Update the password
        new_password = "Puyol@102410"
        account.encrypt_password(new_password)
        account.save()
        
        print("Password updated successfully!")
        
        # Test authentication with new credentials
        print("\nTesting authentication with new credentials...")
        service = ActorService()
        auth_result = service.authenticate_account(account.id)
        
        if auth_result.get('success'):
            print("✅ Authentication successful!")
            print(f"Session data updated: {bool(account.encrypted_session_data)}")
        else:
            print(f"❌ Authentication failed: {auth_result.get('error')}")
        
        return auth_result
        
    except ActorAccount.DoesNotExist:
        print("❌ Account 'grafisone' not found")
        
        # Create the account if it doesn't exist
        print("\nCreating new TikTok account...")
        
        # Get or create a user
        user, created = User.objects.get_or_create(
            username='admin',
            defaults={'email': '<EMAIL>'}
        )
        
        service = ActorService()
        result = service.create_account(
            user=user,
            platform='tiktok',
            username='grafisone',
            password='Puyol@102410'
        )
        
        if result.get('success'):
            print("✅ Account created successfully!")
            print(f"Account ID: {result.get('account_id')}")
        else:
            print(f"❌ Failed to create account: {result.get('error')}")
        
        return result
        
    except Exception as e:
        print(f"❌ Error updating credentials: {str(e)}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    update_tiktok_credentials()