#!/usr/bin/env python3
"""
Final TikTok System Verification
Tests the TikTok system with updated credentials
"""

import os
import sys
import django
from datetime import datetime

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor.models import ActorAccount, ActorTask, ActorScrapedData
from actor.services.actor_service import ActorService
from django.contrib.auth.models import User

def test_tiktok_system():
    """
    Final verification of TikTok system functionality
    """
    print("=== Final TikTok System Verification ===")
    print(f"Test started at: {datetime.now()}")
    
    # 1. Check the updated account
    print("\n1. Checking updated TikTok account...")
    try:
        account = ActorAccount.objects.filter(
            platform='tiktok',
            platform_username='grafisone'
        ).order_by('-created_at').first()
        
        if account:
            print(f"✅ Found account: {account.platform_username} (ID: {account.id})")
            print(f"   User: {account.user.username}")
            print(f"   Active: {account.is_active}")
            print(f"   Created: {account.created_at}")
            
            # Test password decryption
            try:
                decrypted_password = account.decrypt_password()
                print(f"   Password length: {len(decrypted_password)}")
                print(f"   Password matches expected: {'Puyol@102410' == decrypted_password}")
            except Exception as e:
                print(f"   ❌ Password decryption failed: {e}")
        else:
            print("❌ No grafisone account found")
            return
    except Exception as e:
        print(f"❌ Error checking account: {e}")
        return
    
    # 2. Test authentication
    print("\n2. Testing authentication...")
    try:
        service = ActorService()
        auth_result = service.authenticate_account(account.id)
        
        if auth_result.get('success'):
            print("✅ Authentication successful!")
            
            # Refresh account to check session data
            account.refresh_from_db()
            print(f"   Session data present: {bool(account.encrypted_session_data)}")
            if account.last_login:
                print(f"   Last login: {account.last_login}")
        else:
            print(f"❌ Authentication failed: {auth_result.get('error')}")
    except Exception as e:
        print(f"❌ Authentication error: {e}")
    
    # 3. Test engine functionality
    print("\n3. Testing TikTok engine...")
    try:
        engine = service.get_engine('tiktok')
        if engine:
            print("✅ TikTok engine loaded successfully")
            print(f"   Engine class: {engine.__class__.__name__}")
        else:
            print("❌ Failed to load TikTok engine")
    except Exception as e:
        print(f"❌ Engine error: {e}")
    
    # 4. Create a test task
    print("\n4. Creating test task...")
    try:
        user = account.user
        task_result = service.create_task(
            user=user,
            platform='tiktok',
            task_type='CONTENT_SEARCH',
            task_name='Test Search Task',
            parameters={'keyword': 'dance', 'max_results': 5}
        )
        
        if task_result.get('success'):
            task_id = task_result.get('task_id')
            print(f"✅ Task created successfully (ID: {task_id})")
            
            # Check task status
            task = ActorTask.objects.get(id=task_id)
            print(f"   Task status: {task.status}")
            print(f"   Task type: {task.task_type}")
            print(f"   Parameters: {task.parameters}")
        else:
            print(f"❌ Task creation failed: {task_result.get('error')}")
    except Exception as e:
        print(f"❌ Task creation error: {e}")
    
    # 5. Summary
    print("\n=== FINAL SUMMARY ===")
    total_accounts = ActorAccount.objects.filter(platform='tiktok').count()
    active_accounts = ActorAccount.objects.filter(platform='tiktok', is_active=True).count()
    total_tasks = ActorTask.objects.filter(platform='tiktok').count()
    completed_tasks = ActorTask.objects.filter(platform='tiktok', status='COMPLETED').count()
    
    print(f"- Total TikTok accounts: {total_accounts}")
    print(f"- Active TikTok accounts: {active_accounts}")
    print(f"- Total TikTok tasks: {total_tasks}")
    print(f"- Completed TikTok tasks: {completed_tasks}")
    
    # Check recent activity
    recent_tasks = ActorTask.objects.filter(platform='tiktok').order_by('-created_at')[:3]
    if recent_tasks:
        print(f"\nRecent tasks:")
        for task in recent_tasks:
            scraped_count = ActorScrapedData.objects.filter(task=task).count()
            print(f"  - Task {task.id}: {task.task_type}, Status: {task.status}, Items: {scraped_count}")
    
    print(f"\nVerification completed at: {datetime.now()}")
    print("\n🎉 TikTok system verification complete!")

if __name__ == "__main__":
    test_tiktok_system()