#!/usr/bin/env python3
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append('/Users/<USER>/Documents/fullstax/backend')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor.models import ActorTask, ActorScrapedData, ActorAccount
from django.utils import timezone
from datetime import timedelta

print("=== TikTok Task Data Investigation ===")

# Check recent TikTok tasks
recent_tasks = ActorTask.objects.filter(
    actor_account__platform='tiktok',
    created_at__gte=timezone.now() - timedelta(hours=24)
).order_by('-created_at')

print(f"\nRecent TikTok tasks (last 24 hours): {recent_tasks.count()}")
for task in recent_tasks[:10]:
    print(f"Task {task.id}: {task.task_name} - Status: {task.status} - Items: {task.items_scraped} - Created: {task.created_at}")

# Check the latest task in detail
if recent_tasks.exists():
    latest_task = recent_tasks.first()
    print(f"\n=== Latest Task Details ===")
    print(f"Task ID: {latest_task.id}")
    print(f"Task Name: {latest_task.task_name}")
    print(f"Status: {latest_task.status}")
    print(f"Items Scraped: {latest_task.items_scraped}")
    print(f"Account: {latest_task.actor_account.platform_username}")
    print(f"Created: {latest_task.created_at}")
    print(f"Updated: {latest_task.updated_at}")
    
    # Check for scraped data linked to this task
    task_data = ActorScrapedData.objects.filter(task=latest_task)
    print(f"\nScraped data items for task {latest_task.id}: {task_data.count()}")
    
    if task_data.exists():
        print("Data items:")
        for data in task_data[:5]:
            print(f"  - ID: {data.id}, Type: {data.data_type}, Platform ID: {data.platform_content_id}")
    else:
        print("No scraped data found for this task")
        
        # Check if there's any data without task
        orphaned_data = ActorScrapedData.objects.filter(
            actor_account=latest_task.actor_account,
            task__isnull=True,
            scraped_at__gte=latest_task.created_at - timedelta(minutes=30)
        )
        print(f"Orphaned data (no task) around task time: {orphaned_data.count()}")
        
        # Check if there's any data with different task
        recent_data = ActorScrapedData.objects.filter(
            actor_account=latest_task.actor_account,
            scraped_at__gte=latest_task.created_at - timedelta(minutes=30)
        )
        print(f"All data around task time: {recent_data.count()}")
        for data in recent_data[:3]:
            print(f"  - ID: {data.id}, Task ID: {data.task.id}, Scraped: {data.scraped_at}")

# Check all TikTok data in the system
all_tiktok_data = ActorScrapedData.objects.filter(actor_account__platform='tiktok')
print(f"\n=== Total TikTok Data in System ===")
print(f"Total items: {all_tiktok_data.count()}")

# Group by task
task_data_counts = {}
for data in all_tiktok_data:
    task_id = data.task.id if data.task else 'None'
    task_data_counts[task_id] = task_data_counts.get(task_id, 0) + 1

print("\nData distribution by task:")
for task_id, count in sorted(task_data_counts.items()):
    if task_id != 'None':
        try:
            task = ActorTask.objects.get(id=task_id)
            print(f"  Task {task_id} ({task.task_name}): {count} items")
        except ActorTask.DoesNotExist:
            print(f"  Task {task_id} (DELETED): {count} items")
    else:
        print(f"  No Task: {count} items")

# Check TikTok accounts
tiktok_accounts = ActorAccount.objects.filter(platform='tiktok')
print(f"\n=== TikTok Accounts ===")
print(f"Total accounts: {tiktok_accounts.count()}")
for account in tiktok_accounts:
    account_data = ActorScrapedData.objects.filter(actor_account=account)
    print(f"  {account.platform_username} (ID: {account.id}): {account_data.count()} data items")

print("\n=== Investigation Complete ===")