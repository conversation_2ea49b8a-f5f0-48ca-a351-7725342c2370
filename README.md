# Fullstax Blog

A full-stack blog application built with Django REST Framework and Next.js.

## Features

- User authentication and authorization
- Blog post creation and management
- Category management
- Rich text editor for post content
- Comment system
- Responsive design with Tailwind CSS
- Dark/Light theme support

## Tech Stack

### Backend
- Django
- Django REST Framework
- SQLite (database)

### Frontend
- Next.js 14
- TypeScript
- Tailwind CSS
- Shadcn UI Components
- Rich Text Editor

## Project Structure

```
├── backend/          # Django backend
│   ├── blog/        # Blog app
│   └── users/       # User management app
└── frontend/        # Next.js frontend
    ├── app/        # Next.js app router
    ├── components/ # React components
    └── lib/        # Utilities and types
```

## Getting Started

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Create a virtual environment and activate it:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows use: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Run migrations:
   ```bash
   python manage.py migrate
   ```

5. Start the development server:
   ```bash
   python manage.py runserver
   ```

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   pnpm install
   ```

3. Start the development server:
   ```bash
   pnpm dev
   ```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000

## Environment Variables

Create a `.env` file in the frontend directory with the following variables:
```
NEXT_PUBLIC_API_URL=http://localhost:8000
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License.
