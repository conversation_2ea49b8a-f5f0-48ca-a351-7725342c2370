#!/usr/bin/env python3
"""
Test script to debug the execute button issue in the actor system.
This script will check if Celery tasks are being dispatched correctly.
"""

import os
import sys
import django
from datetime import datetime

# Add the backend directory to Python path
sys.path.append('/Users/<USER>/Documents/fullstax/backend')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from actor.models import ActorAccount, ActorTask
from actor.services.actor_service import ActorService

def test_execute_button_functionality():
    """
    Test the execute button functionality by simulating the API call.
    """
    print("🔍 Testing Execute Button Functionality")
    print("=" * 50)
    
    # Initialize service
    actor_service = ActorService()
    
    # Get a test user and account
    try:
        user = User.objects.first()
        if not user:
            print("❌ No users found in database")
            return
            
        print(f"👤 Using user: {user.username}")
        
        # Get TikTok accounts
        tiktok_accounts = ActorAccount.objects.filter(user=user, platform='tiktok')
        
        if not tiktok_accounts.exists():
            print("❌ No TikTok accounts found")
            return
            
        account = tiktok_accounts.first()
        print(f"📱 Using account: {account.platform_username} (ID: {account.id})")
        print(f"🔐 Session valid: {account.is_session_valid()}")
        print(f"📅 Session expires: {account.session_expires_at}")
        
        # Get a test task
        tasks = ActorTask.objects.filter(user=user, actor_account=account)
        
        if not tasks.exists():
            print("❌ No tasks found for this account")
            return
            
        task = tasks.first()
        print(f"📋 Using task: {task.task_name} (ID: {task.id})")
        print(f"📊 Task status: {task.status}")
        print(f"🎯 Task type: {task.task_type}")
        
        # Test the execute_task_async method (same as execute button)
        print("\n🚀 Testing execute_task_async (Execute Button Logic)...")
        
        result = actor_service.execute_task_async(task.id, user=user)
        
        print(f"✅ Execute result: {result}")
        
        if result.get('success'):
            print(f"🎉 SUCCESS: Task dispatched to Celery!")
            print(f"📝 Celery Task ID: {result.get('celery_task_id')}")
            print(f"📊 Status: {result.get('status')}")
            print(f"💬 Message: {result.get('message')}")
        else:
            print(f"❌ FAILED: {result.get('error')}")
            
        # Check task status after execution
        task.refresh_from_db()
        print(f"\n📊 Task status after execution: {task.status}")
        print(f"🆔 Celery task ID: {task.celery_task_id}")
        
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()

def check_celery_connection():
    """
    Check if Celery can connect to Redis and dispatch tasks.
    """
    print("\n🔗 Testing Celery Connection")
    print("=" * 30)
    
    try:
        from actor.tasks import actor_scrape_hashtag_task
        
        # Try to get Celery app status
        from backend.celery import app
        
        # Check if we can inspect active tasks
        inspect = app.control.inspect()
        active_tasks = inspect.active()
        
        print(f"📊 Active Celery tasks: {active_tasks}")
        
        # Check registered tasks
        registered_tasks = inspect.registered()
        print(f"📋 Registered tasks: {list(registered_tasks.keys()) if registered_tasks else 'None'}")
        
        if registered_tasks:
            for worker, tasks in registered_tasks.items():
                print(f"  Worker {worker}: {len(tasks)} tasks")
                for task in tasks:
                    if 'actor' in task:
                        print(f"    - {task}")
        
    except Exception as e:
        print(f"❌ Celery connection error: {str(e)}")

def check_authentication_status():
    """
    Check the authentication status of all accounts.
    """
    print("\n🔐 Checking Authentication Status")
    print("=" * 35)
    
    accounts = ActorAccount.objects.all()
    
    for account in accounts:
        print(f"\n📱 Account: {account.platform_username} ({account.platform})")
        print(f"   🔐 Session valid: {account.is_session_valid()}")
        print(f"   📅 Session expires: {account.session_expires_at}")
        print(f"   ✅ Is active: {account.is_active}")
        print(f"   🚫 Is blocked: {account.is_blocked}")
        
        if not account.is_session_valid():
            print(f"   ⚠️  Session is INVALID - needs re-authentication")

if __name__ == "__main__":
    print("🐛 Actor Execute Button Debug Test")
    print("=" * 40)
    print(f"⏰ Test started at: {datetime.now()}")
    
    # Run all tests
    check_celery_connection()
    check_authentication_status()
    test_execute_button_functionality()
    
    print(f"\n⏰ Test completed at: {datetime.now()}")