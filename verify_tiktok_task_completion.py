#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append('/Users/<USER>/Documents/fullstax/backend')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor.models import ActorTask, ActorAccount, ActorScrapedData
from django.utils import timezone
import json

def verify_task_completion():
    print("🔍 Verifying TikTok Task 131 completion...")
    
    try:
        # Get the task
        task = ActorTask.objects.get(id=131)
        print(f"\n📋 Task Details:")
        print(f"  - ID: {task.id}")
        print(f"  - Name: {task.task_name}")
        print(f"  - Type: {task.task_type}")
        print(f"  - Platform: {task.platform}")
        print(f"  - Status: {task.status}")
        print(f"  - Keywords: {task.keywords}")
        print(f"  - Max Items: {task.max_items}")
        print(f"  - Items Scraped: {task.items_scraped}")
        print(f"  - Total Items Found: {task.total_items_found}")
        print(f"  - Progress: {task.progress_percentage}%")
        print(f"  - Created: {task.created_at}")
        print(f"  - Updated: {task.updated_at}")
        
        if task.completed_at:
            print(f"  - Completed: {task.completed_at}")
        
        if task.error_message:
            print(f"  - Error: {task.error_message}")
        
        # Get scraped data for this task
        scraped_data = ActorScrapedData.objects.filter(task=task).order_by('-scraped_at')
        print(f"\n📊 Scraped Data ({scraped_data.count()} records):")
        
        for i, data in enumerate(scraped_data, 1):
            print(f"\n  📹 Record {i}:")
            print(f"    - ID: {data.id}")
            print(f"    - Data Type: {data.data_type}")
            print(f"    - Platform: {data.platform}")
            print(f"    - Account: {data.account_username}")
            print(f"    - Platform Content ID: {data.platform_content_id}")
            print(f"    - Quality Score: {data.quality_score}")
            print(f"    - Is Complete: {data.is_complete}")
            print(f"    - Scraped At: {data.scraped_at}")
            
            # Show content preview
            if data.content:
                try:
                    if isinstance(data.content, str):
                        content = json.loads(data.content)
                    else:
                        content = data.content
                    
                    print(f"    - Content Preview:")
                    print(f"      * ID: {content.get('id', 'N/A')}")
                    print(f"      * Author: {content.get('author', 'N/A')}")
                    print(f"      * Description: {content.get('desc', 'N/A')[:100]}...")
                    print(f"      * URL: {content.get('url', 'N/A')}")
                    print(f"      * Search Keyword: {content.get('search_keyword', 'N/A')}")
                    print(f"      * Real Scraped: {content.get('real_scraped', 'N/A')}")
                    
                    if 'stats' in content:
                        stats = content['stats']
                        print(f"      * Stats: Views={stats.get('playCount', 'N/A')}, Likes={stats.get('diggCount', 'N/A')}, Shares={stats.get('shareCount', 'N/A')}")
                        
                except Exception as e:
                    print(f"    - Content (raw): {str(data.content)[:200]}...")
                    print(f"    - Content parse error: {e}")
        
        # Get the associated account
        account = task.actor_account if hasattr(task, 'actor_account') else None
        if not account:
            # Try to find the account used for this task
            if scraped_data.exists():
                account = scraped_data.first().actor_account
        
        if account:
            print(f"\n👤 Associated Account:")
            print(f"  - ID: {account.id}")
            print(f"  - Platform: {account.platform}")
            print(f"  - Username: {account.platform_username}")
            print(f"  - Created: {account.created_at}")
            
            # Check session validity
            if account.encrypted_session_data:
                print(f"  - Session Data: Available ({len(str(account.encrypted_session_data))} chars)")
            else:
                print(f"  - Session Data: None")
        
        # Summary
        print(f"\n✅ Task Verification Summary:")
        print(f"  - Task Status: {'✅ COMPLETED' if task.status == 'COMPLETED' else '❌ ' + task.status}")
        print(f"  - Data Records: {scraped_data.count()} saved")
        print(f"  - Target vs Actual: {task.max_items} requested, {task.items_scraped} scraped")
        print(f"  - Success Rate: {(task.items_scraped / task.max_items * 100):.1f}%" if task.max_items > 0 else "  - Success Rate: N/A")
        
        if task.status == 'COMPLETED' and scraped_data.count() > 0:
            print(f"\n🎉 TikTok Task 131 completed successfully!")
            print(f"   Real TikTok data was scraped and saved to the database.")
        else:
            print(f"\n⚠️ Task may not be fully completed or has issues.")
            
    except ActorTask.DoesNotExist:
        print("❌ Task 131 not found")
    except Exception as e:
        print(f"❌ Error verifying task: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_task_completion()