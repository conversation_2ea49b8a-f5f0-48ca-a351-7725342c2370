#!/usr/bin/env python3

"""
Test Twitter Celery Tasks
Tests the new Twitter-specific Celery tasks for async execution.
"""

import os
import sys
import django
import requests
import time

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken

def test_twitter_celery_tasks():
    print("🔄 Testing Twitter Celery Tasks")
    print("=" * 50)
    
    # Get test user and token
    user = User.objects.get(username='test_actor_user')
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    headers = {
        'Authorization': f'JWT {access_token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000/api'
    
    # Get Twitter account
    from actor.models import ActorAccount
    twitter_account = ActorAccount.objects.filter(user=user, platform='twitter').first()
    
    if not twitter_account:
        print("❌ No Twitter account found")
        return
    
    print(f"📱 Twitter Account: @{twitter_account.platform_username}")
    
    # Test cases for different Twitter task types
    test_cases = [
        {
            'task_type': 'CONTENT_SEARCH',
            'task_name': 'Celery Twitter Content Search',
            'keywords': 'prabowo',
            'description': 'Test Twitter content search via Celery'
        },
        {
            'task_type': 'FEED_SCRAPE',
            'task_name': 'Celery Twitter Feed Scrape',
            'keywords': None,
            'description': 'Test Twitter feed scraping via Celery'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ Testing: {test_case['description']}")
        print(f"   Task Type: {test_case['task_type']}")
        
        # Create task
        task_data = {
            'account_id': twitter_account.id,
            'task_type': test_case['task_type'],
            'task_name': test_case['task_name'],
            'description': test_case['description'],
            'max_items': 3,
            'task_parameters': {
                'region': 'indonesia',
                'quality_filter': 'all'
            }
        }
        
        if test_case['keywords']:
            task_data['keywords'] = test_case['keywords']
            task_data['task_parameters']['keywords'] = test_case['keywords']
        
        # Create task
        print("   📝 Creating task...")
        start_time = time.time()
        
        try:
            response = requests.post(f'{base_url}/actor/tasks/create/', 
                                   headers=headers, 
                                   json=task_data, 
                                   timeout=30)
            
            creation_time = time.time() - start_time
            print(f"   ✅ Task created in {creation_time:.2f}s")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    task_id = result.get('task_id')
                    
                    # Execute task (should dispatch to Celery)
                    print("   🚀 Executing task (dispatching to Celery)...")
                    exec_start_time = time.time()
                    
                    try:
                        exec_response = requests.post(f'{base_url}/actor/tasks/execute/', 
                                                    headers=headers, 
                                                    json={'task_id': task_id}, 
                                                    timeout=30)
                        
                        execution_time = time.time() - exec_start_time
                        print(f"   ✅ Task dispatched in {execution_time:.2f}s")
                        
                        if exec_response.status_code == 200:
                            exec_result = exec_response.json()
                            if exec_result.get('success'):
                                celery_task_id = exec_result.get('celery_task_id')
                                status = exec_result.get('status')
                                message = exec_result.get('message')
                                
                                print(f"   📊 Result:")
                                print(f"      Status: {status}")
                                print(f"      Message: {message}")
                                print(f"      Celery Task ID: {celery_task_id}")
                                
                                if celery_task_id:
                                    print(f"   🎉 SUCCESS: Task dispatched to Celery worker!")
                                    
                                    # Wait for task to complete
                                    print("   ⏳ Waiting for Celery task to complete...")
                                    
                                    # Check task status periodically
                                    for attempt in range(10):  # Wait up to 50 seconds
                                        time.sleep(5)
                                        
                                        # Check task status
                                        from actor.models import ActorTask
                                        task = ActorTask.objects.get(id=task_id)
                                        
                                        print(f"      Attempt {attempt + 1}: Status = {task.status}")
                                        
                                        if task.status == 'COMPLETED':
                                            print(f"   ✅ Celery task completed successfully!")
                                            print(f"      Items scraped: {task.items_scraped}")
                                            
                                            # Check scraped data
                                            from actor.models import ActorScrapedData
                                            scraped_data = ActorScrapedData.objects.filter(task_id=task_id)
                                            
                                            if scraped_data.exists():
                                                sample_item = scraped_data.first()
                                                if sample_item.content:
                                                    title = sample_item.content.get('title', '') or sample_item.content.get('text', '')
                                                    print(f"      Sample content: {title[:50]}...")
                                                    
                                                    # Check if it's real scraped data
                                                    is_real = sample_item.content.get('real_scraped', False)
                                                    scrape_source = sample_item.content.get('scrape_source', 'unknown')
                                                    print(f"      Real scraped: {'✅ YES' if is_real else '❌ NO'}")
                                                    print(f"      Scrape source: {scrape_source}")
                                            break
                                        elif task.status == 'FAILED':
                                            print(f"   ❌ Celery task failed: {task.error_message}")
                                            break
                                        elif task.status == 'RUNNING':
                                            print(f"      Task is still running...")
                                    else:
                                        print(f"   ⚠️  Task did not complete within timeout")
                                else:
                                    print(f"   ⚠️  No Celery task ID returned")
                            else:
                                print(f"   ❌ Task execution failed: {exec_result.get('error')}")
                        else:
                            print(f"   ❌ Task execution HTTP error: {exec_response.status_code}")
                    
                    except requests.exceptions.Timeout:
                        print(f"   🚨 TIMEOUT ERROR: Task execution exceeded 30 seconds")
                    except Exception as e:
                        print(f"   ❌ Task execution error: {str(e)}")
                else:
                    print(f"   ❌ Task creation failed: {result.get('error')}")
            else:
                print(f"   ❌ Task creation HTTP error: {response.status_code}")
        
        except requests.exceptions.Timeout:
            print(f"   🚨 TIMEOUT ERROR: Task creation exceeded 30 seconds")
        except Exception as e:
            print(f"   ❌ Task creation error: {str(e)}")
    
    # Test direct Celery task execution
    print(f"\n🔧 Testing Direct Celery Task Execution:")
    
    try:
        from actor.tasks import twitter_content_search_task
        from actor.models import ActorTask
        
        # Create a test task directly
        task = ActorTask.objects.create(
            user=user,
            actor_account=twitter_account,
            task_name='Direct Celery Test',
            task_type='CONTENT_SEARCH',
            keywords='artificial intelligence',
            max_items=2,
            task_parameters={
                'keywords': 'artificial intelligence',
                'region': 'global'
            }
        )
        
        print(f"   Created test task: {task.id}")
        
        # Execute directly via Celery
        print("   🚀 Executing task directly via Celery...")
        celery_result = twitter_content_search_task.delay(task.id)
        
        print(f"   ✅ Celery task dispatched: {celery_result.id}")
        
        # Wait for result
        print("   ⏳ Waiting for Celery result...")
        try:
            result = celery_result.get(timeout=30)  # Wait up to 30 seconds
            print(f"   ✅ Celery task completed!")
            print(f"      Result: {result}")
            
            if result.get('success'):
                items_scraped = result.get('items_scraped', 0)
                print(f"      Items scraped: {items_scraped}")
                print(f"   🎉 SUCCESS: Direct Celery execution worked!")
            else:
                print(f"      Error: {result.get('error')}")
        
        except Exception as e:
            print(f"   ❌ Celery task failed: {str(e)}")
        
    except Exception as e:
        print(f"   ❌ Direct Celery test failed: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🎯 Twitter Celery Tasks Test Summary:")
    
    print(f"\n✅ CELERY INTEGRATION:")
    print(f"   • Twitter-specific Celery tasks created")
    print(f"   • Async task dispatch implemented")
    print(f"   • Task status tracking with celery_task_id")
    print(f"   • Proper error handling and retries")
    
    print(f"\n🔧 TWITTER CELERY TASKS:")
    print(f"   • twitter_content_search_task - Search by keywords")
    print(f"   • twitter_user_scrape_task - Scrape user tweets")
    print(f"   • twitter_feed_scrape_task - Scrape Twitter feed")
    
    print(f"\n⚡ PERFORMANCE BENEFITS:")
    print(f"   • Non-blocking task execution")
    print(f"   • Background processing with Selenium")
    print(f"   • Scalable worker architecture")
    print(f"   • Automatic retry on failures")
    
    print(f"\n🎉 RESULT:")
    print(f"   Twitter engine now runs on Celery workers!")
    print(f"   Tasks are processed asynchronously in the background")
    print(f"   Users get immediate response while scraping happens")
    print(f"   Real Twitter data scraped via Selenium! 🚀")

if __name__ == '__main__':
    test_twitter_celery_tasks()
