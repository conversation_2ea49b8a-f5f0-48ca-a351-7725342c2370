#!/usr/bin/env python3
import os
import sys
import django
import time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from crawler_tiktok.models import TikTokTask, ScrapedData
from crawler_tiktok.tasks import scrape_tiktok_search_task

def test_prabowo_scraping():
    print("Creating TikTok search task for 'prabowo'...")
    
    # Create a TikTok task
    task = TikTokTask.objects.create(
        job_name="Test Prabowo Search",
        task_type="SEARCH",
        identifier="prabowo",
        status="PENDING"
    )
    
    print(f"Task created with ID: {task.id}")
    
    try:
        print("Starting scraping task...")
        # Submit task to Celery
        result = scrape_tiktok_search_task.delay(task.id, "prabowo")
        print(f"Task submitted to Celery with ID: {result.id}")
        
        # Wait for task to complete
        max_wait = 60  # 60 seconds max
        wait_time = 0
        while wait_time < max_wait:
            task.refresh_from_db()
            print(f"Task status: {task.status}")
            
            if task.status in ['COMPLETED', 'FAILED']:
                break
                
            time.sleep(5)
            wait_time += 5
        
        # Check results
        task.refresh_from_db()
        if task.status == 'FAILED':
            print(f"Error message: {task.error_message}")
        
        scraped_data = ScrapedData.objects.filter(task=task)
        print(f"Results count: {scraped_data.count()}")
        
        if scraped_data.exists():
            print("\nFirst few results:")
            for i, result in enumerate(scraped_data[:3]):
                content = result.content
                if isinstance(content, dict):
                    desc = content.get('desc', 'No description')
                    author = content.get('author', 'Unknown author')
                    print(f"  {i+1}. {desc[:100]}... (by {author})")
                else:
                    print(f"  {i+1}. {str(content)[:100]}...")
        else:
            print("No results found.")
            
            # Let's try a simpler approach - just check if we can access TikTok
            print("\nTrying simple TikTok access test...")
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            
            driver = None
            try:
                driver = webdriver.Chrome(options=chrome_options)
                driver.get("https://www.tiktok.com/search?q=prabowo")
                time.sleep(3)
                print(f"Page title: {driver.title}")
                print(f"Page loaded successfully: {len(driver.page_source)} characters")
                
                # Try to find any content
                from selenium.webdriver.common.by import By
                elements = driver.find_elements(By.CSS_SELECTOR, "*[data-e2e]")
                print(f"Found {len(elements)} elements with data-e2e attributes")
                
                # Look for search-related elements
                search_elements = [elem for elem in elements if 'search' in elem.get_attribute('data-e2e')]
                print(f"Found {len(search_elements)} search-related elements")
                
                for elem in search_elements[:5]:
                    print(f"  - {elem.get_attribute('data-e2e')}: {elem.text[:50]}...")
                    
            except Exception as e:
                print(f"Simple test failed: {e}")
            finally:
                if driver:
                    driver.quit()
        
    except Exception as e:
        print(f"Error during scraping: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_prabowo_scraping()