#!/usr/bin/env python3
"""
Test script to verify WebDriver session management system
"""

import os
import sys
import django
from django.conf import settings

# Add the backend directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/fullstax/backend')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from actor.models import ActorAccount
from actor.services.actor_service import ActorService
from actor.webdriver_manager import WebDriverSessionManager

def test_webdriver_session_system():
    """
    Test the WebDriver session management system
    """
    print("\n=== Testing WebDriver Session Management System ===")
    
    # Initialize services
    actor_service = ActorService()
    webdriver_manager = WebDriverSessionManager()
    
    try:
        # Get or create test user
        user, created = User.objects.get_or_create(
            username='test_webdriver_user',
            defaults={'email': '<EMAIL>'}
        )
        print(f"✓ Test user {'created' if created else 'retrieved'}: {user.username}")
        
        # Create test account
        account_result = actor_service.create_account(
            user=user,
            platform='tiktok',
            username='test_tiktok_user',
            password='test_password123',
            email='<EMAIL>'
        )
        
        if account_result['success']:
            account_id = account_result['account']['id']
            print(f"✓ Test account created with ID: {account_id}")
        else:
            # Account might already exist, try to get it
            accounts = actor_service.get_user_accounts(user, 'tiktok')
            if accounts:
                account_id = accounts[0]['id']
                print(f"✓ Using existing test account with ID: {account_id}")
            else:
                print(f"✗ Failed to create or find test account: {account_result.get('error')}")
                return False
        
        # Test WebDriver session creation
        print("\n--- Testing WebDriver Session Creation ---")
        session_result = actor_service.get_or_create_webdriver_session(account_id)
        
        if session_result['success']:
            print(f"✓ WebDriver session created successfully")
            print(f"  Session key: {session_result.get('session_key')}")
            print(f"  Login status: {session_result.get('login_status')}")
        else:
            print(f"✗ Failed to create WebDriver session: {session_result.get('error')}")
            return False
        
        # Test account details with WebDriver status
        print("\n--- Testing Account Details ---")
        details_result = actor_service.get_account_details(user, account_id)
        
        if details_result['success']:
            account_details = details_result['account']
            print(f"✓ Account details retrieved:")
            print(f"  WebDriver active: {account_details.get('webdriver_active')}")
            print(f"  Last used: {account_details.get('last_used')}")
            print(f"  Session key: {account_details.get('webdriver_session_key')}")
        else:
            print(f"✗ Failed to get account details: {details_result.get('error')}")
            return False
        
        # Test WebDriver session closure
        print("\n--- Testing WebDriver Session Closure ---")
        close_result = actor_service.close_webdriver_session(account_id)
        
        if close_result['success']:
            print(f"✓ WebDriver session closed successfully")
        else:
            print(f"✗ Failed to close WebDriver session: {close_result.get('error')}")
            return False
        
        # Verify session is closed
        details_result = actor_service.get_account_details(user, account_id)
        if details_result['success']:
            account_details = details_result['account']
            print(f"✓ Verified session closure:")
            print(f"  WebDriver active: {account_details.get('webdriver_active')}")
        
        print("\n=== All WebDriver Session Tests Passed! ===")
        return True
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_webdriver_session_system()
    sys.exit(0 if success else 1)