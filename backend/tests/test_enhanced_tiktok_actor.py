#!/usr/bin/env python3
"""
Comprehensive test script for Enhanced TikTok Actor

This script tests the enhanced TikTok login and scraping functionality
with proper error handling and logging.
"""

import os
import sys
import django
import logging
from datetime import datetime
import traceback

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('enhanced_tiktok_test.log')
    ]
)
logger = logging.getLogger(__name__)

# Import Django models and utilities
from django.contrib.auth.models import User
from actor_tiktok.models import TikTokUserAccount, ActorScrapedData
from django.utils import timezone

def test_imports():
    """Test if all enhanced modules can be imported"""
    logger.info("=== Testing Enhanced Module Imports ===")
    
    try:
        # Test core imports
        from actor_tiktok.utils.enhanced_tiktok_auth import EnhancedTikTokAuthenticator
        logger.info("✅ EnhancedTikTokAuthenticator imported successfully")
        
        from actor_tiktok.utils.proxy_manager import ProxyManager
        logger.info("✅ ProxyManager imported successfully")
        
        from actor_tiktok.utils.enhanced_scraper import EnhancedTikTokScraper
        logger.info("✅ EnhancedTikTokScraper imported successfully")
        
        from actor_tiktok.utils.anti_detection import AntiDetectionManager
        logger.info("✅ AntiDetectionManager imported successfully")
        
        from actor_tiktok.utils.session_manager import EnhancedSessionManager
        logger.info("✅ EnhancedSessionManager imported successfully")
        
        from actor_tiktok.tasks import enhanced_actor_login_task, enhanced_scraping_task
        logger.info("✅ Enhanced Celery tasks imported successfully")
        
        from actor_tiktok.config.enhanced_config import ENHANCED_ANTI_DETECTION, PROXY_CONFIG
        logger.info("✅ Enhanced configuration imported successfully")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error during import: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_anti_detection_setup():
    """Test anti-detection manager setup"""
    logger.info("=== Testing Anti-Detection Setup ===")
    
    try:
        from actor_tiktok.utils.anti_detection import AntiDetectionManager
        
        # Initialize anti-detection manager
        anti_detection = AntiDetectionManager()
        logger.info("✅ AntiDetectionManager initialized")
        
        # Test driver setup (without actually creating driver)
        logger.info("Testing driver configuration...")
        
        # Check if Chrome is available
        try:
            import selenium
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            logger.info(f"✅ Selenium version: {selenium.__version__}")
            
            # Test Chrome options setup
            options = Options()
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            logger.info("✅ Chrome options configured successfully")
            
        except ImportError as e:
            logger.error(f"❌ Selenium import failed: {str(e)}")
            return False
        
        # Test undetected chrome import
        try:
            import undetected_chromedriver as uc
            logger.info("✅ Undetected Chrome driver available")
        except ImportError:
            logger.warning("⚠️ Undetected Chrome driver not available - will use regular Chrome")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Anti-detection setup failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_proxy_manager():
    """Test proxy manager functionality"""
    logger.info("=== Testing Proxy Manager ===")
    
    try:
        from actor_tiktok.utils.proxy_manager import ProxyManager
        
        # Test with empty proxy list
        proxy_manager = ProxyManager(proxies=[])
        logger.info("✅ ProxyManager initialized with empty proxy list")
        
        # Test proxy statistics
        stats = proxy_manager.get_proxy_stats()
        logger.info(f"✅ Proxy stats: {stats}")
        
        # Test with sample proxy configuration
        sample_proxies = [
            {
                'host': '127.0.0.1',
                'port': 8080,
                'protocol': 'http'
            }
        ]
        
        proxy_manager_with_proxies = ProxyManager(proxies=sample_proxies)
        logger.info("✅ ProxyManager initialized with sample proxies")
        
        # Test proxy selection (should return None since proxy isn't real)
        proxy = proxy_manager_with_proxies.get_next_proxy()
        logger.info(f"✅ Proxy selection test completed (proxy: {proxy})")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Proxy manager test failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_session_manager():
    """Test enhanced session manager"""
    logger.info("=== Testing Enhanced Session Manager ===")
    
    try:
        from actor_tiktok.utils.session_manager import EnhancedSessionManager
        
        # Initialize session manager
        session_manager = EnhancedSessionManager()
        logger.info("✅ EnhancedSessionManager initialized")
        
        # Test session data structure
        test_session_data = {
            'cookies': [{'name': 'test', 'value': 'test_value'}],
            'local_storage': {'test_key': 'test_value'},
            'user_agent': 'Mozilla/5.0 Test Agent',
            'timestamp': timezone.now().isoformat()
        }
        
        # Test session validation (without actual account)
        logger.info("✅ Session data structure test completed")
        
        # Test backup functionality
        backup_id = session_manager.create_session_backup(999)  # Non-existent account
        logger.info(f"✅ Session backup test completed (backup_id: {backup_id})")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Session manager test failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_database_models():
    """Test database models and connections"""
    logger.info("=== Testing Database Models ===")
    
    try:
        # Test User model
        user_count = User.objects.count()
        logger.info(f"✅ User model accessible - {user_count} users in database")
        
        # Test TikTokUserAccount model
        account_count = TikTokUserAccount.objects.count()
        logger.info(f"✅ TikTokUserAccount model accessible - {account_count} accounts in database")
        
        # Test ActorScrapedData model
        data_count = ActorScrapedData.objects.count()
        logger.info(f"✅ ActorScrapedData model accessible - {data_count} records in database")
        
        # Create test user if none exists
        if user_count == 0:
            test_user = User.objects.create_user(
                username='test_user',
                email='<EMAIL>',
                password='test_password'
            )
            logger.info(f"✅ Created test user: {test_user.username}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Database model test failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_enhanced_authenticator():
    """Test enhanced authenticator initialization"""
    logger.info("=== Testing Enhanced Authenticator ===")
    
    try:
        from actor_tiktok.utils.enhanced_tiktok_auth import EnhancedTikTokAuthenticator
        
        # Test initialization without proxy
        authenticator = EnhancedTikTokAuthenticator()
        logger.info("✅ EnhancedTikTokAuthenticator initialized without proxy")
        
        # Test initialization with proxy config
        proxy_config = {
            'enabled': False,
            'proxies': [],
            'max_failures': 3
        }
        
        authenticator_with_proxy = EnhancedTikTokAuthenticator(proxy_config=proxy_config)
        logger.info("✅ EnhancedTikTokAuthenticator initialized with proxy config")
        
        # Test login strategies availability
        strategies = authenticator.login_strategies
        logger.info(f"✅ Available login strategies: {[s[0] for s in strategies]}")
        
        # Test recovery strategies
        recovery_strategies = list(authenticator.recovery_strategies.keys())
        logger.info(f"✅ Available recovery strategies: {recovery_strategies}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Enhanced authenticator test failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_enhanced_scraper():
    """Test enhanced scraper initialization"""
    logger.info("=== Testing Enhanced Scraper ===")
    
    try:
        from actor_tiktok.utils.enhanced_scraper import EnhancedTikTokScraper
        
        # Test initialization
        scraper = EnhancedTikTokScraper()
        logger.info("✅ EnhancedTikTokScraper initialized")
        
        # Test with configurations
        proxy_config = {'enabled': False, 'proxies': []}
        rate_limit_config = {
            'requests_per_minute': 30,
            'requests_per_hour': 500,
            'delay_between_requests': (2, 5)
        }
        
        scraper_with_config = EnhancedTikTokScraper(
            proxy_config=proxy_config,
            rate_limit_config=rate_limit_config
        )
        logger.info("✅ EnhancedTikTokScraper initialized with configurations")
        
        # Test rate limiting check
        rate_limit_ok = scraper_with_config._check_rate_limit()
        logger.info(f"✅ Rate limit check: {rate_limit_ok}")
        
        # Test selectors
        selectors = scraper_with_config.selectors
        logger.info(f"✅ Scraper selectors available: {len(selectors)} selectors")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Enhanced scraper test failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_celery_tasks():
    """Test Celery task definitions"""
    logger.info("=== Testing Celery Tasks ===")
    
    try:
        from actor_tiktok.tasks import enhanced_actor_login_task, enhanced_scraping_task
        
        # Test task definitions
        logger.info(f"✅ Enhanced login task: {enhanced_actor_login_task.name}")
        logger.info(f"✅ Enhanced scraping task: {enhanced_scraping_task.name}")
        
        # Test task configuration
        logger.info(f"✅ Login task max retries: {enhanced_actor_login_task.max_retries}")
        logger.info(f"✅ Scraping task max retries: {enhanced_scraping_task.max_retries}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Celery tasks test failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_configuration():
    """Test configuration loading"""
    logger.info("=== Testing Configuration ===")
    
    try:
        from actor_tiktok.config.enhanced_config import (
            ENHANCED_ANTI_DETECTION,
            PROXY_CONFIG,
            RATE_LIMITING_CONFIG,
            SCRAPING_CONFIG,
            ERROR_HANDLING_CONFIG
        )
        
        logger.info("✅ Enhanced anti-detection config loaded")
        logger.info("✅ Proxy config loaded")
        logger.info("✅ Rate limiting config loaded")
        logger.info("✅ Scraping config loaded")
        logger.info("✅ Error handling config loaded")
        
        # Test configuration values
        logger.info(f"✅ Use undetected Chrome: {ENHANCED_ANTI_DETECTION.get('use_undetected_chrome')}")
        logger.info(f"✅ Proxy enabled: {PROXY_CONFIG.get('enabled')}")
        logger.info(f"✅ Requests per minute: {RATE_LIMITING_CONFIG.get('requests_per_minute')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration test failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def run_comprehensive_test():
    """Run all tests and provide summary"""
    logger.info("🚀 Starting Comprehensive Enhanced TikTok Actor Test")
    logger.info(f"Timestamp: {datetime.now().isoformat()}")
    logger.info("="*60)
    
    tests = [
        ("Module Imports", test_imports),
        ("Anti-Detection Setup", test_anti_detection_setup),
        ("Proxy Manager", test_proxy_manager),
        ("Session Manager", test_session_manager),
        ("Database Models", test_database_models),
        ("Enhanced Authenticator", test_enhanced_authenticator),
        ("Enhanced Scraper", test_enhanced_scraper),
        ("Celery Tasks", test_celery_tasks),
        ("Configuration", test_configuration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - PASSED")
            else:
                logger.error(f"❌ {test_name} - FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - EXCEPTION: {str(e)}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*60}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    logger.info(f"Tests passed: {passed}/{total}")
    logger.info(f"Success rate: {(passed/total)*100:.1f}%")
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED! Enhanced TikTok Actor is ready to use.")
    else:
        logger.warning(f"⚠️ {total - passed} tests failed. Please check the logs above.")
    
    logger.info("🏁 Test completed!")
    return results

if __name__ == "__main__":
    results = run_comprehensive_test()
