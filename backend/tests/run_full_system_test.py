#!/usr/bin/env python3
"""
Full System Test Runner
Tests both backend API and frontend integration with real TikTok account
"""

import os
import sys
import time
import requests
import subprocess
import signal
from datetime import datetime

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class FullSystemTestRunner:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'backend_tests': {},
            'frontend_tests': {},
            'api_tests': {},
            'integration_tests': {}
        }
        
        print("🎭 Full Actor System Test Runner")
        print("=" * 50)
        print(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("")

    def start_backend_server(self):
        """Start Django backend server"""
        print("🔧 Starting Django backend server...")
        
        try:
            # Change to backend directory and start server
            backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            
            self.backend_process = subprocess.Popen(
                ['bash', '-c', 'source venv/bin/activate && python3 manage.py runserver 8000'],
                cwd=backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid
            )
            
            # Wait for server to start
            for i in range(30):  # Wait up to 30 seconds
                try:
                    response = requests.get('http://localhost:8000/api/actor/health/', timeout=2)
                    if response.status_code == 200:
                        print("✅ Django backend server is running on http://localhost:8000")
                        return True
                except:
                    time.sleep(1)
            
            print("❌ Django backend server failed to start")
            return False
            
        except Exception as e:
            print(f"❌ Error starting backend server: {str(e)}")
            return False

    def start_frontend_server(self):
        """Start Next.js frontend server"""
        print("🌐 Starting Next.js frontend server...")
        
        try:
            # Change to frontend directory and start server
            frontend_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'frontend')
            
            if not os.path.exists(frontend_dir):
                print("⚠️ Frontend directory not found, skipping frontend server")
                return False
            
            self.frontend_process = subprocess.Popen(
                ['npm', 'run', 'dev'],
                cwd=frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid
            )
            
            # Wait for server to start
            for i in range(60):  # Wait up to 60 seconds for frontend
                try:
                    response = requests.get('http://localhost:3000', timeout=2)
                    if response.status_code == 200:
                        print("✅ Next.js frontend server is running on http://localhost:3000")
                        return True
                except:
                    time.sleep(1)
            
            print("❌ Next.js frontend server failed to start")
            return False
            
        except Exception as e:
            print(f"❌ Error starting frontend server: {str(e)}")
            return False

    def test_api_endpoints(self):
        """Test API endpoints"""
        print("\n🔗 Testing API Endpoints...")
        print("-" * 30)
        
        endpoints = [
            ('GET', '/api/actor/health/', 200, 'System Health'),
            ('GET', '/api/actor/platforms/', 200, 'Available Platforms'),
            ('GET', '/api/actor/accounts/list/', 401, 'Account List (No Auth)'),
            ('GET', '/api/actor/tasks/list/', 401, 'Task List (No Auth)'),
            ('GET', '/api/actor/data/', 401, 'Scraped Data (No Auth)'),
        ]
        
        passed = 0
        total = len(endpoints)
        
        for method, endpoint, expected_status, description in endpoints:
            try:
                url = f"http://localhost:8000{endpoint}"
                response = requests.get(url, timeout=5)
                
                if response.status_code == expected_status:
                    print(f"✅ {description} - {response.status_code}")
                    passed += 1
                else:
                    print(f"❌ {description} - {response.status_code} (expected {expected_status})")
                    
            except Exception as e:
                print(f"❌ {description} - ERROR: {str(e)}")
        
        success_rate = (passed / total) * 100
        print(f"\n📊 API Tests: {passed}/{total} passed ({success_rate:.1f}%)")
        
        self.test_results['api_tests'] = {
            'total': total,
            'passed': passed,
            'success_rate': success_rate
        }
        
        return success_rate > 80

    def test_frontend_pages(self):
        """Test frontend pages"""
        print("\n🌐 Testing Frontend Pages...")
        print("-" * 30)
        
        pages = [
            ('/', 'Home Page'),
            ('/actor', 'Actor Dashboard'),
            ('/actor/accounts', 'Actor Accounts'),
            ('/actor/accounts/add', 'Add Account'),
            ('/actor/tasks', 'Actor Tasks'),
            ('/actor/data', 'Actor Data'),
            ('/actor/sessions', 'Actor Sessions'),
        ]
        
        passed = 0
        total = len(pages)
        
        for path, description in pages:
            try:
                url = f"http://localhost:3000{path}"
                response = requests.get(url, timeout=10)
                
                if response.status_code == 200:
                    print(f"✅ {description} - {response.status_code}")
                    passed += 1
                else:
                    print(f"❌ {description} - {response.status_code}")
                    
            except Exception as e:
                print(f"❌ {description} - ERROR: {str(e)}")
        
        success_rate = (passed / total) * 100
        print(f"\n📊 Frontend Tests: {passed}/{total} passed ({success_rate:.1f}%)")
        
        self.test_results['frontend_tests'] = {
            'total': total,
            'passed': passed,
            'success_rate': success_rate
        }
        
        return success_rate > 70

    def run_backend_tests(self):
        """Run backend Actor system tests"""
        print("\n🧪 Running Backend Actor System Tests...")
        print("-" * 40)
        
        try:
            backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            
            result = subprocess.run(
                ['bash', '-c', 'source venv/bin/activate && python3 tests/test_complete_actor_system.py'],
                cwd=backend_dir,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes timeout
            )
            
            print(result.stdout)
            if result.stderr:
                print("STDERR:", result.stderr)
            
            # Parse success rate from output
            success_rate = 0
            if "Success Rate:" in result.stdout:
                try:
                    rate_line = [line for line in result.stdout.split('\n') if 'Success Rate:' in line][0]
                    success_rate = float(rate_line.split('Success Rate: ')[1].replace('%', ''))
                except:
                    pass
            
            self.test_results['backend_tests'] = {
                'success_rate': success_rate,
                'output': result.stdout,
                'return_code': result.returncode
            }
            
            return result.returncode == 0
            
        except subprocess.TimeoutExpired:
            print("❌ Backend tests timed out")
            return False
        except Exception as e:
            print(f"❌ Error running backend tests: {str(e)}")
            return False

    def cleanup_servers(self):
        """Stop all servers"""
        print("\n🧹 Stopping servers...")
        
        if self.backend_process:
            try:
                os.killpg(os.getpgid(self.backend_process.pid), signal.SIGTERM)
                print("✅ Django server stopped")
            except:
                pass
        
        if self.frontend_process:
            try:
                os.killpg(os.getpgid(self.frontend_process.pid), signal.SIGTERM)
                print("✅ Frontend server stopped")
            except:
                pass

    def generate_report(self):
        """Generate final test report"""
        print("\n" + "=" * 60)
        print("📊 FULL SYSTEM TEST REPORT")
        print("=" * 60)
        
        api_success = self.test_results.get('api_tests', {}).get('success_rate', 0)
        frontend_success = self.test_results.get('frontend_tests', {}).get('success_rate', 0)
        backend_success = self.test_results.get('backend_tests', {}).get('success_rate', 0)
        
        print(f"🔗 API Endpoints: {api_success:.1f}%")
        print(f"🌐 Frontend Pages: {frontend_success:.1f}%")
        print(f"🧪 Backend Tests: {backend_success:.1f}%")
        
        overall_success = (api_success + frontend_success + backend_success) / 3
        print(f"📈 Overall Success: {overall_success:.1f}%")
        
        if overall_success >= 80:
            print("\n🎉 FULL SYSTEM TEST PASSED!")
            print("The Actor System is ready for production use!")
        elif overall_success >= 60:
            print("\n⚠️ PARTIAL SUCCESS")
            print("Most features are working, but some issues need attention.")
        else:
            print("\n❌ SYSTEM NEEDS WORK")
            print("Several critical issues need to be resolved.")
        
        # Save detailed results
        import json
        results_file = f"tests/full_system_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        print(f"\n💾 Detailed results saved to: {results_file}")
        
        return overall_success >= 70

    def run_full_test(self):
        """Run the complete test suite"""
        try:
            # Start servers
            backend_started = self.start_backend_server()
            if not backend_started:
                print("❌ Cannot proceed without backend server")
                return False
            
            frontend_started = self.start_frontend_server()
            
            # Run tests
            api_success = self.test_api_endpoints()
            
            if frontend_started:
                frontend_success = self.test_frontend_pages()
            else:
                print("⚠️ Skipping frontend tests (server not running)")
            
            backend_success = self.run_backend_tests()
            
            # Generate report
            overall_success = self.generate_report()
            
            return overall_success
            
        except KeyboardInterrupt:
            print("\n⚠️ Test interrupted by user")
            return False
        except Exception as e:
            print(f"\n❌ Unexpected error: {str(e)}")
            return False
        finally:
            self.cleanup_servers()

if __name__ == "__main__":
    runner = FullSystemTestRunner()
    success = runner.run_full_test()
    
    print(f"\n🏁 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    exit(0 if success else 1)
