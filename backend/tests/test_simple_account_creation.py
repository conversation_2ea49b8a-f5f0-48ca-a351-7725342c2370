#!/usr/bin/env python3
"""
Simple Account Creation Test
Tests just the account creation functionality to isolate the issue
"""

import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from actor.models import ActorAccount
from actor.services.actor_service import ActorService

def test_simple_account_creation():
    print("🧪 Simple Account Creation Test")
    print("=" * 40)
    
    # Get or create test user
    user, created = User.objects.get_or_create(
        username='test_simple_user',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'Simple'
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
    
    print(f"👤 Test User: {user.username}")
    
    # Clean up any existing test accounts
    ActorAccount.objects.filter(
        user=user,
        platform='tiktok',
        platform_username='grafisone'
    ).delete()
    
    print("🧹 Cleaned up existing test accounts")
    
    # Test direct model creation first
    print("\n🔧 Test 1: Direct Model Creation")
    try:
        account = ActorAccount.objects.create(
            user=user,
            platform='tiktok',
            platform_username='grafisone',
            email='<EMAIL>',
            password='encrypted_password_here',
            is_active=True
        )
        print(f"✅ Direct model creation successful: {account.id}")
        account.delete()  # Clean up
    except Exception as e:
        print(f"❌ Direct model creation failed: {str(e)}")
        return False
    
    # Test service creation
    print("\n🔧 Test 2: Service Creation")
    try:
        actor_service = ActorService()
        result = actor_service.create_account(
            user=user,
            platform='tiktok',
            username='grafisone',
            password='Puyol@102410',
            email='<EMAIL>'
        )
        
        if result['success']:
            print(f"✅ Service creation successful: {result['account_id']}")
            # Clean up
            ActorAccount.objects.filter(id=result['account_id']).delete()
            return True
        else:
            print(f"❌ Service creation failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Service creation exception: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_account_creation()
    if success:
        print("\n🎉 Simple account creation test PASSED!")
    else:
        print("\n💥 Simple account creation test FAILED!")
    
    exit(0 if success else 1)
