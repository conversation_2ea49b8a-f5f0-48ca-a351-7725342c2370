#!/usr/bin/env python3
import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
sys.path.append('/Users/<USER>/Documents/fullstax/backend')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor.utils.simple_tiktok_auth import SimpleTikTokAuthenticator
from actor.scrapers.real_tiktok_scraper import RealTikTokScraper
from actor.engines.tiktok_engine import TikTokEngine
from actor.models import ActorAccount

def test_simple_tiktok_auth():
    """Test SimpleTikTokAuthenticator"""
    print("\n=== Testing SimpleTikTokAuthenticator ===")
    
    try:
        # Test with dummy credentials
        auth = SimpleTikTokAuthenticator()
        print("SimpleTikTokAuthenticator initialized successfully")
        
        # Check if we can create the authenticator without errors
        print("✓ SimpleTikTokAuthenticator can be instantiated")
        
        # Test login method exists
        if hasattr(auth, 'login'):
            print("✓ login method exists")
        else:
            print("✗ login method missing")
            
    except Exception as e:
        print(f"✗ Error with SimpleTikTokAuthenticator: {e}")
        import traceback
        traceback.print_exc()

def test_real_tiktok_scraper():
    """Test RealTikTokScraper"""
    print("\n=== Testing RealTikTokScraper ===")
    
    try:
        scraper = RealTikTokScraper()
        print("✓ RealTikTokScraper initialized successfully")
        
        # Check if login method exists
        if hasattr(scraper, 'login'):
            print("✓ login method exists")
        else:
            print("✗ login method missing")
            
        # Check if search_videos method exists
        if hasattr(scraper, 'search_videos'):
            print("✓ search_videos method exists")
        else:
            print("✗ search_videos method missing")
            
    except Exception as e:
        print(f"✗ Error with RealTikTokScraper: {e}")
        import traceback
        traceback.print_exc()

def test_tiktok_engine():
    """Test TikTokEngine"""
    print("\n=== Testing TikTokEngine ===")
    
    try:
        engine = TikTokEngine()
        print("✓ TikTokEngine initialized successfully")
        
        # Check if authenticate method exists
        if hasattr(engine, 'authenticate'):
            print("✓ authenticate method exists")
        else:
            print("✗ authenticate method missing")
            
        # Check if verify_session method exists
        if hasattr(engine, 'verify_session'):
            print("✓ verify_session method exists")
        else:
            print("✗ verify_session method missing")
            
    except Exception as e:
        print(f"✗ Error with TikTokEngine: {e}")
        import traceback
        traceback.print_exc()

def test_actor_account_model():
    """Test ActorAccount model"""
    print("\n=== Testing ActorAccount Model ===")
    
    try:
        # Check if we can query ActorAccount
        tiktok_accounts = ActorAccount.objects.filter(platform='tiktok')
        print(f"✓ Found {tiktok_accounts.count()} TikTok accounts in database")
        
        # Check if we can create a test account (without saving)
        test_account = ActorAccount(
            platform='tiktok',
            username='test_user',
            password='test_pass',
            is_active=True
        )
        print("✓ ActorAccount model can be instantiated")
        
    except Exception as e:
        print(f"✗ Error with ActorAccount model: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("Starting TikTok Login System Tests...")
    
    test_simple_tiktok_auth()
    test_real_tiktok_scraper()
    test_tiktok_engine()
    test_actor_account_model()
    
    print("\n=== Test Summary ===")
    print("Tests completed. Check above for any errors.")
    print("\nNext steps to debug login issues:")
    print("1. Check Chrome/ChromeDriver installation")
    print("2. Verify TikTok login page selectors are up-to-date")
    print("3. Test with valid TikTok credentials")
    print("4. Check for CAPTCHA or anti-bot measures")

if __name__ == '__main__':
    main()