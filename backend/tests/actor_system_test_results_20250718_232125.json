{"timestamp": "2025-07-18T23:21:13.084221", "tests": {"create_actor_account": {"success": false, "message": "Exception during account creation: Cannot resolve keyword 'username' into field. Choices are: blocked_until, created_at, email, encrypted_session_data, id, is_active, is_blocked, last_attempt_at, last_login, login_attempts, password, platform, platform_user_id, platform_username, scraped_data, session_expires_at, sessions, tasks, updated_at, user, user_id", "data": null, "timestamp": "2025-07-18T23:21:13.096775"}, "authenticate_account": {"success": false, "message": "Exception during authentication: 'CompleteActorSystemTest' object has no attribute 'account_id'", "data": null, "timestamp": "2025-07-18T23:21:15.097096"}, "create_scraping_task": {"success": false, "message": "Exception during task creation: 'CompleteActorSystemTest' object has no attribute 'account_id'", "data": null, "timestamp": "2025-07-18T23:21:17.120033"}, "execute_scraping_task": {"success": false, "message": "Exception during task execution: 'CompleteActorSystemTest' object has no attribute 'task_id'", "data": null, "timestamp": "2025-07-18T23:21:19.126060"}, "verify_scraped_data": {"success": false, "message": "Exception during data verification: 'CompleteActorSystemTest' object has no attribute 'task_id'", "data": null, "timestamp": "2025-07-18T23:21:21.129970"}, "data_quality_check": {"success": false, "message": "Failed to get data quality stats: Cannot resolve keyword 'user' into field. Choices are: account_username, actor_account, actor_account_id, content, data_type, id, is_complete, platform, platform_content_id, quality_score, scraped_at, task, task_id, tiktok_id", "data": null, "timestamp": "2025-07-18T23:21:23.133881"}}, "summary": {"total_tests": 6, "passed_tests": 0, "failed_tests": 6, "success_rate": "0.0%"}}