#!/usr/bin/env python3
import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
sys.path.append('/Users/<USER>/Documents/fullstax/backend')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor.utils.simple_tiktok_auth import SimpleTikTokAuthenticator
from actor.scrapers.real_tiktok_scraper import RealTikTokScraper
from actor.engines.tiktok_engine import TikTokEngine
from actor.models import ActorAccount
from actor.services.actor_service import ActorService
from django.contrib.auth.models import User

def test_actor_account_creation():
    """Test creating ActorAccount with correct fields"""
    print("\n=== Testing ActorAccount Creation ===")
    
    try:
        # Get or create a test user
        test_user, created = User.objects.get_or_create(
            username='test_user',
            defaults={'email': '<EMAIL>'}
        )
        
        # Test creating ActorAccount with correct fields
        test_account = ActorAccount(
            user=test_user,
            platform='tiktok',
            platform_username='test_tiktok_user',
            password='test_password',
            encrypted_session_data='{}',
            is_active=True
        )
        print("✓ ActorAccount can be instantiated with correct fields")
        
        # Test password encryption
        test_account.encrypt_password('test_password')
        print("✓ Password encryption works")
        
        # Test password decryption
        decrypted = test_account.decrypt_password()
        print(f"✓ Password decryption works: {decrypted == 'test_password'}")
        
        # Test session data encryption
        test_session = {'cookies': [], 'user_agent': 'test'}
        test_account.encrypt_session_data(test_session)
        print("✓ Session data encryption works")
        
        # Test session data decryption
        decrypted_session = test_account.decrypt_session_data()
        print(f"✓ Session data decryption works: {decrypted_session == test_session}")
        
    except Exception as e:
        print(f"✗ Error with ActorAccount creation: {e}")
        import traceback
        traceback.print_exc()

def test_actor_service():
    """Test ActorService functionality"""
    print("\n=== Testing ActorService ===")
    
    try:
        service = ActorService()
        print("✓ ActorService initialized successfully")
        
        # Check if authenticate_account method exists
        if hasattr(service, 'authenticate_account'):
            print("✓ authenticate_account method exists")
        else:
            print("✗ authenticate_account method missing")
            
        # Check if create_task method exists
        if hasattr(service, 'create_task'):
            print("✓ create_task method exists")
        else:
            print("✗ create_task method missing")
            
    except Exception as e:
        print(f"✗ Error with ActorService: {e}")
        import traceback
        traceback.print_exc()

def test_tiktok_authentication_flow():
    """Test the complete TikTok authentication flow"""
    print("\n=== Testing TikTok Authentication Flow ===")
    
    try:
        # Test SimpleTikTokAuthenticator
        auth = SimpleTikTokAuthenticator()
        print("✓ SimpleTikTokAuthenticator created")
        
        # Test if we can call login method (without actual credentials)
        print("Testing login method signature...")
        try:
            # This will fail but we can check if the method accepts the right parameters
            result = auth.login('', '')
            print(f"✓ Login method callable, returned: {type(result)}")
        except Exception as e:
            if "username" in str(e).lower() or "password" in str(e).lower():
                print("✓ Login method expects username/password parameters")
            else:
                print(f"Login method error: {e}")
        
        # Test RealTikTokScraper
        scraper = RealTikTokScraper()
        print("✓ RealTikTokScraper created")
        
        # Test TikTokEngine
        engine = TikTokEngine()
        print("✓ TikTokEngine created")
        
        # Test if we can call authenticate method
        print("Testing engine authenticate method...")
        try:
            # Create a dummy account for testing
            test_user, _ = User.objects.get_or_create(
                username='test_engine_user',
                defaults={'email': '<EMAIL>'}
            )
            
            dummy_account = ActorAccount(
                user=test_user,
                platform='tiktok',
                platform_username='dummy_user',
                password='dummy_pass',
                encrypted_session_data='{}',
                is_active=True
            )
            
            # Test authenticate method signature
            result = engine.authenticate(dummy_account)
            print(f"✓ Engine authenticate method callable, returned: {type(result)}")
            
        except Exception as e:
            print(f"Engine authenticate error: {e}")
            
    except Exception as e:
        print(f"✗ Error in authentication flow test: {e}")
        import traceback
        traceback.print_exc()

def test_existing_tiktok_accounts():
    """Test existing TikTok accounts in database"""
    print("\n=== Testing Existing TikTok Accounts ===")
    
    try:
        # Get all TikTok accounts
        tiktok_accounts = ActorAccount.objects.filter(platform='tiktok')
        print(f"Found {tiktok_accounts.count()} TikTok accounts")
        
        for i, account in enumerate(tiktok_accounts[:3]):  # Show first 3
            print(f"Account {i+1}:")
            print(f"  - Username: {account.platform_username}")
            print(f"  - Active: {account.is_active}")
            print(f"  - Last Login: {account.last_login}")
            print(f"  - Session Valid: {account.is_session_valid()}")
            print(f"  - Blocked: {account.is_blocked}")
            
            # Test if we can decrypt session data
            try:
                session_data = account.decrypt_session_data()
                print(f"  - Session Data Keys: {list(session_data.keys()) if session_data else 'None'}")
            except Exception as e:
                print(f"  - Session Data Error: {e}")
                
    except Exception as e:
        print(f"✗ Error checking existing accounts: {e}")
        import traceback
        traceback.print_exc()

def test_chrome_webdriver():
    """Test Chrome WebDriver availability"""
    print("\n=== Testing Chrome WebDriver ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        import undetected_chromedriver as uc
        
        print("✓ Selenium and undetected_chromedriver imported successfully")
        
        # Test Chrome options
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        print("✓ Chrome options configured")
        
        # Test if we can create a driver (this might fail if Chrome is not installed)
        try:
            driver = uc.Chrome(options=chrome_options)
            print("✓ Chrome WebDriver created successfully")
            driver.quit()
            print("✓ Chrome WebDriver closed successfully")
        except Exception as e:
            print(f"Chrome WebDriver error: {e}")
            print("This might indicate Chrome/ChromeDriver installation issues")
            
    except Exception as e:
        print(f"✗ WebDriver test error: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("Starting Comprehensive TikTok Login System Tests...")
    
    test_actor_account_creation()
    test_actor_service()
    test_tiktok_authentication_flow()
    test_existing_tiktok_accounts()
    test_chrome_webdriver()
    
    print("\n=== Comprehensive Test Summary ===")
    print("Tests completed. Check above for any errors.")
    print("\nCommon issues and solutions:")
    print("1. Chrome/ChromeDriver not installed: Install Chrome browser and ChromeDriver")
    print("2. TikTok login selectors outdated: Update CSS selectors in SimpleTikTokAuthenticator")
    print("3. Account blocked: Check is_blocked field and blocked_until timestamp")
    print("4. Session expired: Check session_expires_at field")
    print("5. CAPTCHA/anti-bot: TikTok may be detecting automation")
    print("\nTo test with real credentials:")
    print("- Create a TikTok account in the admin panel")
    print("- Use the authenticate endpoint with valid credentials")
    print("- Check the response for specific error messages")

if __name__ == '__main__':
    main()