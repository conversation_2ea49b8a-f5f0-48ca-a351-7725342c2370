{"timestamp": "2025-07-19T06:34:51.142262", "base_url": "http://localhost:8000", "summary": {"total": 20, "passed": 18, "failed": 2, "success_rate": "90.0%"}, "results": [{"endpoint": "/api/actor/health/", "method": "GET", "status_code": 200, "expected_status": 200, "success": true, "description": "System health check", "response_preview": "{'success': True, 'status': 'healthy', 'message': 'All systems operational', 'components': {'databas...", "timestamp": "2025-07-19T06:34:50.990845"}, {"endpoint": "/api/actor/platforms/", "method": "GET", "status_code": 200, "expected_status": 200, "success": true, "description": "Available platforms", "response_preview": "{'success': True, 'platforms': ['tiktok', 'instagram', 'facebook', 'twitter', 'youtube']}", "timestamp": "2025-07-19T06:34:50.993829"}, {"endpoint": "/api/actor/accounts/list/", "method": "GET", "status_code": 401, "expected_status": 401, "success": true, "description": "List accounts (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:34:51.004479"}, {"endpoint": "/api/actor/accounts/create/", "method": "POST", "status_code": 401, "expected_status": 401, "success": true, "description": "Create account (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:34:51.008161"}, {"endpoint": "/api/actor/accounts/authenticate/", "method": "POST", "status_code": 401, "expected_status": 401, "success": true, "description": "Authenticate account (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:34:51.012073"}, {"endpoint": "/api/actor/tasks/list/", "method": "GET", "status_code": 401, "expected_status": 401, "success": true, "description": "List tasks (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:34:51.023557"}, {"endpoint": "/api/actor/tasks/create/", "method": "POST", "status_code": 401, "expected_status": 401, "success": true, "description": "Create task (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:34:51.027620"}, {"endpoint": "/api/actor/tasks/execute/", "method": "POST", "status_code": 401, "expected_status": 401, "success": true, "description": "Execute task (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:34:51.036852"}, {"endpoint": "/api/actor/data/", "method": "GET", "status_code": 401, "expected_status": 401, "success": true, "description": "Get scraped data (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:34:51.043707"}, {"endpoint": "/api/actor/data/stats/", "method": "GET", "status_code": 401, "expected_status": 401, "success": true, "description": "Data statistics (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:34:51.055288"}, {"endpoint": "/api/actor/accounts/1/", "method": "PUT", "status_code": 401, "expected_status": 401, "success": true, "description": "Update account (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:34:51.058845"}, {"endpoint": "/api/actor/accounts/1/delete/", "method": "DELETE", "status_code": 401, "expected_status": 401, "success": true, "description": "Delete account (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:34:51.068601"}, {"endpoint": "/api/actor/tasks/1/", "method": "PUT", "status_code": 401, "expected_status": 401, "success": true, "description": "Update task (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:34:51.075511"}, {"endpoint": "/api/actor/tasks/1/delete/", "method": "DELETE", "status_code": 401, "expected_status": 401, "success": true, "description": "Delete task (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:34:51.085146"}, {"endpoint": "/api/actor/accounts/", "method": "GET", "status_code": 200, "expected_status": 200, "success": true, "description": "Legacy accounts", "response_preview": "{'success': True, 'results': [{'id': 1, 'username': 'grafisone', 'is_active': True, 'last_login': '2...", "timestamp": "2025-07-19T06:34:51.088935"}, {"endpoint": "/api/actor/tasks/", "method": "GET", "status_code": 401, "expected_status": 200, "success": false, "description": "Legacy tasks", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:34:51.105927"}, {"endpoint": "/api/actor/sessions/", "method": "GET", "status_code": 200, "expected_status": 200, "success": true, "description": "Legacy sessions", "response_preview": "{'success': True, 'results': [{'id': 1, 'account': {'id': 1, 'user_id': 1, 'username': 'grafisone', ...", "timestamp": "2025-07-19T06:34:51.109804"}, {"endpoint": "/api/actor/scraped-data/", "method": "GET", "status_code": 500, "expected_status": 200, "success": false, "description": "Legacy scraped data", "response_preview": "{'success': <PERSON>als<PERSON>, 'error': \"Field 'id' expected a number but got <django.contrib.auth.models.Anonym...", "timestamp": "2025-07-19T06:34:51.120548"}, {"endpoint": "/api/auth/users/", "method": "POST", "status_code": 400, "expected_status": 400, "success": true, "description": "Create user (no data)", "response_preview": "{'username': ['This field is required.'], 'password': ['This field is required.'], 're_password': ['...", "timestamp": "2025-07-19T06:34:51.125959"}, {"endpoint": "/api/auth/jwt/create/", "method": "POST", "status_code": 400, "expected_status": 400, "success": true, "description": "JWT login (no data)", "response_preview": "{'username': ['This field is required.'], 'password': ['This field is required.']}", "timestamp": "2025-07-19T06:34:51.140841"}]}