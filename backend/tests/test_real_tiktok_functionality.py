#!/usr/bin/env python3
"""
Real TikTok Functionality Test

This script demonstrates actual TikTok login and scraping functionality
using the enhanced TikTok actor system.

IMPORTANT: This script requires real TikTok credentials and will actually
attempt to login to TikTok. Use with caution and ensure you have proper
permissions to scrape the target content.
"""

import os
import sys
import django
import logging
from datetime import datetime
import time

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('real_tiktok_test.log')
    ]
)
logger = logging.getLogger(__name__)

# Import Django models and utilities
from django.contrib.auth.models import User
from actor_tiktok.models import TikTokUserAccount, ActorScrapedData
from django.utils import timezone

def test_enhanced_login_real():
    """
    Test enhanced login with real TikTok credentials
    
    NOTE: You need to provide real credentials for this test
    """
    logger.info("=== Testing Enhanced Login with Real Credentials ===")
    
    try:
        from actor_tiktok.utils.enhanced_tiktok_auth import EnhancedTikTokAuthenticator
        
        # Configuration for testing
        proxy_config = {
            'enabled': False,  # Set to True if you have proxies
            'proxies': [],
            'max_failures': 3
        }
        
        # Initialize authenticator
        authenticator = EnhancedTikTokAuthenticator(proxy_config=proxy_config)
        
        # Test credentials (REPLACE WITH REAL CREDENTIALS)
        test_username = "grafisone"  # Replace with real username
        test_password = "Puyol@102410"  # Replace with real password
        
        # Check if credentials are still placeholder
        if test_username == "your_tiktok_username" or test_password == "your_tiktok_password":
            logger.warning("⚠️ Please update the test credentials in the script before running real login test")
            return {
                'success': False,
                'error': 'Test credentials not configured',
                'message': 'Please update test_username and test_password with real TikTok credentials'
            }
        
        logger.info(f"Attempting login for username: {test_username}")
        
        # Perform login
        login_result = authenticator.login(
            username=test_username,
            password=test_password,
            use_2fa=False,  # Set to True if 2FA is enabled
            two_factor_code=None,  # Provide 2FA code if needed
            retry_count=0,
            account_id=None
        )
        
        if login_result['success']:
            logger.info("✅ Real TikTok login successful!")
            logger.info(f"Strategy used: {login_result.get('strategy', 'unknown')}")
            logger.info(f"Session data available: {'session_data' in login_result}")
            
            # Save session data if available
            if 'session_data' in login_result:
                session_data = login_result['session_data']
                logger.info(f"Session cookies: {len(session_data.get('cookies', []))}")
                logger.info(f"Local storage items: {len(session_data.get('local_storage', {}))}")
        else:
            logger.error(f"❌ Real TikTok login failed: {login_result.get('error')}")
            logger.error(f"Error type: {login_result.get('error_type', 'unknown')}")
            logger.error(f"Strategies attempted: {login_result.get('strategies_attempted', [])}")
        
        return login_result
        
    except Exception as e:
        logger.error(f"Real login test failed: {str(e)}")
        return {'success': False, 'error': str(e)}

def test_enhanced_scraping_real():
    """
    Test enhanced scraping with real TikTok data
    """
    logger.info("=== Testing Enhanced Scraping with Real Data ===")
    
    try:
        from actor_tiktok.utils.enhanced_scraper import EnhancedTikTokScraper
        
        # Configuration
        proxy_config = {'enabled': False, 'proxies': []}
        rate_limit_config = {
            'requests_per_minute': 10,  # Conservative for testing
            'requests_per_hour': 100,
            'delay_between_requests': (5, 10)  # Longer delays for safety
        }
        
        # Initialize scraper
        scraper = EnhancedTikTokScraper(
            proxy_config=proxy_config,
            rate_limit_config=rate_limit_config
        )
        
        # Test credentials (REPLACE WITH REAL CREDENTIALS)
        test_username = "grafisone"  # Replace with real username
        test_password = "Puyol@102410"  # Replace with real password
        
        if test_username == "your_tiktok_username" or test_password == "your_tiktok_password":
            logger.warning("⚠️ Please update the test credentials in the script before running real scraping test")
            return {
                'success': False,
                'error': 'Test credentials not configured'
            }
        
        # Login and prepare scraper
        logger.info("Preparing scraper with login...")
        login_result = scraper.login_and_prepare(
            username=test_username,
            password=test_password,
            account_id=1  # You may need to create a proper account ID
        )
        
        if not login_result['success']:
            logger.error(f"❌ Scraper login failed: {login_result.get('error')}")
            return login_result
        
        logger.info("✅ Scraper prepared successfully")
        
        # Test searching for "prabowo" content
        search_keyword = "prabowo"
        logger.info(f"Searching for keyword: {search_keyword}")

        search_result = scraper.search_content(
            query=search_keyword,
            content_type='videos',
            limit=10  # Small limit for testing
        )

        if search_result['success']:
            search_data = search_result['data']
            logger.info(f"✅ Search completed successfully for: {search_keyword}")
            logger.info(f"Videos found: {len(search_data)}")

            # Log some basic info about found videos
            for i, video in enumerate(search_data[:3]):  # Show first 3 videos
                logger.info(f"Video {i+1}: {video.get('title', 'No title')[:50]}...")
        else:
            logger.error(f"❌ Search failed: {search_result.get('error')}")

        # Also test scraping a popular TikTok profile (public data)
        target_username = "tiktok"  # Official TikTok account - public data
        logger.info(f"Also scraping profile: {target_username}")

        profile_result = scraper.scrape_user_profile(
            username=target_username,
            include_videos=True,
            video_limit=3  # Small limit for testing
        )
        
        if profile_result['success']:
            profile_data = profile_result['data']
            logger.info(f"✅ Profile scraped successfully: {target_username}")
            logger.info(f"Profile info available: {'profile_info' in profile_data}")
            logger.info(f"Videos found: {len(profile_data.get('videos', []))}")
            
            # Log some basic info (without sensitive data)
            if 'profile_info' in profile_data:
                profile_info = profile_data['profile_info']
                logger.info(f"Profile has follower info: {'followers' in str(profile_info)}")
                logger.info(f"Profile has bio info: {'bio' in str(profile_info)}")
        else:
            logger.error(f"❌ Profile scraping failed: {profile_result.get('error')}")
        
        # Cleanup
        scraper.cleanup()
        return profile_result
        
    except Exception as e:
        logger.error(f"Real scraping test failed: {str(e)}")
        return {'success': False, 'error': str(e)}

def test_celery_tasks_real():
    """
    Test Celery tasks with real data (if Celery is running)
    """
    logger.info("=== Testing Celery Tasks with Real Data ===")
    
    try:
        from actor_tiktok.tasks import enhanced_actor_login_task, enhanced_scraping_task
        
        # Check if we have a test user
        try:
            test_user = User.objects.get(username='test_user')
        except User.DoesNotExist:
            test_user = User.objects.create_user(
                username='test_user',
                email='<EMAIL>',
                password='test_password'
            )
            logger.info(f"Created test user: {test_user.username}")
        
        # Test credentials
        test_username = "grafisone"
        test_password = "Puyol@102410"
        
        if test_username == "your_tiktok_username" or test_password == "your_tiktok_password":
            logger.warning("⚠️ Please update the test credentials for Celery task testing")
            return {
                'success': False,
                'error': 'Test credentials not configured'
            }
        
        # Submit enhanced login task
        logger.info("Submitting enhanced login task...")
        
        # Note: This will only work if Celery is running
        # For testing without Celery, you can set CELERY_TASK_ALWAYS_EAGER = True in settings
        try:
            login_task = enhanced_actor_login_task.delay(
                user_id=test_user.id,
                tiktok_username=test_username,
                tiktok_password=test_password,
                use_2fa=False,
                proxy_config={'enabled': False, 'proxies': []}
            )
            
            logger.info(f"Login task submitted: {login_task.id}")
            
            # Wait a bit for task to process (if eager mode)
            time.sleep(2)
            
            # Check task status
            if hasattr(login_task, 'ready') and login_task.ready():
                result = login_task.result
                logger.info(f"Login task result: {result}")
            else:
                logger.info("Login task is processing asynchronously")
            
            return {'success': True, 'task_id': login_task.id}
            
        except Exception as e:
            logger.warning(f"Celery task submission failed (Celery may not be running): {str(e)}")
            return {'success': False, 'error': f'Celery not available: {str(e)}'}
        
    except Exception as e:
        logger.error(f"Celery tasks test failed: {str(e)}")
        return {'success': False, 'error': str(e)}

def test_prabowo_keyword_search():
    """
    Specific test for searching "prabowo" keyword on TikTok
    """
    logger.info("=== Testing Prabowo Keyword Search ===")

    try:
        from actor_tiktok.utils.enhanced_scraper import EnhancedTikTokScraper

        # Configuration for conservative testing
        proxy_config = {'enabled': False, 'proxies': []}
        rate_limit_config = {
            'requests_per_minute': 10,  # Very conservative
            'requests_per_hour': 50,
            'delay_between_requests': (8, 15)  # Longer delays for safety
        }

        # Initialize scraper
        scraper = EnhancedTikTokScraper(
            proxy_config=proxy_config,
            rate_limit_config=rate_limit_config
        )

        # Login with provided credentials
        test_username = "grafisone"
        test_password = "Puyol@102410"

        logger.info("Preparing scraper for Prabowo search...")
        login_result = scraper.login_and_prepare(
            username=test_username,
            password=test_password,
            account_id=1
        )

        if not login_result['success']:
            logger.error(f"❌ Scraper login failed: {login_result.get('error')}")
            return login_result

        logger.info("✅ Scraper prepared successfully")

        # Search for "prabowo" content
        search_keyword = "prabowo"
        logger.info(f"Searching TikTok for keyword: '{search_keyword}'")

        search_result = scraper.search_content(
            query=search_keyword,
            content_type='videos',
            limit=15  # Get more results for better analysis
        )

        if search_result['success']:
            videos = search_result['data']
            logger.info(f"✅ Found {len(videos)} videos for '{search_keyword}'")

            # Analyze the results
            if videos:
                logger.info("📊 Search Results Analysis:")
                for i, video in enumerate(videos[:5]):  # Show first 5 videos
                    title = video.get('title', 'No title')
                    author = video.get('author', 'Unknown author')
                    views = video.get('views', 'Unknown views')
                    logger.info(f"  {i+1}. '{title[:60]}...' by @{author} ({views} views)")

                # Try to get additional details for the first video
                if len(videos) > 0:
                    first_video = videos[0]
                    video_id = first_video.get('video_id')
                    author = first_video.get('author')

                    if video_id and author:
                        logger.info(f"Getting detailed info for video: {video_id}")
                        video_details = scraper.scrape_video_details(author, video_id)

                        if video_details['success']:
                            details = video_details['data']
                            logger.info(f"✅ Video details retrieved:")
                            logger.info(f"  Comments: {len(details.get('comments', []))}")
                            logger.info(f"  Stats: {details.get('stats', {})}")
            else:
                logger.warning("No videos found for the search term")
        else:
            logger.error(f"❌ Search failed: {search_result.get('error')}")

        # Cleanup
        scraper.cleanup()
        return search_result

    except Exception as e:
        logger.error(f"Prabowo search test failed: {str(e)}")
        return {'success': False, 'error': str(e)}

def run_real_functionality_tests():
    """
    Run all real functionality tests
    """
    logger.info("🚀 Starting Real TikTok Functionality Tests")
    logger.info(f"Timestamp: {datetime.now().isoformat()}")
    logger.info("="*60)
    
    logger.warning("⚠️ IMPORTANT: These tests will attempt real TikTok login and scraping!")
    logger.warning("⚠️ Make sure you have permission to scrape the target content.")
    logger.warning("⚠️ Update the test credentials in the script before running.")
    
    tests = [
        ("Enhanced Login (Real)", test_enhanced_login_real),
        ("Enhanced Scraping (Real)", test_enhanced_scraping_real),
        ("Prabowo Keyword Search", test_prabowo_keyword_search),
        ("Celery Tasks (Real)", test_celery_tasks_real)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result and result.get('success'):
                logger.info(f"✅ {test_name} - PASSED")
            else:
                logger.warning(f"⚠️ {test_name} - FAILED or SKIPPED")
                if result and 'error' in result:
                    logger.warning(f"Reason: {result['error']}")
                    
        except Exception as e:
            logger.error(f"❌ {test_name} - EXCEPTION: {str(e)}")
            results[test_name] = {'success': False, 'error': str(e)}
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("REAL FUNCTIONALITY TEST SUMMARY")
    logger.info(f"{'='*60}")
    
    passed = sum(1 for result in results.values() if result and result.get('success'))
    total = len(results)
    
    logger.info(f"Tests passed: {passed}/{total}")
    
    for test_name, result in results.items():
        if result and result.get('success'):
            status = "✅ PASS"
        elif result and 'Test credentials not configured' in result.get('error', ''):
            status = "⚠️ SKIP (credentials not configured)"
        else:
            status = "❌ FAIL"
        
        logger.info(f"{status} - {test_name}")
    
    if passed > 0:
        logger.info("🎉 Some real functionality tests passed!")
    else:
        logger.info("ℹ️ No real tests passed - likely due to missing credentials or Celery setup")
    
    logger.info("🏁 Real functionality tests completed!")
    
    # Instructions for users
    logger.info("\n" + "="*60)
    logger.info("INSTRUCTIONS FOR REAL TESTING:")
    logger.info("="*60)
    logger.info("1. Update test_username and test_password with real TikTok credentials")
    logger.info("2. Ensure you have permission to scrape the target content")
    logger.info("3. For Celery tasks, make sure Redis and Celery worker are running:")
    logger.info("   - Start Redis: redis-server")
    logger.info("   - Start Celery: celery -A backend worker --loglevel=info")
    logger.info("4. Consider using proxies for production scraping")
    logger.info("5. Respect TikTok's terms of service and rate limits")
    
    return results

if __name__ == "__main__":
    results = run_real_functionality_tests()
