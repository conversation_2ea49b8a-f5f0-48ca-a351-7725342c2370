{"timestamp": "2025-07-18T23:26:37.729139", "tests": {"create_actor_account": {"success": true, "message": "Account created successfully with ID: 4", "data": {"id": 4, "platform": "tiktok", "username": "grafisone", "email": "<EMAIL>", "is_active": true, "created_at": "2025-07-18T23:26:37.763800+00:00"}, "timestamp": "2025-07-18T23:26:37.768616"}, "authenticate_account": {"success": false, "message": "Authentication failed: Authentication error: 'SimpleTikTokAuthenticator' object has no attribute 'simple_login'", "data": null, "timestamp": "2025-07-18T23:26:39.776554"}, "create_scraping_task": {"success": true, "message": "Task created successfully with ID: 2", "data": {"id": 2, "name": "Test Scraping - prabow<PERSON>", "type": "CONTENT_SEARCH", "platform": "tiktok", "status": "PENDING", "created_at": "2025-07-18T23:26:41.784371+00:00"}, "timestamp": "2025-07-18T23:26:41.791670"}, "execute_scraping_task": {"success": false, "message": "Task execution failed: Session invalid, please re-authenticate", "data": null, "timestamp": "2025-07-18T23:26:43.801495"}, "verify_scraped_data": {"success": false, "message": "No scraped data found in database", "data": null, "timestamp": "2025-07-18T23:26:45.809433"}, "data_quality_check": {"success": true, "message": "Data quality stats retrieved successfully", "data": {"total_items": 0, "complete_items": 0, "completion_rate": 0, "average_quality_score": 0, "platform_breakdown": [], "data_type_breakdown": []}, "timestamp": "2025-07-18T23:26:47.826050"}}, "summary": {"total_tests": 6, "passed_tests": 3, "failed_tests": 3, "success_rate": "50.0%"}}