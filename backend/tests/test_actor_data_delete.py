#!/usr/bin/env python3

"""
Test Actor Data Delete Functionality

Tests the delete functionality for actor scraped data to identify
why deletion is not working on the frontend.
"""

import os
import sys
import django
import requests

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from actor.models import ActorAccount, ActorTask, ActorScrapedData

def test_actor_data_delete():
    print("🗑️ Testing Actor Data Delete Functionality")
    print("=" * 50)
    
    # Get test user and token
    user = User.objects.get(username='test_actor_user')
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    headers = {
        'Authorization': f'JWT {access_token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000'
    
    # Test 1: Create test data first
    print(f"\n1️⃣ Creating Test Data")
    print("-" * 40)

    try:
        # Get a task to associate with test data
        tasks_response = requests.get(f'{base_url}/api/actor/tasks/', headers=headers)
        if tasks_response.status_code == 200:
            tasks_data = tasks_response.json()
            tasks = tasks_data.get('tasks', [])

            if tasks:
                test_task_id = tasks[0]['id']
                print(f"   📝 Using task ID: {test_task_id}")

                # Create test data items
                for i in range(3):
                    test_data = {
                        'task_id': test_task_id,
                        'data_type': 'TEST',
                        'content': {'test': f'Test data item {i+1}', 'index': i+1},
                        'platform': 'test',
                        'account_username': 'test_user',
                        'platform_content_id': f'test_{i+1}',
                        'is_complete': True
                    }

                    create_response = requests.post(f'{base_url}/api/actor/data/create/',
                                                  headers=headers, json=test_data)

                    if create_response.status_code == 200:
                        print(f"   ✅ Created test data item {i+1}")
                    else:
                        print(f"   ❌ Failed to create test data item {i+1}: {create_response.text}")
            else:
                print(f"   ❌ No tasks found to associate test data with")

        # Now check existing data
        print(f"\n2️⃣ Checking Existing Actor Data")
        print("-" * 40)

        # Get existing scraped data
        response = requests.get(f'{base_url}/api/actor/data/', headers=headers)

        if response.status_code == 200:
            data = response.json()
            scraped_data = data.get('data', [])
            print(f"   📊 Found {len(scraped_data)} existing data items")
            
            if scraped_data:
                sample_item = scraped_data[0]
                print(f"   📝 Sample item:")
                print(f"      ID: {sample_item.get('id')}")
                print(f"      Platform: {sample_item.get('platform')}")
                print(f"      Data Type: {sample_item.get('data_type')}")
                print(f"      Task ID: {sample_item.get('task_id')}")
                
                # Test single delete
                print(f"\n3️⃣ Testing Single Item Delete")
                print("-" * 40)
                
                item_id = sample_item.get('id')
                delete_url = f'{base_url}/api/actor/data/{item_id}/delete/'
                
                print(f"   🔗 Delete URL: {delete_url}")
                print(f"   🗑️ Attempting to delete item ID: {item_id}")
                
                delete_response = requests.delete(delete_url, headers=headers)
                
                print(f"   📊 Response Status: {delete_response.status_code}")
                print(f"   📊 Response Headers: {dict(delete_response.headers)}")
                
                if delete_response.status_code == 200:
                    result = delete_response.json()
                    print(f"   ✅ Delete successful!")
                    print(f"   📝 Response: {result}")
                else:
                    print(f"   ❌ Delete failed!")
                    print(f"   📝 Response: {delete_response.text}")
                
                # Verify deletion
                verify_response = requests.get(f'{base_url}/api/actor/data/', headers=headers)
                if verify_response.status_code == 200:
                    verify_data = verify_response.json()
                    new_count = len(verify_data.get('data', []))
                    print(f"   📊 Data count after delete: {new_count}")
                    
                    if new_count < len(scraped_data):
                        print(f"   ✅ Deletion verified - count reduced from {len(scraped_data)} to {new_count}")
                    else:
                        print(f"   ❌ Deletion not verified - count unchanged")
            
            # Test bulk delete if we have multiple items
            if len(scraped_data) > 1:
                print(f"\n4️⃣ Testing Bulk Delete")
                print("-" * 40)
                
                # Get fresh data after single delete
                fresh_response = requests.get(f'{base_url}/api/actor/data/', headers=headers)
                if fresh_response.status_code == 200:
                    fresh_data = fresh_response.json()
                    remaining_items = fresh_data.get('data', [])
                    
                    if len(remaining_items) >= 2:
                        # Select first 2 items for bulk delete
                        bulk_ids = [item['id'] for item in remaining_items[:2]]
                        
                        print(f"   🗑️ Attempting bulk delete of IDs: {bulk_ids}")
                        
                        bulk_delete_url = f'{base_url}/api/actor/data/bulk-delete/'
                        bulk_data = {'data_ids': bulk_ids}
                        
                        print(f"   🔗 Bulk Delete URL: {bulk_delete_url}")
                        print(f"   📝 Bulk Delete Data: {bulk_data}")
                        
                        bulk_response = requests.post(bulk_delete_url, headers=headers, json=bulk_data)
                        
                        print(f"   📊 Bulk Response Status: {bulk_response.status_code}")
                        
                        if bulk_response.status_code == 200:
                            bulk_result = bulk_response.json()
                            print(f"   ✅ Bulk delete successful!")
                            print(f"   📝 Response: {bulk_result}")
                        else:
                            print(f"   ❌ Bulk delete failed!")
                            print(f"   📝 Response: {bulk_response.text}")
                    else:
                        print(f"   ⚠️ Not enough items for bulk delete test")
        else:
            print(f"   ❌ Failed to get data: {response.status_code}")
            print(f"   📝 Response: {response.text}")
    
    except Exception as e:
        print(f"   ❌ Test failed: {str(e)}")
    
    # Test 5: Check API endpoint availability
    print(f"\n5️⃣ Testing API Endpoint Availability")
    print("-" * 40)
    
    try:
        # Test if endpoints are accessible
        # Get a real data ID from database
        real_data = ActorScrapedData.objects.filter(task__user=user).first()
        real_data_id = real_data.id if real_data else 1

        endpoints_to_test = [
            '/api/actor/data/',
            f'/api/actor/data/{real_data_id}/delete/',
            '/api/actor/data/bulk-delete/'
        ]
        
        for endpoint in endpoints_to_test:
            test_url = f'{base_url}{endpoint}'
            print(f"   🔗 Testing: {test_url}")
            
            if 'delete' in endpoint and endpoint.endswith('/'):
                # Test DELETE method
                test_response = requests.delete(test_url, headers=headers)
            elif 'bulk-delete' in endpoint:
                # Test POST method
                test_response = requests.post(test_url, headers=headers, json={'data_ids': []})
            else:
                # Test GET method
                test_response = requests.get(test_url, headers=headers)
            
            print(f"      Status: {test_response.status_code}")
            
            if test_response.status_code == 404:
                print(f"      ❌ Endpoint not found!")
            elif test_response.status_code in [200, 400, 401]:
                print(f"      ✅ Endpoint accessible")
            else:
                print(f"      ⚠️ Unexpected status: {test_response.status_code}")
    
    except Exception as e:
        print(f"   ❌ Endpoint test failed: {str(e)}")
    
    # Test 6: Check database directly
    print(f"\n6️⃣ Testing Database Direct Access")
    print("-" * 40)
    
    try:
        # Check database directly
        all_data = ActorScrapedData.objects.filter(task__user=user)
        print(f"   📊 Database shows {all_data.count()} items for user")
        
        if all_data.exists():
            sample_db_item = all_data.first()
            print(f"   📝 Sample DB item:")
            print(f"      ID: {sample_db_item.id}")
            print(f"      Platform: {sample_db_item.platform}")
            print(f"      Data Type: {sample_db_item.data_type}")
            print(f"      Task: {sample_db_item.task.id}")
            print(f"      User: {sample_db_item.task.user.username}")
            
            # Test direct deletion
            print(f"   🗑️ Testing direct database deletion...")
            item_to_delete = all_data.last()
            item_id = item_to_delete.id
            
            item_to_delete.delete()
            
            remaining_count = ActorScrapedData.objects.filter(task__user=user).count()
            print(f"   ✅ Direct deletion successful - {remaining_count} items remaining")
        else:
            print(f"   ⚠️ No data items found in database for user")
    
    except Exception as e:
        print(f"   ❌ Database test failed: {str(e)}")
    
    # Summary
    print("\n" + "=" * 50)
    print("🎯 Actor Data Delete Test Summary")
    print("=" * 50)
    
    print(f"\n🔍 DIAGNOSIS:")
    print(f"   • Check if API endpoints are correctly mapped")
    print(f"   • Verify authentication is working")
    print(f"   • Confirm data ownership permissions")
    print(f"   • Test frontend JavaScript console for errors")
    print(f"   • Verify CSRF tokens if required")
    
    print(f"\n🛠️ POTENTIAL FIXES:")
    print(f"   • Check URL routing in backend/actor/urls.py")
    print(f"   • Verify API function implementations")
    print(f"   • Check frontend API call syntax")
    print(f"   • Confirm authentication headers")
    print(f"   • Test with browser developer tools")
    
    print(f"\n📝 NEXT STEPS:")
    print(f"   1. Run this test to identify specific failure points")
    print(f"   2. Check browser console for JavaScript errors")
    print(f"   3. Verify network requests in browser dev tools")
    print(f"   4. Test API endpoints directly with curl/Postman")
    print(f"   5. Check Django logs for backend errors")

if __name__ == '__main__':
    test_actor_data_delete()
