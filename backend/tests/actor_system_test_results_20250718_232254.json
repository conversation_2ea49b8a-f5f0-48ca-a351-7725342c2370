{"timestamp": "2025-07-18T23:22:42.806259", "tests": {"create_actor_account": {"success": false, "message": "Exception during account creation: Cannot resolve keyword 'username' into field. Choices are: blocked_until, created_at, email, encrypted_session_data, id, is_active, is_blocked, last_attempt_at, last_login, login_attempts, password, platform, platform_user_id, platform_username, scraped_data, session_expires_at, sessions, tasks, updated_at, user, user_id", "data": null, "timestamp": "2025-07-18T23:22:42.833881"}, "authenticate_account": {"success": false, "message": "Exception during authentication: 'CompleteActorSystemTest' object has no attribute 'account_id'", "data": null, "timestamp": "2025-07-18T23:22:44.840705"}, "create_scraping_task": {"success": false, "message": "Exception during task creation: 'CompleteActorSystemTest' object has no attribute 'account_id'", "data": null, "timestamp": "2025-07-18T23:22:46.842822"}, "execute_scraping_task": {"success": false, "message": "Exception during task execution: 'CompleteActorSystemTest' object has no attribute 'task_id'", "data": null, "timestamp": "2025-07-18T23:22:48.846544"}, "verify_scraped_data": {"success": false, "message": "Exception during data verification: 'CompleteActorSystemTest' object has no attribute 'task_id'", "data": null, "timestamp": "2025-07-18T23:22:50.848524"}, "data_quality_check": {"success": true, "message": "Data quality stats retrieved successfully", "data": {"total_items": 0, "complete_items": 0, "completion_rate": 0, "average_quality_score": 0, "platform_breakdown": [], "data_type_breakdown": []}, "timestamp": "2025-07-18T23:22:52.863236"}}, "summary": {"total_tests": 6, "passed_tests": 1, "failed_tests": 5, "success_rate": "16.7%"}}