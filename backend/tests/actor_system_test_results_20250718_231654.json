{"timestamp": "2025-07-18T23:16:42.524646", "tests": {"create_actor_account": {"success": false, "message": "Exception during account creation: Cannot resolve keyword 'username' into field. Choices are: blocked_until, created_at, email, encrypted_session_data, id, is_active, is_blocked, last_attempt_at, last_login, login_attempts, password, platform, platform_user_id, platform_username, scraped_data, session_expires_at, sessions, tasks, updated_at, user, user_id", "data": null, "timestamp": "2025-07-18T23:16:42.536392"}, "authenticate_account": {"success": false, "message": "Exception during authentication: 'CompleteActorSystemTest' object has no attribute 'account_id'", "data": null, "timestamp": "2025-07-18T23:16:44.541096"}, "create_scraping_task": {"success": false, "message": "Exception during task creation: 'CompleteActorSystemTest' object has no attribute 'account_id'", "data": null, "timestamp": "2025-07-18T23:16:46.543223"}, "execute_scraping_task": {"success": false, "message": "Exception during task execution: 'CompleteActorSystemTest' object has no attribute 'task_id'", "data": null, "timestamp": "2025-07-18T23:16:48.546182"}, "verify_scraped_data": {"success": false, "message": "Exception during data verification: 'CompleteActorSystemTest' object has no attribute 'task_id'", "data": null, "timestamp": "2025-07-18T23:16:50.548775"}, "data_quality_check": {"success": false, "message": "Exception during data quality check: 'ActorService' object has no attribute 'get_data_labeling_stats'", "data": null, "timestamp": "2025-07-18T23:16:52.549296"}}, "summary": {"total_tests": 6, "passed_tests": 0, "failed_tests": 6, "success_rate": "0.0%"}}