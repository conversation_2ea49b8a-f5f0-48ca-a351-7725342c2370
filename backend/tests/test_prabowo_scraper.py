#!/usr/bin/env python3
"""
Prabowo Content Scraper

Now that we have working login, let's scrape Prabowo content from TikTok.
"""

import os
import sys
import django
import logging
import time
import random
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def scrape_prabowo_content():
    """Scrape Prabowo content from TikTok"""
    logger.info("=== Prabowo Content Scraper ===")
    
    try:
        from actor_tiktok.utils.simple_tiktok_auth import SimpleTikTokAuthenticator
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        # Initialize authenticator
        authenticator = SimpleTikTokAuthenticator()
        
        # Test credentials
        username = "grafisone"
        password = "Puyol@102410"
        
        logger.info(f"🚀 Starting Prabowo content scraping for: {username}")
        logger.info("")
        
        # Login first
        logger.info("Step 1: Logging into TikTok...")
        login_result = authenticator.login(username, password)
        
        if not login_result['success']:
            logger.error(f"❌ Login failed: {login_result.get('error')}")
            return False
        
        logger.info("✅ Login successful! Now searching for Prabowo content...")
        
        # Get the driver from the session (we'll need to modify the authenticator)
        # For now, let's create a new session and navigate to search
        
        import undetected_chromedriver as uc
        
        # Setup driver with session cookies
        logger.info("Step 2: Setting up scraping session...")
        options = uc.ChromeOptions()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        
        driver = uc.Chrome(options=options, version_main=None)
        
        try:
            # Navigate to TikTok
            logger.info("Step 3: Navigating to TikTok...")
            driver.get("https://www.tiktok.com")
            time.sleep(3)
            
            # Add session cookies
            session_info = login_result.get('session_info', {})
            cookies = session_info.get('cookies', [])
            
            if cookies:
                logger.info(f"Step 4: Adding {len(cookies)} session cookies...")
                for cookie in cookies:
                    try:
                        driver.add_cookie(cookie)
                    except Exception as e:
                        logger.debug(f"Could not add cookie {cookie.get('name', 'unknown')}: {str(e)}")
                
                # Refresh to apply cookies
                driver.refresh()
                time.sleep(3)
            
            # Navigate to search for "prabowo"
            search_url = "https://www.tiktok.com/search?q=prabowo&t=videos"
            logger.info(f"Step 5: Searching for 'prabowo' content...")
            logger.info(f"Search URL: {search_url}")
            
            driver.get(search_url)
            time.sleep(5)
            
            # Wait for search results
            logger.info("Step 6: Waiting for search results...")
            try:
                WebDriverWait(driver, 15).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, '[data-e2e="search-result"]'))
                )
                logger.info("✅ Search results loaded!")
            except:
                logger.info("ℹ️ Search results may have different structure, continuing...")
            
            # Try to find video elements
            logger.info("Step 7: Looking for Prabowo videos...")
            
            video_selectors = [
                '[data-e2e="search-result"]',
                '[data-e2e="user-post-item"]',
                '.video-feed-item',
                '[data-testid="video-item"]',
                'div[data-e2e="search-result-item"]',
                'div[class*="video"]',
                'a[href*="/video/"]'
            ]
            
            videos_found = []
            for selector in video_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        logger.info(f"✅ Found {len(elements)} elements with selector: {selector}")
                        videos_found.extend(elements[:5])  # Take first 5
                        break
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {str(e)}")
                    continue
            
            if videos_found:
                logger.info(f"🎯 Found {len(videos_found)} Prabowo-related videos!")
                
                # Extract basic information from videos
                logger.info("Step 8: Extracting video information...")
                
                for i, video in enumerate(videos_found[:3], 1):  # Process first 3
                    try:
                        logger.info(f"\n--- Video {i} ---")
                        
                        # Try to get video link
                        try:
                            link = video.get_attribute('href') or video.find_element(By.CSS_SELECTOR, 'a').get_attribute('href')
                            logger.info(f"🔗 Link: {link}")
                        except:
                            logger.info("🔗 Link: Not found")
                        
                        # Try to get video description/title
                        try:
                            desc_selectors = [
                                '[data-e2e="video-desc"]',
                                '.video-meta-title',
                                'h3',
                                'span[title]'
                            ]
                            
                            description = None
                            for desc_sel in desc_selectors:
                                try:
                                    desc_elem = video.find_element(By.CSS_SELECTOR, desc_sel)
                                    description = desc_elem.text or desc_elem.get_attribute('title')
                                    if description:
                                        break
                                except:
                                    continue
                            
                            if description:
                                logger.info(f"📝 Description: {description[:100]}...")
                            else:
                                logger.info("📝 Description: Not found")
                                
                        except Exception as e:
                            logger.info(f"📝 Description: Error - {str(e)}")
                        
                        # Try to get engagement metrics
                        try:
                            metrics_selectors = [
                                '[data-e2e="like-count"]',
                                '[data-e2e="comment-count"]',
                                '[data-e2e="share-count"]',
                                '.video-count'
                            ]
                            
                            for metric_sel in metrics_selectors:
                                try:
                                    metric_elem = video.find_element(By.CSS_SELECTOR, metric_sel)
                                    metric_text = metric_elem.text
                                    if metric_text:
                                        logger.info(f"📊 Metric: {metric_text}")
                                except:
                                    continue
                                    
                        except Exception as e:
                            logger.debug(f"Metrics extraction error: {str(e)}")
                        
                    except Exception as e:
                        logger.warning(f"Error processing video {i}: {str(e)}")
                
                logger.info(f"\n🎉 Successfully found and processed Prabowo content!")
                logger.info(f"✅ Total videos found: {len(videos_found)}")
                logger.info(f"✅ Videos processed: {min(3, len(videos_found))}")
                
                return True
                
            else:
                logger.warning("⚠️ No video elements found with current selectors")
                logger.info("This might mean:")
                logger.info("1. Different page structure than expected")
                logger.info("2. Content requires login verification")
                logger.info("3. Search results are loading dynamically")
                
                # Let's check what's actually on the page
                logger.info("\nPage analysis:")
                page_title = driver.title
                current_url = driver.current_url
                logger.info(f"Page title: {page_title}")
                logger.info(f"Current URL: {current_url}")
                
                # Check if we're still logged in
                if 'login' in current_url.lower():
                    logger.warning("❌ Redirected to login page - session may have expired")
                else:
                    logger.info("✅ Still on TikTok, session appears valid")
                
                return False
                
        finally:
            # Keep browser open to see results
            logger.info("\nKeeping browser open for 15 seconds to observe results...")
            time.sleep(15)
            driver.quit()
            
    except Exception as e:
        logger.error(f"❌ Scraping failed with exception: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def run_prabowo_scraper():
    """Run the Prabowo content scraper"""
    logger.info("🎯 Prabowo Content Scraper - TikTok")
    logger.info(f"Timestamp: {datetime.now().isoformat()}")
    logger.info("="*50)
    
    logger.info("🎬 This scraper will:")
    logger.info("1. Login to TikTok with your credentials")
    logger.info("2. Search for 'prabowo' content")
    logger.info("3. Extract video information and metrics")
    logger.info("4. Provide detailed results")
    logger.info("")
    
    # Run the scraper
    result = scrape_prabowo_content()
    
    # Summary
    logger.info("="*50)
    logger.info("PRABOWO SCRAPER SUMMARY")
    logger.info("="*50)
    
    if result:
        logger.info("🎉 SCRAPING SUCCESSFUL!")
        logger.info("Successfully found and processed Prabowo content!")
        logger.info("")
        logger.info("✅ Achievements:")
        logger.info("  - TikTok login successful")
        logger.info("  - Search functionality working")
        logger.info("  - Video content found")
        logger.info("  - Data extraction completed")
        logger.info("")
        logger.info("🚀 Production Ready:")
        logger.info("  - Scale up for more content")
        logger.info("  - Add data storage")
        logger.info("  - Implement scheduling")
        logger.info("  - Add more extraction fields")
        
    else:
        logger.info("⚠️ SCRAPING ENCOUNTERED ISSUES")
        logger.info("This is common with dynamic content loading")
        logger.info("")
        logger.info("🔧 Next Steps:")
        logger.info("  1. Verify search page structure")
        logger.info("  2. Update selectors if needed")
        logger.info("  3. Add more wait time for dynamic loading")
        logger.info("  4. Consider alternative search approaches")
    
    logger.info("")
    logger.info("🏁 Prabowo scraper test completed!")
    
    return result

if __name__ == "__main__":
    run_prabowo_scraper()
