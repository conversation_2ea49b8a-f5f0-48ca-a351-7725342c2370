#!/usr/bin/env python3

"""
Test the modern task management system
"""

import os
import sys
import django
import requests
import json

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken

def test_modern_task_system():
    print("🎭 Modern Task System Test")
    print("=" * 50)
    
    # Get test user and token
    user = User.objects.get(username='test_actor_user')
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    headers = {
        'Authorization': f'JWT {access_token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000/api'
    
    print("🔍 Testing Modern Task Management System")
    print(f"🔑 Token: {access_token[:20]}...")
    
    try:
        # 1. Test getting accounts
        print("\n📱 1. Getting Actor Accounts...")
        response = requests.get(f'{base_url}/actor/accounts/list/', headers=headers, timeout=10)
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            accounts_data = response.json()
            accounts = accounts_data.get('accounts', [])
            print(f"✅ Found {len(accounts)} accounts")
            
            if accounts:
                account = accounts[0]
                account_id = account['id']
                print(f"   Using account: @{account['username']} (ID: {account_id})")
                
                # 2. Test creating a task
                print("\n📋 2. Creating a New Task...")
                task_data = {
                    'account_id': account_id,
                    'task_type': 'CONTENT_SEARCH',
                    'task_name': 'Test Modern Task - Prabowo Content',
                    'description': 'Testing the new modern task management system',
                    'max_items': 25,
                    'task_parameters': {
                        'keywords': 'prabowo',
                        'quality_filter': 'all',
                        'include_metadata': True
                    }
                }
                
                response = requests.post(
                    f'{base_url}/actor/tasks/create/', 
                    headers=headers, 
                    json=task_data,
                    timeout=10
                )
                print(f"📊 Status: {response.status_code}")
                
                if response.status_code == 200:
                    task_result = response.json()
                    if task_result.get('success'):
                        task_id = task_result.get('task_id')
                        print(f"✅ Task created successfully! ID: {task_id}")
                        print(f"   Task Name: {task_result.get('task', {}).get('name')}")
                        print(f"   Task Type: {task_result.get('task', {}).get('type')}")
                        print(f"   Status: {task_result.get('task', {}).get('status')}")
                        
                        # 3. Test getting tasks
                        print("\n📋 3. Getting All Tasks...")
                        response = requests.get(f'{base_url}/actor/tasks/list/', headers=headers, timeout=10)
                        print(f"📊 Status: {response.status_code}")
                        
                        if response.status_code == 200:
                            tasks_data = response.json()
                            tasks = tasks_data.get('tasks', [])
                            print(f"✅ Found {len(tasks)} tasks")
                            
                            for task in tasks:
                                print(f"   - {task.get('name')} ({task.get('status')})")
                        
                        # 4. Test task execution (optional - might take time)
                        print(f"\n🚀 4. Testing Task Execution (ID: {task_id})...")
                        response = requests.post(
                            f'{base_url}/actor/tasks/execute/', 
                            headers=headers, 
                            json={'task_id': task_id},
                            timeout=15
                        )
                        print(f"📊 Status: {response.status_code}")
                        
                        if response.status_code == 200:
                            exec_result = response.json()
                            if exec_result.get('success'):
                                print(f"✅ Task execution started!")
                                print(f"   Message: {exec_result.get('message')}")
                            else:
                                print(f"⚠️ Task execution failed: {exec_result.get('error')}")
                        else:
                            print(f"⚠️ Task execution request failed: {response.text}")
                        
                    else:
                        print(f"❌ Task creation failed: {task_result.get('error')}")
                else:
                    print(f"❌ Task creation request failed: {response.text}")
            else:
                print("❌ No accounts found. Please create an account first.")
        else:
            print(f"❌ Failed to get accounts: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Modern Task System Test Complete!")

if __name__ == '__main__':
    test_modern_task_system()
