#!/usr/bin/env python3

"""
Test Fixed Twitter Engine (No Mock Data)
"""

import os
import sys
import django
import requests

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken

def test_twitter_engine_fixed():
    print("🔧 Testing Fixed Twitter Engine (No Mock Data)")
    print("=" * 60)
    
    # Get test user and token
    user = User.objects.get(username='test_actor_user')
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    headers = {
        'Authorization': f'JWT {access_token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000/api'
    
    print(f"🔑 User: {user.username}")
    
    # Get Twitter account
    from actor.models import ActorAccount
    twitter_account = ActorAccount.objects.filter(user=user, platform='twitter').first()
    
    if not twitter_account:
        print("❌ No Twitter account found")
        return
    
    print(f"📱 Twitter Account: @{twitter_account.platform_username}")
    
    # Test cases for different regions
    test_cases = [
        {
            'region': 'indonesia',
            'keywords': 'prabowo',
            'description': 'Test Indonesian regional filtering with political keyword'
        },
        {
            'region': 'global',
            'keywords': 'artificial intelligence',
            'description': 'Test global content with tech keyword'
        },
        {
            'region': 'asia',
            'keywords': 'breaking news',
            'description': 'Test Asian regional content with news keyword'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ Testing: {test_case['description']}")
        print(f"   Region: {test_case['region']}")
        print(f"   Keywords: {test_case['keywords']}")
        
        # Create task with regional filtering
        task_data = {
            'account_id': twitter_account.id,
            'task_type': 'CONTENT_SEARCH',
            'task_name': f'Fixed Engine Test - {test_case["region"].title()}',
            'description': test_case['description'],
            'max_items': 3,
            'keywords': test_case['keywords'],
            'task_parameters': {
                'keywords': test_case['keywords'],
                'quality_filter': 'all',
                'region': test_case['region']
            }
        }
        
        # Create task via API
        print(f"   📝 Creating task...")
        response = requests.post(f'{base_url}/actor/tasks/create/', 
                               headers=headers, 
                               json=task_data, 
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"   ✅ Task created: ID {task_id}")
                
                # Execute task
                print(f"   🚀 Executing task...")
                exec_response = requests.post(f'{base_url}/actor/tasks/execute/', 
                                            headers=headers, 
                                            json={'task_id': task_id}, 
                                            timeout=30)
                
                print(f"   📊 Response Status: {exec_response.status_code}")
                
                if exec_response.status_code == 200:
                    exec_result = exec_response.json()
                    if exec_result.get('success'):
                        items_scraped = exec_result.get('items_scraped', 0)
                        print(f"   ✅ Task executed successfully: {items_scraped} items scraped")
                        
                        # Check scraped data
                        import time
                        time.sleep(1)  # Wait for data to be saved
                        
                        from actor.models import ActorScrapedData
                        scraped_data = ActorScrapedData.objects.filter(task_id=task_id)
                        print(f"   📊 Data saved: {scraped_data.count()} items in database")
                        
                        # Show sample content
                        for j, item in enumerate(scraped_data[:2], 1):
                            if item.content:
                                title = item.content.get('title', '') or item.content.get('text', '')
                                author = item.content.get('author', 'N/A')
                                print(f"     Sample {j}: @{author} - {title[:60]}...")
                        
                        print(f"   🎉 SUCCESS: No 404 errors, real scraper working!")
                    else:
                        print(f"   ❌ Task execution failed: {exec_result.get('error')}")
                        print(f"   📋 Full response: {exec_result}")
                elif exec_response.status_code == 404:
                    print(f"   ❌ 404 ERROR STILL PRESENT")
                    print(f"   📋 Response: {exec_response.text}")
                elif exec_response.status_code == 400:
                    print(f"   ⚠️  400 Bad Request")
                    try:
                        error_data = exec_response.json()
                        print(f"   📋 Error details: {error_data}")
                    except:
                        print(f"   📋 Raw response: {exec_response.text}")
                else:
                    print(f"   ❌ Unexpected status code: {exec_response.status_code}")
                    print(f"   📋 Response: {exec_response.text}")
            else:
                print(f"   ❌ Task creation failed: {result.get('error')}")
        else:
            print(f"   ❌ Task creation request failed: {response.status_code}")
            print(f"   📋 Response: {response.text}")
    
    # Test direct engine usage
    print(f"\n🔧 Testing Direct Engine Usage:")
    
    try:
        from actor.engines.twitter_engine import TwitterEngine
        from actor.models import ActorAccount
        
        engine = TwitterEngine()
        account = ActorAccount.objects.filter(user=user, platform='twitter').first()
        
        if account:
            print(f"   📱 Testing with account: @{account.platform_username}")
            
            # Test search_content method directly
            keywords = ['prabowo']
            task_parameters = {'region': 'indonesia'}
            
            print(f"   🔍 Testing search_content method...")
            results = engine.search_content(
                account=account, 
                keywords=keywords, 
                limit=2,
                task_parameters=task_parameters
            )
            
            print(f"   ✅ Direct engine test successful: {len(results)} results")
            for i, result in enumerate(results, 1):
                print(f"     Result {i}: {result.get('title', 'N/A')[:50]}...")
        else:
            print(f"   ❌ No account found for direct testing")
            
    except Exception as e:
        print(f"   ❌ Direct engine test failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎯 Twitter Engine Fix Summary:")
    print("✅ Removed all mock data generation methods")
    print("✅ Fixed task parameter passing issue")
    print("✅ Using real TwitterScraper for all operations")
    print("✅ Regional filtering integrated")
    print("✅ No more fallback to mock data")
    
    print(f"\n🔧 CHANGES MADE:")
    print(f"   • Removed _generate_realistic_mock_data()")
    print(f"   • Removed _generate_user_mock_data()")
    print(f"   • Removed _generate_feed_mock_data()")
    print(f"   • Fixed task.task_parameters -> kwargs.get('task_parameters')")
    print(f"   • All methods now use real TwitterScraper")
    print(f"   • Proper error handling without mock fallbacks")
    
    print(f"\n🎯 EXPECTED RESULTS:")
    print(f"   • No more 404 errors")
    print(f"   • Real Indonesian content generation")
    print(f"   • Regional filtering working properly")
    print(f"   • Authentic Twitter-like data structure")
    print(f"   • No 'mock_data' flags in responses")

if __name__ == '__main__':
    test_twitter_engine_fixed()
