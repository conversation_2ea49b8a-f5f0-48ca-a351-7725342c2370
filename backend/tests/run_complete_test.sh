#!/bin/bash

# Complete Actor System Test Runner
# This script starts both backend and frontend servers and runs comprehensive tests

echo "🎭 Complete Actor System Test Runner"
echo "===================================="
echo "📅 Started at: $(date)"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "manage.py" ]; then
    print_error "Please run this script from the backend directory"
    exit 1
fi

# Step 1: Check Django setup
print_status "Step 1: Checking Django setup..."
python3 manage.py check
if [ $? -eq 0 ]; then
    print_success "Django setup is valid"
else
    print_error "Django setup has issues. Please fix them first."
    exit 1
fi

# Step 2: Run migrations
print_status "Step 2: Running database migrations..."
python3 manage.py migrate
if [ $? -eq 0 ]; then
    print_success "Database migrations completed"
else
    print_warning "Some migrations may have failed, but continuing..."
fi

# Step 3: Start Django server in background
print_status "Step 3: Starting Django backend server..."
python3 manage.py runserver 8000 > /dev/null 2>&1 &
DJANGO_PID=$!
echo "Django server started with PID: $DJANGO_PID"

# Wait for Django server to start
sleep 5

# Check if Django server is running
if curl -s http://localhost:8000/api/actor/health/ > /dev/null; then
    print_success "Django backend server is running on http://localhost:8000"
else
    print_error "Django backend server failed to start"
    kill $DJANGO_PID 2>/dev/null
    exit 1
fi

# Step 4: Start Frontend server (if exists)
print_status "Step 4: Checking for frontend..."
if [ -d "../frontend" ]; then
    print_status "Starting Next.js frontend server..."
    cd ../frontend
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_status "Installing frontend dependencies..."
        npm install
    fi
    
    # Start frontend server in background
    npm run dev > /dev/null 2>&1 &
    FRONTEND_PID=$!
    echo "Frontend server started with PID: $FRONTEND_PID"
    
    # Wait for frontend server to start
    sleep 10
    
    # Check if frontend server is running
    if curl -s http://localhost:3000 > /dev/null; then
        print_success "Frontend server is running on http://localhost:3000"
    else
        print_warning "Frontend server may not be ready yet"
    fi
    
    cd ../backend
else
    print_warning "Frontend directory not found, skipping frontend server"
    FRONTEND_PID=""
fi

# Step 5: Run the complete Actor system test
print_status "Step 5: Running complete Actor system test..."
echo ""
echo "🧪 STARTING ACTOR SYSTEM TESTS"
echo "=============================="
echo "👤 TikTok Account: grafisone"
echo "🔍 Search Keyword: prabowo"
echo "📊 Expected: Real scraping data saved to database"
echo ""

python3 tests/test_complete_actor_system.py

TEST_EXIT_CODE=$?

# Step 6: Display test results
echo ""
echo "🔍 CHECKING TEST RESULTS"
echo "======================="

# Find the latest test results file
LATEST_RESULTS=$(ls -t tests/actor_system_test_results_*.json 2>/dev/null | head -n1)

if [ -n "$LATEST_RESULTS" ]; then
    print_success "Test results saved to: $LATEST_RESULTS"
    
    # Extract summary from JSON (if jq is available)
    if command -v jq &> /dev/null; then
        echo ""
        echo "📊 TEST SUMMARY:"
        jq -r '.summary | "Total Tests: \(.total_tests)\nPassed: \(.passed_tests)\nFailed: \(.failed_tests)\nSuccess Rate: \(.success_rate)"' "$LATEST_RESULTS"
    else
        print_warning "Install 'jq' to see formatted test summary"
    fi
else
    print_warning "No test results file found"
fi

# Step 7: Show server URLs
echo ""
echo "🌐 SERVER INFORMATION"
echo "===================="
echo "Backend API: http://localhost:8000"
echo "Backend Admin: http://localhost:8000/admin"
echo "API Health: http://localhost:8000/api/actor/health/"
echo "Actor Accounts: http://localhost:8000/api/actor/accounts/list/"

if [ -n "$FRONTEND_PID" ]; then
    echo "Frontend App: http://localhost:3000"
    echo "Actor Accounts Page: http://localhost:3000/actor/accounts"
    echo "Actor Tasks Page: http://localhost:3000/actor/tasks"
    echo "Actor Data Page: http://localhost:3000/actor/data"
fi

# Step 8: Wait for user input to stop servers
echo ""
echo "🎯 MANUAL TESTING READY"
echo "======================"
echo "Both servers are running. You can now:"
echo "1. Test the frontend at http://localhost:3000"
echo "2. Test API endpoints directly"
echo "3. Check the database for scraped data"
echo ""
echo "Press ENTER to stop all servers and exit..."
read

# Step 9: Cleanup - Stop servers
print_status "Stopping servers..."

if [ -n "$DJANGO_PID" ]; then
    kill $DJANGO_PID 2>/dev/null
    print_success "Django server stopped"
fi

if [ -n "$FRONTEND_PID" ]; then
    kill $FRONTEND_PID 2>/dev/null
    print_success "Frontend server stopped"
fi

# Final summary
echo ""
echo "🎉 COMPLETE TEST FINISHED"
echo "========================"
echo "📅 Finished at: $(date)"

if [ $TEST_EXIT_CODE -eq 0 ]; then
    print_success "All tests completed successfully!"
else
    print_warning "Some tests may have failed. Check the results above."
fi

echo ""
echo "📁 Test files are organized in the 'tests/' directory"
echo "📊 Check the latest JSON results file for detailed information"
echo ""
echo "Thank you for testing the Actor System! 🎭✨"
