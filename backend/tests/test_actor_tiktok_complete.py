#!/usr/bin/env python3
"""
Comprehensive test for TikTok Actor login and scraping functionality.
This test validates the complete workflow from login to data scraping.
"""

import os
import sys
import django
import time
import json
from datetime import datetime

# Add the backend directory to Python path
sys.path.append('/Users/<USER>/Documents/fullstax/backend')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor_tiktok.utils.tiktok_auth import TikTok<PERSON>uthenticator
from actor_tiktok.utils.anti_detection import AntiDetectionManager
from actor_tiktok.models import TikTokUserAccount, ActorScrapedData
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class TikTokActorTest:
    def __init__(self):
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'overall_success': False
        }
        self.driver = None
        self.auth = None
        self.anti_detection = None
        
    def setup_driver(self):
        """Setup Chrome driver with anti-detection measures"""
        try:
            print("\n🔧 Setting up Chrome driver with anti-detection...")
            
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # Enable browser logging for JavaScript error detection
            chrome_options.add_experimental_option('goog:loggingPrefs', {
                'browser': 'ALL',
                'driver': 'ALL',
                'performance': 'ALL'
            })
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Initialize anti-detection and auth
            self.anti_detection = AntiDetectionManager()
            self.auth = TikTokAuthenticator()
            
            print("✅ Driver setup completed successfully")
            return True
            
        except Exception as e:
            print(f"❌ Driver setup failed: {str(e)}")
            return False
    
    def test_javascript_error_handling(self):
        """Test JavaScript error detection and atob polyfill"""
        test_name = "javascript_error_handling"
        print(f"\n🧪 Testing {test_name}...")
        
        try:
            # Navigate to TikTok
            self.driver.get('https://www.tiktok.com')
            time.sleep(3)
            
            # Check for JavaScript errors
            js_errors = self.auth._check_javascript_errors(self.driver)
            print(f"📊 Found {len(js_errors)} JavaScript errors")
            
            # Test atob polyfill injection
            self.auth._handle_atob_error(self.driver)
            
            # Verify atob function works
            result = self.driver.execute_script("""
                try {
                    var test = btoa('test');
                    var decoded = atob(test);
                    return decoded === 'test';
                } catch(e) {
                    return false;
                }
            """)
            
            success = result is True
            self.test_results['tests'][test_name] = {
                'success': success,
                'js_errors_found': len(js_errors),
                'atob_working': result
            }
            
            print(f"{'✅' if success else '❌'} JavaScript error handling: {'PASSED' if success else 'FAILED'}")
            return success
            
        except Exception as e:
            print(f"❌ {test_name} failed: {str(e)}")
            self.test_results['tests'][test_name] = {'success': False, 'error': str(e)}
            return False
    
    def test_login_navigation(self):
        """Test navigation to login page and form detection"""
        test_name = "login_navigation"
        print(f"\n🧪 Testing {test_name}...")
        
        try:
            # Navigate to login page
            self.driver.get('https://www.tiktok.com/login')
            time.sleep(5)
            
            # Wait for page load with error handling
            page_load_result = self.auth._wait_for_page_load(self.driver, timeout=15)
            
            # Test dynamic selector system
            selectors = self.auth._get_dynamic_selectors()
            
            # Try to find login method selection
            phone_email_option = self.auth._find_element_with_dynamic_selectors(
                self.driver, 'phone_email_option', timeout=10
            )
            
            success = phone_email_option is not None
            self.test_results['tests'][test_name] = {
                'success': success,
                'page_load_result': page_load_result,
                'phone_email_option_found': phone_email_option is not None,
                'current_url': self.driver.current_url
            }
            
            print(f"{'✅' if success else '❌'} Login navigation: {'PASSED' if success else 'FAILED'}")
            return success
            
        except Exception as e:
            print(f"❌ {test_name} failed: {str(e)}")
            self.test_results['tests'][test_name] = {'success': False, 'error': str(e)}
            return False
    
    def test_login_method_selection(self):
        """Test selecting login method and email/username tab"""
        test_name = "login_method_selection"
        print(f"\n🧪 Testing {test_name}...")
        
        try:
            # Test the complete login method selection
            result = self.auth._select_login_method(self.driver)
            
            # Check if username and password fields are now visible
            username_field = self.auth._find_element_with_dynamic_selectors(
                self.driver, 'username_input', timeout=5
            )
            password_field = self.auth._find_element_with_dynamic_selectors(
                self.driver, 'password_input', timeout=5
            )
            
            success = (result.get('success', False) and 
                      username_field is not None and 
                      password_field is not None)
            
            self.test_results['tests'][test_name] = {
                'success': success,
                'method_selection_result': result,
                'username_field_found': username_field is not None,
                'password_field_found': password_field is not None
            }
            
            print(f"{'✅' if success else '❌'} Login method selection: {'PASSED' if success else 'FAILED'}")
            return success
            
        except Exception as e:
            print(f"❌ {test_name} failed: {str(e)}")
            self.test_results['tests'][test_name] = {'success': False, 'error': str(e)}
            return False
    
    def test_credential_entry(self):
        """Test entering credentials with test data"""
        test_name = "credential_entry"
        print(f"\n🧪 Testing {test_name}...")
        
        try:
            # Use test credentials (these won't actually log in)
            test_username = "<EMAIL>"
            test_password = "test_password_123"
            
            # Test credential entry
            result = self.auth._enter_credentials(self.driver, test_username, test_password)
            
            # Verify fields were filled
            username_field = self.auth._find_element_with_dynamic_selectors(
                self.driver, 'username_input', timeout=3
            )
            password_field = self.auth._find_element_with_dynamic_selectors(
                self.driver, 'password_input', timeout=3
            )
            
            username_filled = username_field and username_field.get_attribute('value')
            password_filled = password_field and password_field.get_attribute('value')
            
            success = (result.get('success', False) and 
                      username_filled and 
                      password_filled)
            
            self.test_results['tests'][test_name] = {
                'success': success,
                'credential_entry_result': result,
                'username_filled': bool(username_filled),
                'password_filled': bool(password_filled)
            }
            
            print(f"{'✅' if success else '❌'} Credential entry: {'PASSED' if success else 'FAILED'}")
            return success
            
        except Exception as e:
            print(f"❌ {test_name} failed: {str(e)}")
            self.test_results['tests'][test_name] = {'success': False, 'error': str(e)}
            return False
    
    def test_form_submission_detection(self):
        """Test form submission button detection and interaction"""
        test_name = "form_submission_detection"
        print(f"\n🧪 Testing {test_name}...")
        
        try:
            # Find submit button
            submit_button = self.auth._find_element_with_dynamic_selectors(
                self.driver, 'login_button', timeout=5
            )
            
            # Test button interaction (without actually submitting)
            button_clickable = False
            if submit_button:
                try:
                    # Scroll to button
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", submit_button)
                    time.sleep(1)
                    
                    # Check if button is clickable
                    button_clickable = submit_button.is_enabled() and submit_button.is_displayed()
                except Exception:
                    pass
            
            success = submit_button is not None and button_clickable
            
            self.test_results['tests'][test_name] = {
                'success': success,
                'submit_button_found': submit_button is not None,
                'button_clickable': button_clickable
            }
            
            print(f"{'✅' if success else '❌'} Form submission detection: {'PASSED' if success else 'FAILED'}")
            return success
            
        except Exception as e:
            print(f"❌ {test_name} failed: {str(e)}")
            self.test_results['tests'][test_name] = {'success': False, 'error': str(e)}
            return False
    
    def test_success_verification_system(self):
        """Test the login success verification system"""
        test_name = "success_verification_system"
        print(f"\n🧪 Testing {test_name}...")
        
        try:
            # Test success indicators detection
            success_indicators = self.auth._get_success_indicators()
            
            # Verify we have comprehensive selectors
            has_css_selectors = len(success_indicators.get('css_selectors', [])) > 0
            has_xpath_selectors = len(success_indicators.get('xpath_selectors', [])) > 0
            has_url_patterns = len(success_indicators.get('url_patterns', [])) > 0
            
            # Test verification method (should detect we're still on login page)
            verification_result = self.auth._verify_login_success(self.driver)
            
            success = (has_css_selectors and has_xpath_selectors and has_url_patterns)
            
            self.test_results['tests'][test_name] = {
                'success': success,
                'css_selectors_count': len(success_indicators.get('css_selectors', [])),
                'xpath_selectors_count': len(success_indicators.get('xpath_selectors', [])),
                'url_patterns_count': len(success_indicators.get('url_patterns', [])),
                'verification_result': verification_result
            }
            
            print(f"{'✅' if success else '❌'} Success verification system: {'PASSED' if success else 'FAILED'}")
            return success
            
        except Exception as e:
            print(f"❌ {test_name} failed: {str(e)}")
            self.test_results['tests'][test_name] = {'success': False, 'error': str(e)}
            return False
    
    def test_database_integration(self):
        """Test database models and integration"""
        test_name = "database_integration"
        print(f"\n🧪 Testing {test_name}...")
        
        try:
            # Test TikTokAccount model
            account_count = TikTokAccount.objects.count()
            
            # Test ScrapedData model
            scraped_data_count = ScrapedData.objects.count()
            
            # Test creating a test account (will be cleaned up)
            test_account = TikTokAccount.objects.create(
                username='test_account_' + str(int(time.time())),
                email='<EMAIL>',
                is_active=False,  # Mark as test account
                account_type='test'
            )
            
            # Clean up test account
            test_account.delete()
            
            success = True  # If we reach here, database is working
            
            self.test_results['tests'][test_name] = {
                'success': success,
                'account_count': account_count,
                'scraped_data_count': scraped_data_count,
                'test_account_created': True
            }
            
            print(f"✅ Database integration: PASSED")
            return success
            
        except Exception as e:
            print(f"❌ {test_name} failed: {str(e)}")
            self.test_results['tests'][test_name] = {'success': False, 'error': str(e)}
            return False
    
    def cleanup(self):
        """Clean up resources"""
        try:
            if self.driver:
                self.driver.quit()
                print("\n🧹 Driver cleanup completed")
        except Exception as e:
            print(f"⚠️ Cleanup warning: {str(e)}")
    
    def run_all_tests(self):
        """Run all tests in sequence"""
        print("🚀 Starting comprehensive TikTok Actor tests...")
        print("=" * 60)
        
        # Setup
        if not self.setup_driver():
            print("❌ Cannot proceed without driver setup")
            return False
        
        # Run tests in order
        tests = [
            self.test_javascript_error_handling,
            self.test_login_navigation,
            self.test_login_method_selection,
            self.test_credential_entry,
            self.test_form_submission_detection,
            self.test_success_verification_system,
            self.test_database_integration
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed_tests += 1
                time.sleep(2)  # Brief pause between tests
            except Exception as e:
                print(f"❌ Test failed with exception: {str(e)}")
        
        # Calculate overall success
        self.test_results['overall_success'] = passed_tests == total_tests
        self.test_results['passed_tests'] = passed_tests
        self.test_results['total_tests'] = total_tests
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if self.test_results['overall_success']:
            print("\n🎉 ALL TESTS PASSED! TikTok Actor system is ready for production.")
        else:
            print("\n⚠️ Some tests failed. Please review the results above.")
        
        # Save results to file
        results_file = f"/Users/<USER>/Documents/fullstax/backend/test_results_{int(time.time())}.json"
        with open(results_file, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        print(f"\n📄 Detailed results saved to: {results_file}")
        
        self.cleanup()
        return self.test_results['overall_success']

if __name__ == "__main__":
    tester = TikTokActorTest()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)