#!/usr/bin/env python3

"""
Test CRUD API for Scraped Data
"""

import os
import sys
import django
import requests

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from actor.models import ActorTask, ActorScrapedData

def test_crud_api():
    print("🔧 CRUD API Test")
    print("=" * 50)
    
    # Get test user and token
    user = User.objects.get(username='test_actor_user')
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    headers = {
        'Authorization': f'JWT {access_token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000/api/actor'
    
    print(f"🔑 User: {user.username}")
    
    # Get a task to use for testing
    task = ActorTask.objects.filter(user=user).first()
    if not task:
        print("❌ No tasks found for testing")
        return
    
    print(f"📋 Using task: {task.id} - {task.task_name}")
    
    # Test 1: CREATE - Create new scraped data
    print(f"\n1️⃣ Testing CREATE operation...")
    
    create_data = {
        'task_id': task.id,
        'data_type': 'VIDEO',
        'content': {
            'title': 'Test CRUD Video',
            'author': 'test_user_crud',
            'description': 'This is a test video created via CRUD API',
            'likes': 1000,
            'views': 5000,
            'url': 'https://example.com/test-crud-video'
        },
        'platform': 'tiktok',
        'account_username': 'test_crud_user',
        'platform_content_id': 'crud_test_123',
        'is_complete': True
    }
    
    response = requests.post(f'{base_url}/data/create/', 
                           headers=headers, 
                           json=create_data, 
                           timeout=10)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            created_id = result['data']['id']
            print(f"   ✅ CREATE successful: ID {created_id}")
            print(f"   Data: {result['data']['content']['title']}")
        else:
            print(f"   ❌ CREATE failed: {result.get('error')}")
            return
    else:
        print(f"   ❌ CREATE request failed: {response.status_code}")
        print(f"   Response: {response.text}")
        return
    
    # Test 2: READ - Get the created data
    print(f"\n2️⃣ Testing READ operation...")
    
    response = requests.get(f'{base_url}/data/', 
                          headers=headers, 
                          params={'page_size': 5}, 
                          timeout=10)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            items = result.get('results', [])
            found_item = None
            for item in items:
                if item['id'] == created_id:
                    found_item = item
                    break
            
            if found_item:
                print(f"   ✅ READ successful: Found created item")
                print(f"   Title: {found_item['content']['title']}")
                print(f"   Author: {found_item['content']['author']}")
            else:
                print(f"   ⚠️  Created item not found in recent data")
        else:
            print(f"   ❌ READ failed: {result.get('error')}")
    else:
        print(f"   ❌ READ request failed: {response.status_code}")
    
    # Test 3: UPDATE - Update the created data
    print(f"\n3️⃣ Testing UPDATE operation...")
    
    update_data = {
        'data_type': 'VIDEO',
        'content': {
            'title': 'Updated CRUD Video',
            'author': 'test_user_crud_updated',
            'description': 'This video was updated via CRUD API',
            'likes': 2000,
            'views': 10000,
            'url': 'https://example.com/updated-crud-video',
            'updated': True
        },
        'platform_content_id': 'crud_test_updated_456',
        'account_username': 'updated_crud_user',
        'is_complete': True
    }
    
    response = requests.put(f'{base_url}/data/{created_id}/', 
                          headers=headers, 
                          json=update_data, 
                          timeout=10)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print(f"   ✅ UPDATE successful")
            print(f"   Updated title: {result['data']['content']['title']}")
            print(f"   Updated author: {result['data']['content']['author']}")
        else:
            print(f"   ❌ UPDATE failed: {result.get('error')}")
    else:
        print(f"   ❌ UPDATE request failed: {response.status_code}")
        print(f"   Response: {response.text}")
    
    # Test 4: READ again to verify update
    print(f"\n4️⃣ Testing READ after UPDATE...")
    
    response = requests.get(f'{base_url}/data/', 
                          headers=headers, 
                          params={'page_size': 5}, 
                          timeout=10)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            items = result.get('results', [])
            found_item = None
            for item in items:
                if item['id'] == created_id:
                    found_item = item
                    break
            
            if found_item:
                print(f"   ✅ READ after UPDATE successful")
                print(f"   Title: {found_item['content']['title']}")
                print(f"   Author: {found_item['content']['author']}")
                print(f"   Updated flag: {found_item['content'].get('updated', False)}")
            else:
                print(f"   ⚠️  Updated item not found")
    
    # Test 5: CREATE another item for bulk delete test
    print(f"\n5️⃣ Creating second item for bulk delete test...")
    
    create_data2 = {
        'task_id': task.id,
        'data_type': 'profile',
        'content': {
            'username': 'bulk_delete_test',
            'followers': 500,
            'bio': 'Test profile for bulk delete'
        },
        'platform': 'tiktok',
        'account_username': 'bulk_test_user',
        'platform_content_id': 'bulk_test_789',
        'is_complete': True
    }
    
    response = requests.post(f'{base_url}/data/create/', 
                           headers=headers, 
                           json=create_data2, 
                           timeout=10)
    
    created_id2 = None
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            created_id2 = result['data']['id']
            print(f"   ✅ Second item created: ID {created_id2}")
    
    # Test 6: BULK DELETE
    print(f"\n6️⃣ Testing BULK DELETE operation...")
    
    if created_id2:
        bulk_delete_data = {
            'data_ids': [created_id, created_id2]
        }
        
        response = requests.post(f'{base_url}/data/bulk-delete/', 
                               headers=headers, 
                               json=bulk_delete_data, 
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"   ✅ BULK DELETE successful")
                print(f"   Deleted count: {result.get('deleted_count')}")
                print(f"   Deleted items: {[item['id'] for item in result.get('deleted_data', [])]}")
            else:
                print(f"   ❌ BULK DELETE failed: {result.get('error')}")
        else:
            print(f"   ❌ BULK DELETE request failed: {response.status_code}")
    else:
        # Test single DELETE instead
        print(f"   Testing single DELETE instead...")
        
        response = requests.delete(f'{base_url}/data/{created_id}/delete/', 
                                 headers=headers, 
                                 timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"   ✅ DELETE successful")
                print(f"   Deleted item: {result.get('deleted_data')}")
            else:
                print(f"   ❌ DELETE failed: {result.get('error')}")
        else:
            print(f"   ❌ DELETE request failed: {response.status_code}")
    
    # Test 7: Verify deletion
    print(f"\n7️⃣ Testing READ after DELETE...")
    
    response = requests.get(f'{base_url}/data/', 
                          headers=headers, 
                          params={'page_size': 10}, 
                          timeout=10)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            items = result.get('results', [])
            found_items = [item for item in items if item['id'] in [created_id, created_id2 or 0]]
            
            if not found_items:
                print(f"   ✅ DELETE verification successful: Items not found (deleted)")
            else:
                print(f"   ⚠️  Some items still exist: {[item['id'] for item in found_items]}")
    
    print("\n" + "=" * 50)
    print("🎉 CRUD API Test Complete!")
    
    print(f"\n📋 CRUD OPERATIONS TESTED:")
    print(f"✅ CREATE - Create new scraped data entries")
    print(f"✅ READ - Retrieve scraped data with filtering")
    print(f"✅ UPDATE - Modify existing scraped data")
    print(f"✅ DELETE - Remove individual data entries")
    print(f"✅ BULK DELETE - Remove multiple data entries")
    
    print(f"\n🎯 API ENDPOINTS:")
    print(f"   POST   /api/actor/data/create/")
    print(f"   GET    /api/actor/data/")
    print(f"   PUT    /api/actor/data/<id>/")
    print(f"   DELETE /api/actor/data/<id>/delete/")
    print(f"   POST   /api/actor/data/bulk-delete/")
    
    print(f"\n💡 FRONTEND INTEGRATION:")
    print(f"   • Enhanced data page with CRUD operations")
    print(f"   • Create modal for adding new data")
    print(f"   • Edit modal for updating existing data")
    print(f"   • Delete buttons with confirmation")
    print(f"   • Bulk selection and deletion")
    print(f"   • Real-time data refresh after operations")

if __name__ == '__main__':
    test_crud_api()
