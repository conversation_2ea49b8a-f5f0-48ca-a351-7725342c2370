#!/usr/bin/env python3
import os
import sys
import django
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

def test_tiktok_access():
    """Simple test to check TikTok access and available selectors."""
    
    # Setup Chrome options
    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    driver = None
    try:
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)
        
        print("Accessing TikTok search page...")
        search_url = "https://www.tiktok.com/search?q=prabowo"
        driver.get(search_url)
        
        print("Waiting for page to load...")
        time.sleep(5)
        
        print("Page title:", driver.title)
        print("Current URL:", driver.current_url)
        
        # Check for various possible selectors
        selectors_to_check = [
            'div[data-e2e="search-card-container"]',
            'div[data-e2e="search-result"]',
            'div[class*="search"]',
            'div[class*="video"]',
            'div[class*="card"]',
            '[data-e2e]',
            'video',
            'main',
            'body > div'
        ]
        
        for selector in selectors_to_check:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                print(f"Selector '{selector}': Found {len(elements)} elements")
                if len(elements) > 0 and len(elements) < 10:
                    for i, elem in enumerate(elements[:3]):
                        print(f"  Element {i+1}: {elem.tag_name}, classes: {elem.get_attribute('class')}")
            except Exception as e:
                print(f"Error with selector '{selector}': {e}")
        
        # Get page source snippet
        page_source = driver.page_source
        print(f"\nPage source length: {len(page_source)}")
        print("First 500 characters of page source:")
        print(page_source[:500])
        
        # Look for data-e2e attributes
        import re
        data_e2e_matches = re.findall(r'data-e2e="([^"]+)"', page_source)
        unique_data_e2e = list(set(data_e2e_matches))
        print(f"\nFound {len(unique_data_e2e)} unique data-e2e attributes:")
        for attr in sorted(unique_data_e2e)[:20]:  # Show first 20
            print(f"  data-e2e='{attr}'")
            
    except Exception as e:
        print(f"Error during test: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if driver:
            driver.quit()

if __name__ == '__main__':
    test_tiktok_access()