#!/usr/bin/env python3
"""
Enhanced TikTok Actor Demo

This script demonstrates how to use the enhanced TikTok actor system
with proper configuration and best practices.
"""

import os
import sys
import django
import logging
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def demo_configuration():
    """
    Demonstrate configuration options for the enhanced TikTok actor
    """
    logger.info("=== Enhanced TikTok Actor Configuration Demo ===")
    
    # Import configuration
    from actor_tiktok.config.enhanced_config import (
        ENHANCED_ANTI_DETECTION,
        PROXY_CONFIG,
        RATE_LIMITING_CONFIG,
        SCRAPING_CONFIG,
        ERROR_HANDLING_CONFIG,
        USE_CASE_CONFIGS
    )
    
    logger.info("📋 Available Configuration Options:")
    logger.info(f"✅ Enhanced Anti-Detection: {len(ENHANCED_ANTI_DETECTION)} settings")
    logger.info(f"✅ Proxy Configuration: {len(PROXY_CONFIG)} settings")
    logger.info(f"✅ Rate Limiting: {len(RATE_LIMITING_CONFIG)} settings")
    logger.info(f"✅ Scraping Options: {len(SCRAPING_CONFIG)} settings")
    logger.info(f"✅ Error Handling: {len(ERROR_HANDLING_CONFIG)} settings")
    
    logger.info("\n🎯 Pre-configured Use Cases:")
    for use_case, config in USE_CASE_CONFIGS.items():
        logger.info(f"  • {use_case}: {len(config)} configuration sections")
    
    # Example configuration for different scenarios
    logger.info("\n🔧 Example Configurations:")
    
    # Stealth mode configuration
    stealth_config = {
        'proxy_config': {
            'enabled': True,
            'proxies': [
                {
                    'host': '127.0.0.1',
                    'port': 8080,
                    'protocol': 'http'
                }
            ],
            'rotation_strategy': 'random',
            'max_failures': 3
        },
        'rate_limiting': {
            'requests_per_minute': 15,
            'requests_per_hour': 200,
            'delay_between_requests': (5, 10)
        },
        'anti_detection': {
            'use_undetected_chrome': True,
            'stealth_mode_enabled': True,
            'randomize_viewports': True
        }
    }
    
    logger.info("🥷 Stealth Mode Configuration:")
    logger.info(f"  • Proxy enabled: {stealth_config['proxy_config']['enabled']}")
    logger.info(f"  • Rate limit: {stealth_config['rate_limiting']['requests_per_minute']} req/min")
    logger.info(f"  • Undetected Chrome: {stealth_config['anti_detection']['use_undetected_chrome']}")
    
    # High-volume configuration
    high_volume_config = {
        'proxy_config': {
            'enabled': True,
            'proxies': [],  # Would contain multiple proxies
            'rotation_strategy': 'best_performance'
        },
        'rate_limiting': {
            'requests_per_minute': 60,
            'requests_per_hour': 1000,
            'delay_between_requests': (2, 4)
        }
    }
    
    logger.info("\n🚀 High-Volume Configuration:")
    logger.info(f"  • Rate limit: {high_volume_config['rate_limiting']['requests_per_minute']} req/min")
    logger.info(f"  • Proxy strategy: {high_volume_config['proxy_config']['rotation_strategy']}")
    
    return True

def demo_authenticator_features():
    """
    Demonstrate the enhanced authenticator features
    """
    logger.info("\n=== Enhanced Authenticator Features Demo ===")
    
    try:
        from actor_tiktok.utils.enhanced_tiktok_auth import EnhancedTikTokAuthenticator
        
        # Initialize authenticator
        authenticator = EnhancedTikTokAuthenticator()
        
        logger.info("🔐 Authenticator Features:")
        logger.info(f"✅ Available login strategies: {len(authenticator.login_strategies)}")
        for strategy_name, _ in authenticator.login_strategies:
            logger.info(f"  • {strategy_name}")
        
        logger.info(f"✅ Available recovery strategies: {len(authenticator.recovery_strategies)}")
        for strategy_name in authenticator.recovery_strategies.keys():
            logger.info(f"  • {strategy_name}")
        
        # Demonstrate error classification
        test_errors = [
            "Captcha verification required",
            "Rate limit exceeded",
            "Suspicious activity detected",
            "Account temporarily locked",
            "Session expired"
        ]
        
        logger.info("\n🔍 Error Classification Demo:")
        for error in test_errors:
            error_type = authenticator._classify_error(error)
            logger.info(f"  • '{error}' → {error_type}")
        
        return True
        
    except Exception as e:
        logger.error(f"Authenticator demo failed: {str(e)}")
        return False

def demo_scraper_features():
    """
    Demonstrate the enhanced scraper features
    """
    logger.info("\n=== Enhanced Scraper Features Demo ===")
    
    try:
        from actor_tiktok.utils.enhanced_scraper import EnhancedTikTokScraper
        
        # Initialize scraper with configuration
        rate_limit_config = {
            'requests_per_minute': 30,
            'requests_per_hour': 500,
            'delay_between_requests': (2, 5)
        }
        
        scraper = EnhancedTikTokScraper(rate_limit_config=rate_limit_config)
        
        logger.info("🔍 Scraper Features:")
        logger.info(f"✅ Available selectors: {len(scraper.selectors)}")
        for selector_name in scraper.selectors.keys():
            logger.info(f"  • {selector_name}")
        
        logger.info(f"✅ Rate limiting configured:")
        logger.info(f"  • Requests per minute: {scraper.rate_limits['requests_per_minute']}")
        logger.info(f"  • Requests per hour: {scraper.rate_limits['requests_per_hour']}")
        
        # Demonstrate rate limiting check
        rate_limit_ok = scraper._check_rate_limit()
        logger.info(f"✅ Rate limit check: {'PASS' if rate_limit_ok else 'FAIL'}")
        
        # Demonstrate scraping capabilities
        logger.info("\n📊 Scraping Capabilities:")
        capabilities = [
            "User profile scraping",
            "Video details extraction",
            "Trending videos collection",
            "Content search",
            "Comment extraction",
            "Engagement metrics"
        ]
        
        for capability in capabilities:
            logger.info(f"  • {capability}")
        
        return True
        
    except Exception as e:
        logger.error(f"Scraper demo failed: {str(e)}")
        return False

def demo_proxy_management():
    """
    Demonstrate proxy management features
    """
    logger.info("\n=== Proxy Management Demo ===")
    
    try:
        from actor_tiktok.utils.proxy_manager import ProxyManager
        
        # Sample proxy configuration
        sample_proxies = [
            {
                'host': '127.0.0.1',
                'port': 8080,
                'protocol': 'http',
                'country': 'US'
            },
            {
                'host': '127.0.0.1',
                'port': 8081,
                'protocol': 'http',
                'country': 'UK'
            }
        ]
        
        # Initialize proxy manager
        proxy_manager = ProxyManager(
            proxies=sample_proxies,
            max_failures=3,
            health_check_interval=300
        )
        
        logger.info("🌐 Proxy Management Features:")
        logger.info(f"✅ Loaded proxies: {len(proxy_manager.proxies)}")
        logger.info(f"✅ Working proxies: {len(proxy_manager.get_working_proxies())}")
        
        # Get proxy statistics
        stats = proxy_manager.get_proxy_stats()
        logger.info(f"✅ Proxy statistics:")
        for key, value in stats.items():
            if key != 'countries':  # Skip set display
                logger.info(f"  • {key}: {value}")
        
        # Demonstrate proxy selection strategies
        logger.info("\n🎯 Proxy Selection Strategies:")
        
        # Get next proxy (round-robin)
        next_proxy = proxy_manager.get_next_proxy()
        if next_proxy:
            logger.info(f"  • Round-robin: {next_proxy.host}:{next_proxy.port}")
        
        # Get random proxy
        random_proxy = proxy_manager.get_random_proxy()
        if random_proxy:
            logger.info(f"  • Random: {random_proxy.host}:{random_proxy.port}")
        
        # Get best proxy (by performance)
        best_proxy = proxy_manager.get_best_proxy()
        if best_proxy:
            logger.info(f"  • Best performance: {best_proxy.host}:{best_proxy.port}")
        
        return True
        
    except Exception as e:
        logger.error(f"Proxy management demo failed: {str(e)}")
        return False

def demo_session_management():
    """
    Demonstrate session management features
    """
    logger.info("\n=== Session Management Demo ===")
    
    try:
        from actor_tiktok.utils.session_manager import EnhancedSessionManager
        
        # Initialize session manager
        session_manager = EnhancedSessionManager()
        
        logger.info("💾 Session Management Features:")
        logger.info("✅ Encrypted session storage")
        logger.info("✅ Automatic session backup")
        logger.info("✅ Session health monitoring")
        logger.info("✅ Smart session rotation")
        
        # Demonstrate session data structure
        sample_session_data = {
            'cookies': [
                {'name': 'session_id', 'value': 'abc123', 'domain': '.tiktok.com'},
                {'name': 'user_token', 'value': 'xyz789', 'domain': '.tiktok.com'}
            ],
            'local_storage': {
                'user_preferences': '{"theme":"dark"}',
                'last_activity': '2025-07-18T12:00:00Z'
            },
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'timestamp': '2025-07-18T12:00:00Z'
        }
        
        logger.info(f"\n📋 Session Data Structure:")
        logger.info(f"  • Cookies: {len(sample_session_data['cookies'])} items")
        logger.info(f"  • Local storage: {len(sample_session_data['local_storage'])} items")
        logger.info(f"  • User agent: {sample_session_data['user_agent'][:50]}...")
        
        # Demonstrate session quality calculation
        quality_score = session_manager._calculate_session_quality(sample_session_data)
        logger.info(f"  • Quality score: {quality_score}/100")
        
        return True
        
    except Exception as e:
        logger.error(f"Session management demo failed: {str(e)}")
        return False

def demo_usage_examples():
    """
    Show practical usage examples
    """
    logger.info("\n=== Usage Examples ===")
    
    logger.info("🔧 Basic Usage Example:")
    logger.info("""
# 1. Initialize Enhanced Authenticator
from actor_tiktok.utils.enhanced_tiktok_auth import EnhancedTikTokAuthenticator

proxy_config = {
    'enabled': True,
    'proxies': [{'host': '127.0.0.1', 'port': 8080, 'protocol': 'http'}]
}

authenticator = EnhancedTikTokAuthenticator(proxy_config=proxy_config)

# 2. Perform Login
login_result = authenticator.login(
    username='your_username',
    password='your_password',
    account_id=1
)

# 3. Initialize Scraper
from actor_tiktok.utils.enhanced_scraper import EnhancedTikTokScraper

scraper = EnhancedTikTokScraper(proxy_config=proxy_config)
scraper.login_and_prepare('username', 'password', account_id=1)

# 4. Scrape Data
profile_data = scraper.scrape_user_profile('target_username')
""")
    
    logger.info("\n🚀 Celery Task Example:")
    logger.info("""
# Using Celery for asynchronous processing
from actor_tiktok.tasks import enhanced_actor_login_task, enhanced_scraping_task

# Submit login task
login_task = enhanced_actor_login_task.delay(
    user_id=1,
    tiktok_username='username',
    tiktok_password='password',
    proxy_config=proxy_config
)

# Submit scraping task
scraping_config = {
    'type': 'profile',
    'target': 'target_username',
    'options': {'include_videos': True, 'video_limit': 50}
}

scraping_task = enhanced_scraping_task.delay(
    user_id=1,
    account_id=1,
    scraping_config=scraping_config
)
""")
    
    return True

def run_demo():
    """
    Run the complete demo
    """
    logger.info("🎬 Enhanced TikTok Actor Demo")
    logger.info(f"Timestamp: {datetime.now().isoformat()}")
    logger.info("="*60)
    
    demos = [
        ("Configuration Options", demo_configuration),
        ("Authenticator Features", demo_authenticator_features),
        ("Scraper Features", demo_scraper_features),
        ("Proxy Management", demo_proxy_management),
        ("Session Management", demo_session_management),
        ("Usage Examples", demo_usage_examples)
    ]
    
    results = {}
    
    for demo_name, demo_func in demos:
        try:
            result = demo_func()
            results[demo_name] = result
            
            if result:
                logger.info(f"✅ {demo_name} demo completed")
            else:
                logger.warning(f"⚠️ {demo_name} demo had issues")
                
        except Exception as e:
            logger.error(f"❌ {demo_name} demo failed: {str(e)}")
            results[demo_name] = False
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("DEMO SUMMARY")
    logger.info(f"{'='*60}")
    
    successful = sum(1 for result in results.values() if result)
    total = len(results)
    
    logger.info(f"Demos completed: {successful}/{total}")
    
    for demo_name, result in results.items():
        status = "✅" if result else "❌"
        logger.info(f"{status} {demo_name}")
    
    logger.info("\n🎯 Next Steps:")
    logger.info("1. Update credentials in test_real_tiktok_functionality.py for real testing")
    logger.info("2. Configure proxies for production use")
    logger.info("3. Set up Redis and Celery for asynchronous processing")
    logger.info("4. Review and adjust rate limiting settings")
    logger.info("5. Test with your specific TikTok scraping requirements")
    
    logger.info("\n🏁 Demo completed!")
    return results

if __name__ == "__main__":
    results = run_demo()
