#!/usr/bin/env python3
"""
Simple test to inspect TikTok login page structure
"""

import os
import sys
import django
from pathlib import Path
import time

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor_tiktok.utils.anti_detection import AntiDetectionManager
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def inspect_tiktok_login_page():
    """Inspect the current TikTok login page structure"""
    driver = None
    try:
        # Create driver with anti-detection
        anti_detection = AntiDetectionManager()
        driver = anti_detection.setup_driver(headless=False)
        
        print("Navigating to TikTok login page...")
        driver.get("https://www.tiktok.com/login/phone-or-email/email")
        
        # Wait for page to load
        time.sleep(5)
        
        print(f"Current URL: {driver.current_url}")
        print(f"Page title: {driver.title}")
        
        # Check for common input fields
        print("\nLooking for input fields...")
        inputs = driver.find_elements("css selector", "input")
        for i, inp in enumerate(inputs):
            try:
                input_type = inp.get_attribute("type") or "text"
                input_name = inp.get_attribute("name") or "N/A"
                input_placeholder = inp.get_attribute("placeholder") or "N/A"
                input_id = inp.get_attribute("id") or "N/A"
                input_class = inp.get_attribute("class") or "N/A"
                print(f"  Input {i+1}: type='{input_type}', name='{input_name}', placeholder='{input_placeholder}', id='{input_id}', class='{input_class[:50]}...'")
            except Exception as e:
                print(f"  Input {i+1}: Error getting attributes - {str(e)}")
        
        # Check for buttons
        print("\nLooking for buttons...")
        buttons = driver.find_elements("css selector", "button")
        for i, btn in enumerate(buttons):
            try:
                btn_text = btn.text or "N/A"
                btn_type = btn.get_attribute("type") or "N/A"
                btn_class = btn.get_attribute("class") or "N/A"
                btn_data_testid = btn.get_attribute("data-testid") or "N/A"
                print(f"  Button {i+1}: text='{btn_text}', type='{btn_type}', class='{btn_class[:50]}...', data-testid='{btn_data_testid}'")
            except Exception as e:
                print(f"  Button {i+1}: Error getting attributes - {str(e)}")
        
        # Check for forms
        print("\nLooking for forms...")
        forms = driver.find_elements("css selector", "form")
        for i, form in enumerate(forms):
            try:
                form_action = form.get_attribute("action") or "N/A"
                form_method = form.get_attribute("method") or "N/A"
                form_class = form.get_attribute("class") or "N/A"
                print(f"  Form {i+1}: action='{form_action}', method='{form_method}', class='{form_class[:50]}...'")
            except Exception as e:
                print(f"  Form {i+1}: Error getting attributes - {str(e)}")
        
        # Get page source snippet
        print("\nPage source snippet (first 1000 chars):")
        page_source = driver.page_source
        print(page_source[:1000] + "...")
        
        # Wait for manual inspection
        input("\nPress Enter to close the browser...")
        
    except Exception as e:
        print(f"Error during inspection: {str(e)}")
    finally:
        if driver:
            try:
                driver.quit()
            except Exception as e:
                print(f"Error closing driver: {str(e)}")

if __name__ == "__main__":
    inspect_tiktok_login_page()