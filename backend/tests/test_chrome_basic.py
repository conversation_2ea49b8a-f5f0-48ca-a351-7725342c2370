#!/usr/bin/env python3
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import time

def test_basic_chrome():
    """Test basic Chrome functionality"""
    print("Testing basic Chrome setup...")
    
    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    
    driver = None
    try:
        print("Creating Chrome driver...")
        driver = webdriver.Chrome(options=chrome_options)
        print("Chrome driver created successfully")
        
        print("Navigating to Google...")
        driver.get("https://www.google.com")
        print(f"Page title: {driver.title}")
        print(f"Page loaded successfully: {len(driver.page_source)} characters")
        
        print("Basic Chrome test passed!")
        return True
        
    except Exception as e:
        print(f"Chrome test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if driver:
            driver.quit()
            print("Chrome driver closed")

if __name__ == '__main__':
    success = test_basic_chrome()
    if success:
        print("\n✅ Chrome is working properly")
    else:
        print("\n❌ Chrome setup has issues")
        sys.exit(1)