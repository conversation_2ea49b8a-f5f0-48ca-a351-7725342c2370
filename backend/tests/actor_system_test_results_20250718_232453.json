{"timestamp": "2025-07-18T23:24:41.181539", "tests": {"create_actor_account": {"success": true, "message": "Account created successfully with ID: 3", "data": {"id": 3, "platform": "tiktok", "username": "grafisone", "email": "<EMAIL>", "is_active": true, "created_at": "2025-07-18T23:24:41.201404+00:00"}, "timestamp": "2025-07-18T23:24:41.208646"}, "authenticate_account": {"success": false, "message": "Exception during authentication: ActorService.authenticate_account() got an unexpected keyword argument 'user'", "data": null, "timestamp": "2025-07-18T23:24:43.210825"}, "create_scraping_task": {"success": true, "message": "Task created successfully with ID: 1", "data": {"id": 1, "name": "Test Scraping - prabow<PERSON>", "type": "CONTENT_SEARCH", "platform": "tiktok", "status": "PENDING", "created_at": "2025-07-18T23:24:45.219992+00:00"}, "timestamp": "2025-07-18T23:24:45.226764"}, "execute_scraping_task": {"success": false, "message": "Task execution failed: Session invalid, please re-authenticate", "data": null, "timestamp": "2025-07-18T23:24:47.232607"}, "verify_scraped_data": {"success": false, "message": "Exception during data verification: Cannot resolve keyword 'user' into field. Choices are: account_username, actor_account, actor_account_id, content, data_type, id, is_complete, platform, platform_content_id, quality_score, scraped_at, task, task_id, tiktok_id", "data": null, "timestamp": "2025-07-18T23:24:49.239823"}, "data_quality_check": {"success": true, "message": "Data quality stats retrieved successfully", "data": {"total_items": 0, "complete_items": 0, "completion_rate": 0, "average_quality_score": 0, "platform_breakdown": [], "data_type_breakdown": []}, "timestamp": "2025-07-18T23:24:51.253184"}}, "summary": {"total_tests": 6, "passed_tests": 3, "failed_tests": 3, "success_rate": "50.0%"}}