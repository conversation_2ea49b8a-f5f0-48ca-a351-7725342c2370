#!/usr/bin/env python3

"""
Test the data API to see why data is not showing on frontend
"""

import os
import sys
import django
import requests
import json

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from actor.models import ActorTask, ActorAccount, ActorScrapedData

def test_data_api():
    print("🔍 Testing Data API")
    print("=" * 50)
    
    # Get test user and token
    user = User.objects.get(username='test_actor_user')
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    headers = {
        'Authorization': f'JWT {access_token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000/api'
    
    print(f"🔑 User: {user.username}")
    
    # Check database state first
    print(f"\n📊 Database State:")
    all_data = ActorScrapedData.objects.filter(task__user=user)
    print(f"   Total items in database: {all_data.count()}")
    
    for item in all_data[:3]:
        print(f"   - ID: {item.id}, Type: {item.data_type}, Quality: {item.quality_score}")
        print(f"     Author: {item.content.get('author', 'N/A') if item.content else 'No content'}")
        print(f"     Title: {item.content.get('title', 'N/A') if item.content else 'No content'}")
    
    # Test 1: Basic data API call
    print(f"\n🌐 1. Testing Basic Data API...")
    response = requests.get(f'{base_url}/actor/data/', headers=headers, timeout=10)
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"   Success: {result.get('success')}")
        print(f"   Results count: {len(result.get('results', []))}")
        print(f"   Total: {result.get('total', 0)}")
        
        if result.get('results'):
            print(f"   Sample results:")
            for i, item in enumerate(result.get('results', [])[:3]):
                print(f"     Item {i+1}:")
                print(f"       ID: {item.get('id')}")
                print(f"       Data Type: {item.get('data_type')}")
                print(f"       Quality: {item.get('quality_score')}")
                print(f"       Author: {item.get('content', {}).get('author', 'N/A')}")
                print(f"       Title: {item.get('content', {}).get('title', 'N/A')}")
        else:
            print(f"   ❌ No results returned from API")
    else:
        print(f"   ❌ API Error: {response.text}")
    
    # Test 2: Data API with filters
    print(f"\n🔍 2. Testing Data API with Filters...")
    account = ActorAccount.objects.filter(user=user).first()
    if account:
        response = requests.get(f'{base_url}/actor/data/', 
                              headers=headers, 
                              params={'account_id': account.id}, 
                              timeout=10)
        print(f"   Status with account filter: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   Results with account filter: {len(result.get('results', []))}")
        else:
            print(f"   ❌ API Error with filter: {response.text}")
    
    # Test 3: Data stats API
    print(f"\n📈 3. Testing Data Stats API...")
    response = requests.get(f'{base_url}/actor/data/stats/', headers=headers, timeout=10)
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"   Success: {result.get('success')}")
        if result.get('success'):
            stats = result.get('stats', {})
            print(f"   Total items: {stats.get('total_items', 0)}")
            print(f"   Platforms used: {stats.get('platforms_used', 0)}")
            print(f"   Accounts used: {stats.get('accounts_used', 0)}")
    else:
        print(f"   ❌ Stats API Error: {response.text}")
    
    # Test 4: Check if there's a pagination issue
    print(f"\n📄 4. Testing Pagination...")
    response = requests.get(f'{base_url}/actor/data/', 
                          headers=headers, 
                          params={'page': 1, 'page_size': 20}, 
                          timeout=10)
    print(f"   Status with pagination: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"   Results with pagination: {len(result.get('results', []))}")
        print(f"   Has next: {result.get('has_next', False)}")
        print(f"   Has previous: {result.get('has_previous', False)}")
    
    # Test 5: Check the exact API endpoint the frontend uses
    print(f"\n🎭 5. Testing Frontend-specific API calls...")
    
    # This is likely what the frontend data page calls
    response = requests.get(f'{base_url}/actor/data/', 
                          headers=headers, 
                          params={
                              'page': 1, 
                              'page_size': 10,
                              'ordering': '-scraped_at'
                          }, 
                          timeout=10)
    print(f"   Status with frontend params: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"   Results with frontend params: {len(result.get('results', []))}")
        
        if result.get('results'):
            print(f"   ✅ Data is available via API!")
            print(f"   The issue might be in the frontend display logic.")
        else:
            print(f"   ❌ No results with frontend parameters")
            print(f"   Full response: {json.dumps(result, indent=2)}")
    else:
        print(f"   ❌ Frontend API Error: {response.text}")
    
    print("\n" + "=" * 50)
    print("🔍 Data API Test Complete!")
    
    # Summary
    db_count = ActorScrapedData.objects.filter(task__user=user).count()
    print(f"\n📋 SUMMARY:")
    print(f"   Database items: {db_count}")
    print(f"   API accessible: {'✅' if response.status_code == 200 else '❌'}")
    
    if db_count > 0 and response.status_code == 200:
        print(f"   🎯 DIAGNOSIS: Data exists in database and API works")
        print(f"   💡 LIKELY ISSUE: Frontend display or filtering logic")
        print(f"   🔧 NEXT STEPS: Check frontend data page component")
    elif db_count > 0:
        print(f"   🎯 DIAGNOSIS: Data exists but API has issues")
        print(f"   💡 LIKELY ISSUE: API endpoint or serialization")
    else:
        print(f"   🎯 DIAGNOSIS: No data in database")
        print(f"   💡 LIKELY ISSUE: Scraping or data saving")

if __name__ == '__main__':
    test_data_api()
