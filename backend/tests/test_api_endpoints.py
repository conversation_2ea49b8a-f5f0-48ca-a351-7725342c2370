#!/usr/bin/env python3
"""
API Endpoints Test
Tests all Actor system API endpoints to ensure they're accessible
"""

import requests
import json
import time
from datetime import datetime

class APIEndpointsTest:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
        print(f"🔗 API Endpoints Test")
        print(f"🌐 Base URL: {base_url}")
        print(f"📅 Started: {datetime.now().isoformat()}")
        print("=" * 50)

    def test_endpoint(self, method, endpoint, data=None, expected_status=200, description=""):
        """Test a single API endpoint"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data)
            elif method.upper() == 'PUT':
                response = self.session.put(url, json=data)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            success = response.status_code == expected_status
            status_icon = "✅" if success else "❌"
            
            result = {
                'endpoint': endpoint,
                'method': method,
                'status_code': response.status_code,
                'expected_status': expected_status,
                'success': success,
                'description': description,
                'response_size': len(response.content),
                'timestamp': datetime.now().isoformat()
            }
            
            self.test_results.append(result)
            
            print(f"{status_icon} {method} {endpoint} - {response.status_code} ({description})")
            
            return result
            
        except requests.exceptions.ConnectionError:
            print(f"❌ {method} {endpoint} - CONNECTION ERROR (Server not running?)")
            result = {
                'endpoint': endpoint,
                'method': method,
                'status_code': 0,
                'expected_status': expected_status,
                'success': False,
                'description': f"{description} - CONNECTION ERROR",
                'error': 'Connection refused',
                'timestamp': datetime.now().isoformat()
            }
            self.test_results.append(result)
            return result
            
        except Exception as e:
            print(f"❌ {method} {endpoint} - ERROR: {str(e)}")
            result = {
                'endpoint': endpoint,
                'method': method,
                'status_code': 0,
                'expected_status': expected_status,
                'success': False,
                'description': f"{description} - ERROR",
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            self.test_results.append(result)
            return result

    def run_all_tests(self):
        """Run all API endpoint tests"""
        
        print("\n🧪 Testing Actor System API Endpoints...")
        print("-" * 50)
        
        # Test basic endpoints
        self.test_endpoint('GET', '/api/actor/health/', description="System health check")
        self.test_endpoint('GET', '/api/actor/platforms/', description="Available platforms")
        
        # Test account endpoints (without authentication - should return 401 or 403)
        self.test_endpoint('GET', '/api/actor/accounts/list/', expected_status=401, description="List accounts (no auth)")
        self.test_endpoint('POST', '/api/actor/accounts/create/', expected_status=401, description="Create account (no auth)")
        
        # Test task endpoints (without authentication)
        self.test_endpoint('GET', '/api/actor/tasks/list/', expected_status=401, description="List tasks (no auth)")
        self.test_endpoint('POST', '/api/actor/tasks/create/', expected_status=401, description="Create task (no auth)")
        
        # Test data endpoints (without authentication)
        self.test_endpoint('GET', '/api/actor/data/', expected_status=401, description="Get scraped data (no auth)")
        self.test_endpoint('GET', '/api/actor/data/stats/', expected_status=401, description="Data statistics (no auth)")
        
        # Test legacy endpoints
        self.test_endpoint('GET', '/api/actor/accounts/', description="Legacy accounts")
        self.test_endpoint('GET', '/api/actor/tasks/', description="Legacy tasks")
        self.test_endpoint('GET', '/api/actor/sessions/', description="Legacy sessions")
        self.test_endpoint('GET', '/api/actor/scraped-data/', description="Legacy scraped data")
        
        # Test authentication endpoints
        self.test_endpoint('POST', '/api/auth/users/', expected_status=400, description="Create user (no data)")
        self.test_endpoint('POST', '/api/auth/jwt/create/', expected_status=400, description="JWT login (no data)")
        
        print("\n" + "=" * 50)
        self.generate_summary()
        self.save_results()

    def generate_summary(self):
        """Generate test summary"""
        total = len(self.test_results)
        passed = sum(1 for r in self.test_results if r['success'])
        failed = total - passed
        
        print(f"📊 API ENDPOINTS TEST SUMMARY")
        print(f"Total Endpoints: {total}")
        print(f"✅ Accessible: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"📈 Success Rate: {(passed/total)*100:.1f}%" if total > 0 else "0%")
        
        if failed > 0:
            print(f"\n⚠️ Failed Endpoints:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   • {result['method']} {result['endpoint']} - {result.get('error', 'Status: ' + str(result['status_code']))}")

    def save_results(self):
        """Save test results to file"""
        results_file = f"tests/api_endpoints_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        summary = {
            'total': len(self.test_results),
            'passed': sum(1 for r in self.test_results if r['success']),
            'failed': sum(1 for r in self.test_results if not r['success']),
            'success_rate': f"{(sum(1 for r in self.test_results if r['success'])/len(self.test_results))*100:.1f}%" if self.test_results else "0%"
        }
        
        output = {
            'timestamp': datetime.now().isoformat(),
            'base_url': self.base_url,
            'summary': summary,
            'results': self.test_results
        }
        
        with open(results_file, 'w') as f:
            json.dump(output, f, indent=2)
        
        print(f"\n💾 Results saved to: {results_file}")

if __name__ == "__main__":
    # Test if server is running first
    try:
        response = requests.get("http://localhost:8000", timeout=5)
        print("🟢 Django server is running")
    except:
        print("🔴 Django server is not running. Please start it first:")
        print("   python3 manage.py runserver")
        exit(1)
    
    test = APIEndpointsTest()
    test.run_all_tests()
