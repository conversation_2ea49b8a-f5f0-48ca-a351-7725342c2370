#!/usr/bin/env python3

"""
Test data stats API
"""

import os
import sys
import django
import requests
import json

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from actor.models import ActorScrapedData, ActorTask, ActorAccount

def test_data_stats_api():
    print("📊 Data Stats API Test")
    print("=" * 50)
    
    # Get test user and token
    user = User.objects.get(username='test_actor_user')
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    headers = {
        'Authorization': f'JWT {access_token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000/api'
    
    print(f"🔑 User: {user.username}")
    
    # 1. Check current scraped data in database
    print("\n📱 1. Checking Database...")
    scraped_data_count = ActorScrapedData.objects.filter(task__user=user).count()
    tasks_count = ActorTask.objects.filter(user=user).count()
    accounts_count = ActorAccount.objects.filter(user=user).count()
    
    print(f"   Scraped data items: {scraped_data_count}")
    print(f"   Tasks: {tasks_count}")
    print(f"   Accounts: {accounts_count}")
    
    # Show some sample data if it exists
    if scraped_data_count > 0:
        sample_data = ActorScrapedData.objects.filter(task__user=user).first()
        print(f"   Sample data: {sample_data.data_type} from {sample_data.platform}")
    
    # 2. Test data stats API
    print(f"\n📊 2. Testing Data Stats API...")
    try:
        response = requests.get(f'{base_url}/actor/data/stats/', headers=headers, timeout=10)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                stats = result.get('stats', {})
                print(f"✅ Data stats retrieved successfully!")
                print(f"   Total items: {stats.get('total_items', 0)}")
                print(f"   Platforms used: {stats.get('platforms_used', 0)}")
                print(f"   Accounts used: {stats.get('accounts_used', 0)}")
                print(f"   Average quality: {stats.get('average_quality_score', 0)}")
                print(f"   Complete items: {stats.get('complete_items', 0)}")
                print(f"   Completion rate: {stats.get('completion_rate', 0)}%")
                
                # Show breakdowns
                platform_breakdown = stats.get('platform_breakdown', {})
                data_type_breakdown = stats.get('data_type_breakdown', {})
                
                print(f"   Platform breakdown: {platform_breakdown}")
                print(f"   Data type breakdown: {data_type_breakdown}")
                
            else:
                print(f"❌ Data stats failed: {result.get('error')}")
        else:
            print(f"❌ Data stats request failed")
            
    except Exception as e:
        print(f"❌ Data stats error: {e}")
    
    # 3. Test scraped data API
    print(f"\n📋 3. Testing Scraped Data API...")
    try:
        response = requests.get(f'{base_url}/actor/data/', headers=headers, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data_items = result.get('results', [])
                print(f"✅ Scraped data retrieved successfully!")
                print(f"   Data items: {len(data_items)}")
                
                # Show sample item if exists
                if data_items:
                    sample = data_items[0]
                    print(f"   Sample item: {sample.get('data_type')} from {sample.get('platform')}")
                    print(f"   Quality score: {sample.get('quality_score', 'N/A')}")
                
            else:
                print(f"❌ Scraped data failed: {result.get('error')}")
        else:
            print(f"❌ Scraped data request failed")
            
    except Exception as e:
        print(f"❌ Scraped data error: {e}")
    
    # 4. Create some test data if none exists
    if scraped_data_count == 0:
        print(f"\n🔧 4. Creating Test Data...")
        try:
            # Get or create account and task
            account = ActorAccount.objects.filter(user=user).first()
            if account:
                task = ActorTask.objects.filter(user=user).first()
                if task:
                    # Create some mock scraped data
                    test_data = [
                        {
                            'task': task,
                            'data_type': 'video',
                            'platform': account.platform,
                            'actor_account': account,
                            'account_username': account.platform_username,
                            'content': {'title': 'Test Video 1', 'views': 1000},
                            'quality_score': 85.5,
                            'is_complete': True
                        },
                        {
                            'task': task,
                            'data_type': 'profile',
                            'platform': account.platform,
                            'actor_account': account,
                            'account_username': account.platform_username,
                            'content': {'username': 'test_user', 'followers': 500},
                            'quality_score': 92.0,
                            'is_complete': True
                        }
                    ]
                    
                    for data in test_data:
                        ActorScrapedData.objects.create(**data)
                    
                    print(f"✅ Created {len(test_data)} test data items")
                    
                    # Re-test the stats API
                    print(f"\n📊 5. Re-testing Stats API with Test Data...")
                    response = requests.get(f'{base_url}/actor/data/stats/', headers=headers, timeout=10)
                    if response.status_code == 200:
                        result = response.json()
                        if result.get('success'):
                            stats = result.get('stats', {})
                            print(f"✅ Updated stats:")
                            print(f"   Total items: {stats.get('total_items', 0)}")
                            print(f"   Platform breakdown: {stats.get('platform_breakdown', {})}")
                            print(f"   Data type breakdown: {stats.get('data_type_breakdown', {})}")
                        
                else:
                    print("❌ No tasks found to create test data")
            else:
                print("❌ No accounts found to create test data")
                
        except Exception as e:
            print(f"❌ Error creating test data: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Data Stats API Test Complete!")

if __name__ == '__main__':
    test_data_stats_api()
