#!/usr/bin/env python3
"""
API Endpoints Test - Fixed Version
Tests all Actor system API endpoints to ensure they're accessible and return correct responses
"""

import requests
import json
import time
from datetime import datetime

class APIEndpointsTestFixed:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
        print(f"🔗 API Endpoints Test - Fixed Version")
        print(f"🌐 Base URL: {base_url}")
        print(f"📅 Started: {datetime.now().isoformat()}")
        print("=" * 60)

    def test_endpoint(self, method, endpoint, expected_status=200, description="", data=None):
        """Test a single API endpoint"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data)
            elif method.upper() == 'PUT':
                response = self.session.put(url, json=data)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            success = response.status_code == expected_status
            status_icon = "✅" if success else "❌"
            
            # Try to parse JSON response
            try:
                response_data = response.json()
                response_preview = str(response_data)[:100] + "..." if len(str(response_data)) > 100 else str(response_data)
            except:
                response_preview = response.text[:100] + "..." if len(response.text) > 100 else response.text
            
            result = {
                'endpoint': endpoint,
                'method': method,
                'status_code': response.status_code,
                'expected_status': expected_status,
                'success': success,
                'description': description,
                'response_preview': response_preview,
                'timestamp': datetime.now().isoformat()
            }
            
            self.test_results.append(result)
            
            print(f"{status_icon} {method} {endpoint} - {response.status_code} ({description})")
            if not success:
                print(f"   Expected: {expected_status}, Got: {response.status_code}")
                print(f"   Response: {response_preview}")
            
            return result
            
        except requests.exceptions.ConnectionError:
            print(f"❌ {method} {endpoint} - CONNECTION ERROR (Server not running?)")
            result = {
                'endpoint': endpoint,
                'method': method,
                'status_code': 0,
                'expected_status': expected_status,
                'success': False,
                'description': f"{description} - CONNECTION ERROR",
                'error': 'Connection refused',
                'timestamp': datetime.now().isoformat()
            }
            self.test_results.append(result)
            return result
            
        except Exception as e:
            print(f"❌ {method} {endpoint} - ERROR: {str(e)}")
            result = {
                'endpoint': endpoint,
                'method': method,
                'status_code': 0,
                'expected_status': expected_status,
                'success': False,
                'description': f"{description} - ERROR",
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            self.test_results.append(result)
            return result

    def run_all_tests(self):
        """Run all API endpoint tests"""
        
        print("\n🧪 Testing Actor System API Endpoints...")
        print("-" * 60)
        
        # Test public endpoints (no authentication required)
        print("\n📖 Public Endpoints:")
        self.test_endpoint('GET', '/api/actor/health/', description="System health check")
        self.test_endpoint('GET', '/api/actor/platforms/', description="Available platforms")
        
        # Test protected endpoints (should return 401 without authentication)
        print("\n🔒 Protected Endpoints (should return 401):")
        self.test_endpoint('GET', '/api/actor/accounts/list/', expected_status=401, description="List accounts (no auth)")
        self.test_endpoint('POST', '/api/actor/accounts/create/', expected_status=401, description="Create account (no auth)")
        self.test_endpoint('POST', '/api/actor/accounts/authenticate/', expected_status=401, description="Authenticate account (no auth)")
        self.test_endpoint('GET', '/api/actor/tasks/list/', expected_status=401, description="List tasks (no auth)")
        self.test_endpoint('POST', '/api/actor/tasks/create/', expected_status=401, description="Create task (no auth)")
        self.test_endpoint('POST', '/api/actor/tasks/execute/', expected_status=401, description="Execute task (no auth)")
        self.test_endpoint('GET', '/api/actor/data/', expected_status=401, description="Get scraped data (no auth)")
        self.test_endpoint('GET', '/api/actor/data/stats/', expected_status=401, description="Data statistics (no auth)")
        
        # Test CRUD endpoints (should return 401 without authentication)
        print("\n🔧 CRUD Endpoints (should return 401):")
        self.test_endpoint('PUT', '/api/actor/accounts/1/', expected_status=401, description="Update account (no auth)")
        self.test_endpoint('DELETE', '/api/actor/accounts/1/delete/', expected_status=401, description="Delete account (no auth)")
        self.test_endpoint('PUT', '/api/actor/tasks/1/', expected_status=401, description="Update task (no auth)")
        self.test_endpoint('DELETE', '/api/actor/tasks/1/delete/', expected_status=401, description="Delete task (no auth)")
        
        # Test legacy endpoints for backward compatibility
        print("\n🔄 Legacy Endpoints:")
        self.test_endpoint('GET', '/api/actor/accounts/', description="Legacy accounts")
        self.test_endpoint('GET', '/api/actor/tasks/', description="Legacy tasks")
        self.test_endpoint('GET', '/api/actor/sessions/', description="Legacy sessions")
        self.test_endpoint('GET', '/api/actor/scraped-data/', description="Legacy scraped data")
        
        # Test authentication endpoints
        print("\n🔐 Authentication Endpoints:")
        self.test_endpoint('POST', '/api/auth/users/', expected_status=400, description="Create user (no data)")
        self.test_endpoint('POST', '/api/auth/jwt/create/', expected_status=400, description="JWT login (no data)")
        
        print("\n" + "=" * 60)
        self.generate_summary()
        self.save_results()

    def generate_summary(self):
        """Generate test summary"""
        total = len(self.test_results)
        passed = sum(1 for r in self.test_results if r['success'])
        failed = total - passed
        
        print(f"📊 API ENDPOINTS TEST SUMMARY")
        print(f"Total Endpoints: {total}")
        print(f"✅ Working Correctly: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"📈 Success Rate: {(passed/total)*100:.1f}%" if total > 0 else "0%")
        
        if failed > 0:
            print(f"\n⚠️ Failed Endpoints:")
            for result in self.test_results:
                if not result['success']:
                    error_msg = result.get('error', f'Status: {result["status_code"]}')
                    print(f"   • {result['method']} {result['endpoint']} - {error_msg}")
        
        # Categorize results
        public_endpoints = [r for r in self.test_results if r['expected_status'] == 200]
        protected_endpoints = [r for r in self.test_results if r['expected_status'] == 401]
        
        public_success = sum(1 for r in public_endpoints if r['success'])
        protected_success = sum(1 for r in protected_endpoints if r['success'])
        
        print(f"\n📋 Detailed Breakdown:")
        print(f"📖 Public Endpoints: {public_success}/{len(public_endpoints)} working")
        print(f"🔒 Protected Endpoints: {protected_success}/{len(protected_endpoints)} properly secured")
        
        if public_success == len(public_endpoints) and protected_success == len(protected_endpoints):
            print(f"\n🎉 ALL ENDPOINTS WORKING CORRECTLY!")
            print(f"✅ Public endpoints accessible")
            print(f"✅ Protected endpoints properly secured")
            print(f"✅ API is ready for frontend integration")
        else:
            print(f"\n⚠️ Some endpoints need attention")

    def save_results(self):
        """Save test results to file"""
        results_file = f"tests/api_endpoints_fixed_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        summary = {
            'total': len(self.test_results),
            'passed': sum(1 for r in self.test_results if r['success']),
            'failed': sum(1 for r in self.test_results if not r['success']),
            'success_rate': f"{(sum(1 for r in self.test_results if r['success'])/len(self.test_results))*100:.1f}%" if self.test_results else "0%"
        }
        
        output = {
            'timestamp': datetime.now().isoformat(),
            'base_url': self.base_url,
            'summary': summary,
            'results': self.test_results
        }
        
        with open(results_file, 'w') as f:
            json.dump(output, f, indent=2)
        
        print(f"\n💾 Results saved to: {results_file}")

if __name__ == "__main__":
    # Test if server is running first
    try:
        response = requests.get("http://localhost:8000/api/actor/health/", timeout=5)
        print("🟢 Django server is running")
    except:
        print("🔴 Django server is not running. Please start it first:")
        print("   cd backend && source venv/bin/activate && python3 manage.py runserver")
        exit(1)
    
    test = APIEndpointsTestFixed()
    test.run_all_tests()
    
    # Return appropriate exit code
    success_rate = sum(1 for r in test.test_results if r['success']) / len(test.test_results) * 100
    exit(0 if success_rate >= 90 else 1)
