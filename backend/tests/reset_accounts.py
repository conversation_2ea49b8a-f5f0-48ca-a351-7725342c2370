#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor_tiktok.models import TikTokUserAccount
from django.utils import timezone

def reset_blocked_accounts():
    """Reset blocked TikTok accounts"""
    print("Checking TikTok accounts...")
    
    accounts = TikTokUserAccount.objects.all()
    current_time = timezone.now()
    
    for account in accounts:
        print(f"\nAccount: {account.tiktok_username}")
        print(f"  Active: {account.is_active}")
        print(f"  Blocked: {account.is_blocked}")
        print(f"  Login attempts: {account.login_attempts}")
        print(f"  Last login: {account.last_login}")
        print(f"  Blocked until: {account.blocked_until}")
        
        # Reset if block period has expired
        if account.is_blocked and account.blocked_until:
            if account.blocked_until < current_time:
                print(f"  -> Unblocking {account.tiktok_username} (block period expired)")
                account.is_blocked = False
                account.blocked_until = None
                account.login_attempts = 0
                account.save()
            else:
                remaining_time = (account.blocked_until - current_time).total_seconds() / 60
                print(f"  -> Still blocked for {remaining_time:.1f} more minutes")
        
        # Reset high login attempts for non-blocked accounts
        elif account.login_attempts > 5 and not account.is_blocked:
            print(f"  -> Resetting login attempts for {account.tiktok_username}")
            account.login_attempts = 0
            account.save()

def check_login_issues():
    """Check for common login issues"""
    print("\n=== Login Issue Analysis ===")
    
    accounts = TikTokUserAccount.objects.all()
    
    issues_found = []
    
    for account in accounts:
        if account.is_blocked:
            issues_found.append(f"Account '{account.tiktok_username}' is blocked")
        
        if account.login_attempts >= 5:
            issues_found.append(f"Account '{account.tiktok_username}' has {account.login_attempts} failed login attempts")
        
        if not account.is_session_valid():
            issues_found.append(f"Account '{account.tiktok_username}' has invalid session")
        
        if account.last_login is None:
            issues_found.append(f"Account '{account.tiktok_username}' has never logged in successfully")
    
    if issues_found:
        print("Issues found:")
        for issue in issues_found:
            print(f"  - {issue}")
    else:
        print("No obvious issues found with accounts.")
    
    print("\n=== Recommendations ===")
    print("1. Check if TikTok credentials are correct")
    print("2. Verify TikTok hasn't implemented new anti-bot measures")
    print("3. Check if IP is blocked by TikTok")
    print("4. Try using different browser user agents")
    print("5. Consider using residential proxies")
    print("6. Check Celery worker status")

if __name__ == '__main__':
    reset_blocked_accounts()
    check_login_issues()