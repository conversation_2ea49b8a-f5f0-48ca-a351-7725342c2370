#!/usr/bin/env python3
"""
TikTok Login Debug Test

Test TikTok authentication with real accounts and debug login issues.
"""

import os
import sys
import django
from django.conf import settings

# Add the backend directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/fullstax/backend')

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from actor.models import ActorAccount
from actor.services.actor_service import ActorService
from actor.engines.tiktok_engine import TikTokEngine
from actor.utils.simple_tiktok_auth import SimpleTikTokAuthenticator

def test_existing_accounts():
    """Test existing TikTok accounts"""
    print("\n=== Testing Existing TikTok Accounts ===")
    
    try:
        # Get all TikTok accounts
        tiktok_accounts = ActorAccount.objects.filter(platform='tiktok', is_active=True)
        print(f"Found {tiktok_accounts.count()} active TikTok accounts")
        
        for i, account in enumerate(tiktok_accounts[:3]):  # Test first 3
            print(f"\nAccount {i+1}: @{account.platform_username}")
            print(f"  - User: {account.user.username}")
            print(f"  - Active: {account.is_active}")
            print(f"  - Last Login: {account.last_login}")
            print(f"  - Session Valid: {account.is_session_valid()}")
            print(f"  - Blocked: {account.is_blocked}")
            
            # Check if password is available
            try:
                password = account.get_decrypted_password()
                if password:
                    print(f"  - Password: Available ({'*' * len(password)})")
                    
                    # Test authentication with this account
                    print(f"  - Testing authentication...")
                    
                    actor_service = ActorService()
                    result = actor_service.authenticate_account(account.id)
                    
                    if result.get('success'):
                        print(f"  ✅ Authentication successful!")
                    else:
                        print(f"  ❌ Authentication failed: {result.get('error')}")
                        
                else:
                    print(f"  - Password: Not available")
                    
            except Exception as e:
                print(f"  - Password error: {e}")
                
    except Exception as e:
        print(f"❌ Error testing existing accounts: {e}")
        import traceback
        traceback.print_exc()

def test_simple_authenticator_with_real_account():
    """Test SimpleTikTokAuthenticator with a real account"""
    print("\n=== Testing SimpleTikTokAuthenticator with Real Account ===")
    
    try:
        # Get first TikTok account with password
        account = ActorAccount.objects.filter(
            platform='tiktok', 
            is_active=True
        ).first()
        
        if not account:
            print("❌ No TikTok accounts found")
            return
            
        password = account.get_decrypted_password()
        if not password:
            print(f"❌ No password available for @{account.platform_username}")
            return
            
        print(f"Testing login for @{account.platform_username}")
        
        # Test direct authenticator
        auth = SimpleTikTokAuthenticator()
        result = auth.login(account.platform_username, password)
        
        if result.get('success'):
            print("✅ Direct authenticator login successful!")
            print(f"   Session info keys: {list(result.get('session_info', {}).keys())}")
        else:
            print(f"❌ Direct authenticator login failed: {result.get('error')}")
            print(f"   Current URL: {result.get('current_url')}")
            
    except Exception as e:
        print(f"❌ Error testing simple authenticator: {e}")
        import traceback
        traceback.print_exc()

def test_tiktok_engine_authenticate():
    """Test TikTokEngine authenticate method"""
    print("\n=== Testing TikTokEngine Authenticate Method ===")
    
    try:
        # Get first TikTok account
        account = ActorAccount.objects.filter(
            platform='tiktok', 
            is_active=True
        ).first()
        
        if not account:
            print("❌ No TikTok accounts found")
            return
            
        password = account.get_decrypted_password()
        if not password:
            print(f"❌ No password available for @{account.platform_username}")
            return
            
        print(f"Testing TikTokEngine authenticate for @{account.platform_username}")
        
        # Test TikTokEngine
        engine = TikTokEngine()
        credentials = {
            'username': account.platform_username,
            'password': password
        }
        
        result = engine.authenticate(account, credentials)
        
        if result.get('success'):
            print("✅ TikTokEngine authenticate successful!")
            print(f"   Message: {result.get('message')}")
            print(f"   Account ID: {result.get('account_id')}")
        else:
            print(f"❌ TikTokEngine authenticate failed: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Error testing TikTokEngine authenticate: {e}")
        import traceback
        traceback.print_exc()

def test_task_creation():
    """Test task creation for TikTok account"""
    print("\n=== Testing Task Creation ===")
    
    try:
        # Get first TikTok account
        account = ActorAccount.objects.filter(
            platform='tiktok', 
            is_active=True
        ).first()
        
        if not account:
            print("❌ No TikTok accounts found")
            return
            
        print(f"Testing task creation for @{account.platform_username}")
        
        # Test task creation
        actor_service = ActorService()
        result = actor_service.create_task(
            user=account.user,
            account_id=account.id,
            task_type='CONTENT_SEARCH',
            task_name='Test TikTok Search',
            keywords=['test'],
            max_items=10
        )
        
        if result.get('success'):
            print("✅ Task creation successful!")
            print(f"   Task ID: {result.get('task_id')}")
            print(f"   Task Name: {result.get('task_name')}")
        else:
            print(f"❌ Task creation failed: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Error testing task creation: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🔍 TikTok Login Debug Test")
    print("=" * 50)
    
    test_existing_accounts()
    test_simple_authenticator_with_real_account()
    test_tiktok_engine_authenticate()
    test_task_creation()
    
    print("\n=== Debug Test Summary ===")
    print("Tests completed. Check above for any errors.")
    print("\nCommon issues and solutions:")
    print("1. No valid credentials: Add TikTok accounts with passwords in admin")
    print("2. TikTok login selectors outdated: Update CSS selectors")
    print("3. Account blocked: Check is_blocked field and blocked_until")
    print("4. CAPTCHA/anti-bot: TikTok may be detecting automation")
    print("5. Session expired: Re-authenticate the account")

if __name__ == '__main__':
    main()