{"timestamp": "2025-07-19T06:36:10.340121", "base_url": "http://localhost:8000", "summary": {"total": 20, "passed": 20, "failed": 0, "success_rate": "100.0%"}, "results": [{"endpoint": "/api/actor/health/", "method": "GET", "status_code": 200, "expected_status": 200, "success": true, "description": "System health check", "response_preview": "{'success': True, 'status': 'healthy', 'message': 'All systems operational', 'components': {'databas...", "timestamp": "2025-07-19T06:36:10.178286"}, {"endpoint": "/api/actor/platforms/", "method": "GET", "status_code": 200, "expected_status": 200, "success": true, "description": "Available platforms", "response_preview": "{'success': True, 'platforms': ['tiktok', 'instagram', 'facebook', 'twitter', 'youtube']}", "timestamp": "2025-07-19T06:36:10.189562"}, {"endpoint": "/api/actor/accounts/list/", "method": "GET", "status_code": 401, "expected_status": 401, "success": true, "description": "List accounts (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:36:10.206858"}, {"endpoint": "/api/actor/accounts/create/", "method": "POST", "status_code": 401, "expected_status": 401, "success": true, "description": "Create account (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:36:10.210847"}, {"endpoint": "/api/actor/accounts/authenticate/", "method": "POST", "status_code": 401, "expected_status": 401, "success": true, "description": "Authenticate account (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:36:10.224563"}, {"endpoint": "/api/actor/tasks/list/", "method": "GET", "status_code": 401, "expected_status": 401, "success": true, "description": "List tasks (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:36:10.228108"}, {"endpoint": "/api/actor/tasks/create/", "method": "POST", "status_code": 401, "expected_status": 401, "success": true, "description": "Create task (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:36:10.240902"}, {"endpoint": "/api/actor/tasks/execute/", "method": "POST", "status_code": 401, "expected_status": 401, "success": true, "description": "Execute task (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:36:10.244534"}, {"endpoint": "/api/actor/data/", "method": "GET", "status_code": 401, "expected_status": 401, "success": true, "description": "Get scraped data (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:36:10.255668"}, {"endpoint": "/api/actor/data/stats/", "method": "GET", "status_code": 401, "expected_status": 401, "success": true, "description": "Data statistics (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:36:10.260426"}, {"endpoint": "/api/actor/accounts/1/", "method": "PUT", "status_code": 401, "expected_status": 401, "success": true, "description": "Update account (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:36:10.268642"}, {"endpoint": "/api/actor/accounts/1/delete/", "method": "DELETE", "status_code": 401, "expected_status": 401, "success": true, "description": "Delete account (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:36:10.275856"}, {"endpoint": "/api/actor/tasks/1/", "method": "PUT", "status_code": 401, "expected_status": 401, "success": true, "description": "Update task (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:36:10.280255"}, {"endpoint": "/api/actor/tasks/1/delete/", "method": "DELETE", "status_code": 401, "expected_status": 401, "success": true, "description": "Delete task (no auth)", "response_preview": "{'detail': 'Authentication credentials were not provided.'}", "timestamp": "2025-07-19T06:36:10.291401"}, {"endpoint": "/api/actor/accounts/", "method": "GET", "status_code": 200, "expected_status": 200, "success": true, "description": "Legacy accounts", "response_preview": "{'success': True, 'results': [{'id': 1, 'username': 'grafisone', 'is_active': True, 'last_login': '2...", "timestamp": "2025-07-19T06:36:10.294768"}, {"endpoint": "/api/actor/tasks/", "method": "GET", "status_code": 200, "expected_status": 200, "success": true, "description": "Legacy tasks", "response_preview": "{'success': True, 'results': [{'id': 1, 'task_type': 'content_search', 'status': 'completed', 'progr...", "timestamp": "2025-07-19T06:36:10.305095"}, {"endpoint": "/api/actor/sessions/", "method": "GET", "status_code": 200, "expected_status": 200, "success": true, "description": "Legacy sessions", "response_preview": "{'success': True, 'results': [{'id': 1, 'account': {'id': 1, 'user_id': 1, 'username': 'grafisone', ...", "timestamp": "2025-07-19T06:36:10.308121"}, {"endpoint": "/api/actor/scraped-data/", "method": "GET", "status_code": 200, "expected_status": 200, "success": true, "description": "Legacy scraped data", "response_preview": "{'success': True, 'results': [], 'count': 0, 'total_pages': 1, 'current_page': 1, 'next': None, 'pre...", "timestamp": "2025-07-19T06:36:10.319779"}, {"endpoint": "/api/auth/users/", "method": "POST", "status_code": 400, "expected_status": 400, "success": true, "description": "Create user (no data)", "response_preview": "{'username': ['This field is required.'], 'password': ['This field is required.'], 're_password': ['...", "timestamp": "2025-07-19T06:36:10.325332"}, {"endpoint": "/api/auth/jwt/create/", "method": "POST", "status_code": 400, "expected_status": 400, "success": true, "description": "JWT login (no data)", "response_preview": "{'username': ['This field is required.'], 'password': ['This field is required.']}", "timestamp": "2025-07-19T06:36:10.339888"}]}