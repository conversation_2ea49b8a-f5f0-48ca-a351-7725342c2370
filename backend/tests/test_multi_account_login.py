#!/usr/bin/env python
"""
Test script for multi-account login functionality
"""

import requests
import json
import time

# API endpoint
BASE_URL = "http://127.0.0.1:8000"
MULTI_LOGIN_URL = f"{BASE_URL}/api/actor/multi-login/"

# Test data
test_accounts = [
    {
        "username": "<EMAIL>",
        "email": "<EMAIL>",
        "password": "test_password123"
    },
    {
        "username": "<EMAIL>", 
        "email": "<EMAIL>",
        "password": "test_password456"
    }
]

def test_multi_account_login():
    """
    Test the multi-account login API endpoint
    """
    print("Testing Multi-Account Login API...")
    print(f"URL: {MULTI_LOGIN_URL}")
    
    # Test data
    payload = {
        "accounts": test_accounts,
        "headless": True
    }
    
    try:
        # Make POST request to initiate multi-account login
        print("\n1. Initiating multi-account login...")
        response = requests.post(
            MULTI_LOGIN_URL,
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 202:  # Accepted
            response_data = response.json()
            task_id = response_data.get('task_id')
            
            if task_id:
                print(f"\n2. Task initiated successfully. Task ID: {task_id}")
                
                # Test GET request to check task status
                print("\n3. Checking task status...")
                status_response = requests.get(f"{MULTI_LOGIN_URL}?task_id={task_id}")
                print(f"Status Check Response: {status_response.text}")
                
            else:
                print("ERROR: No task_id returned")
        else:
            print(f"ERROR: Unexpected status code {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("ERROR: Could not connect to Django server. Make sure it's running on http://127.0.0.1:8000")
    except Exception as e:
        print(f"ERROR: {str(e)}")

def test_endpoint_availability():
    """
    Test if the endpoint is available
    """
    print("Testing endpoint availability...")
    
    try:
        # Test GET request without task_id (should return error)
        response = requests.get(MULTI_LOGIN_URL)
        print(f"GET Status Code: {response.status_code}")
        print(f"GET Response: {response.text}")
        
        # Test POST request with invalid data
        response = requests.post(
            MULTI_LOGIN_URL,
            json={"invalid": "data"},
            headers={"Content-Type": "application/json"}
        )
        print(f"\nPOST (invalid) Status Code: {response.status_code}")
        print(f"POST (invalid) Response: {response.text}")
        
    except Exception as e:
        print(f"ERROR: {str(e)}")

if __name__ == "__main__":
    print("=" * 50)
    print("Multi-Account Login API Test")
    print("=" * 50)
    
    # Test endpoint availability first
    test_endpoint_availability()
    
    print("\n" + "=" * 50)
    
    # Test actual functionality
    test_multi_account_login()
    
    print("\n" + "=" * 50)
    print("Test completed!")