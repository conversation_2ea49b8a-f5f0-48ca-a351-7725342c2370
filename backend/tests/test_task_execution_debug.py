#!/usr/bin/env python3

"""
Debug task execution issues
"""

import os
import sys
import django
import requests
import json

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from actor.models import ActorTask, ActorAccount

def debug_task_execution():
    print("🚀 Task Execution Debug")
    print("=" * 50)
    
    # Get test user and token
    user = User.objects.get(username='test_actor_user')
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    headers = {
        'Authorization': f'JWT {access_token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000/api'
    
    print(f"🔑 User: {user.username} (ID: {user.id})")
    
    # 1. Get available tasks
    print("\n📋 1. Getting Available Tasks...")
    tasks = ActorTask.objects.filter(user=user, status='PENDING')
    print(f"   Found {tasks.count()} pending tasks")
    
    if not tasks.exists():
        print("❌ No pending tasks found. Creating a test task...")
        
        # Create a test task
        account = ActorAccount.objects.filter(user=user).first()
        if not account:
            print("❌ No accounts found. Please create an account first.")
            return
        
        task = ActorTask.objects.create(
            user=user,
            actor_account=account,
            platform=account.platform,
            task_name='Debug Execution Test',
            task_type='CONTENT_SEARCH',
            keywords='debug test',
            max_items=5
        )
        print(f"✅ Created test task: {task.id}")
        tasks = [task]
    else:
        tasks = list(tasks)
    
    task = tasks[0]
    print(f"   Using task: {task.task_name} (ID: {task.id})")
    print(f"   Task type: {task.task_type}")
    print(f"   Platform: {task.platform}")
    print(f"   Account: @{task.actor_account.platform_username}")
    
    # 2. Check account session status
    print(f"\n🔐 2. Checking Account Session...")
    account = task.actor_account
    print(f"   Account ID: {account.id}")
    print(f"   Platform: {account.platform}")
    print(f"   Username: @{account.platform_username}")
    print(f"   Last login: {account.last_login}")
    print(f"   Session expires: {account.session_expires_at}")
    
    # Check if session data exists
    try:
        session_data = account.decrypt_session_data()
        print(f"   Session data exists: {bool(session_data)}")
        if session_data:
            print(f"   Session keys: {list(session_data.keys())}")
    except Exception as e:
        print(f"   Session data error: {e}")
    
    # 3. Test task execution via API
    print(f"\n🚀 3. Testing Task Execution via API...")
    execution_data = {
        'task_id': task.id
    }
    
    print(f"   Execution data: {json.dumps(execution_data, indent=2)}")
    
    try:
        response = requests.post(
            f'{base_url}/actor/tasks/execute/', 
            headers=headers, 
            json=execution_data,
            timeout=30
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Task execution started successfully!")
                print(f"   Message: {result.get('message')}")
            else:
                print(f"❌ Task execution failed: {result.get('error')}")
        else:
            try:
                error_data = response.json()
                print(f"❌ API request failed: {error_data}")
            except:
                print(f"❌ API request failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # 4. Check task status after execution attempt
    print(f"\n📊 4. Checking Task Status After Execution...")
    task.refresh_from_db()
    print(f"   Status: {task.status}")
    print(f"   Started at: {task.started_at}")
    print(f"   Progress: {task.progress_percentage}%")
    
    print("\n" + "=" * 50)
    print("🎉 Debug Complete!")

if __name__ == '__main__':
    debug_task_execution()
