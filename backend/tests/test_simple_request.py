#!/usr/bin/env python3
import requests
import time

def test_tiktok_access():
    """Test basic TikTok access without Selenium"""
    print("Testing basic TikTok access...")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    
    try:
        # Test basic TikTok access
        response = requests.get('https://www.tiktok.com', headers=headers, timeout=10)
        print(f"TikTok main page status: {response.status_code}")
        print(f"Response length: {len(response.text)} characters")
        
        # Test search page
        search_url = 'https://www.tiktok.com/search?q=prabowo'
        response = requests.get(search_url, headers=headers, timeout=10)
        print(f"Search page status: {response.status_code}")
        print(f"Search response length: {len(response.text)} characters")
        
        # Check if we get blocked
        if 'blocked' in response.text.lower() or 'captcha' in response.text.lower():
            print("WARNING: Possible blocking detected")
        else:
            print("Basic access seems to work")
            
        # Look for some basic content indicators
        if 'data-e2e' in response.text:
            print("Found data-e2e attributes in response")
        else:
            print("No data-e2e attributes found - might be dynamic content")
            
    except Exception as e:
        print(f"Request failed: {e}")

if __name__ == '__main__':
    test_tiktok_access()