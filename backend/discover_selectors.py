#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to discover current TikTok login page selectors
"""

import os
import sys
import django
import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

class SelectorDiscovery:
    def __init__(self):
        self.driver = None
        self.discovered_selectors = {
            'login_methods': [],
            'form_elements': [],
            'buttons': [],
            'links': []
        }
        
    def setup_driver(self):
        """Setup Chrome driver"""
        try:
            print("🔧 Setting up Chrome driver...")
            
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✅ Driver setup completed")
            return True
            
        except Exception as e:
            print(f"❌ Driver setup failed: {str(e)}")
            return False
    
    def navigate_and_prepare(self):
        """Navigate to login page and handle popups"""
        try:
            print("\n🌐 Navigating to TikTok login page...")
            self.driver.get('https://www.tiktok.com/login')
            time.sleep(5)
            
            # Handle basic popups
            self.handle_basic_popups()
            
            print(f"📍 Current URL: {self.driver.current_url}")
            return True
            
        except Exception as e:
            print(f"❌ Navigation failed: {str(e)}")
            return False
    
    def handle_basic_popups(self):
        """Handle common popups"""
        try:
            # Cookie consent
            cookie_selectors = [
                'button[data-testid="cookie-accept"]',
                'button[id*="cookie"]',
                'button[class*="cookie"]',
                '//button[contains(text(), "Accept")]',
                '//button[contains(text(), "OK")]'
            ]
            
            for selector in cookie_selectors:
                try:
                    if selector.startswith('//'):
                        element = self.driver.find_element(By.XPATH, selector)
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    if element.is_displayed():
                        element.click()
                        time.sleep(1)
                        break
                except:
                    continue
            
            # App download modal
            modal_selectors = [
                'button[data-testid="modal-close"]',
                'button[aria-label*="close"]',
                '.modal-close',
                '//button[contains(@aria-label, "close")]'
            ]
            
            for selector in modal_selectors:
                try:
                    if selector.startswith('//'):
                        element = self.driver.find_element(By.XPATH, selector)
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    if element.is_displayed():
                        element.click()
                        time.sleep(1)
                        break
                except:
                    continue
                    
        except Exception as e:
            print(f"⚠️ Popup handling: {str(e)}")
    
    def discover_clickable_elements(self):
        """Discover all clickable elements that might be login methods"""
        try:
            print("\n🔍 Discovering clickable elements...")
            
            # Find all clickable elements
            clickable_selectors = [
                'div[role="button"]',
                'div[role="link"]', 
                'button',
                'a[href*="login"]',
                'a[href*="email"]',
                'a[href*="phone"]',
                '[data-testid*="login"]',
                '[data-e2e*="login"]',
                '[data-testid*="email"]',
                '[data-e2e*="email"]',
                '[tabindex="0"]'
            ]
            
            all_elements = []
            
            for selector in clickable_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            try:
                                element_info = {
                                    'tag': elem.tag_name,
                                    'text': elem.text.strip()[:100],
                                    'class': elem.get_attribute('class'),
                                    'id': elem.get_attribute('id'),
                                    'data-testid': elem.get_attribute('data-testid'),
                                    'data-e2e': elem.get_attribute('data-e2e'),
                                    'role': elem.get_attribute('role'),
                                    'href': elem.get_attribute('href'),
                                    'tabindex': elem.get_attribute('tabindex'),
                                    'selector_used': selector
                                }
                                
                                # Filter for potentially relevant elements
                                text_lower = element_info['text'].lower()
                                if any(keyword in text_lower for keyword in ['phone', 'email', 'username', 'login', 'sign', 'continue']):
                                    all_elements.append(element_info)
                                elif element_info['href'] and any(keyword in element_info['href'].lower() for keyword in ['login', 'email', 'phone']):
                                    all_elements.append(element_info)
                                elif element_info['data-testid'] and any(keyword in element_info['data-testid'].lower() for keyword in ['login', 'email', 'phone']):
                                    all_elements.append(element_info)
                                elif element_info['data-e2e'] and any(keyword in element_info['data-e2e'].lower() for keyword in ['login', 'email', 'phone']):
                                    all_elements.append(element_info)
                                    
                            except Exception as e:
                                continue
                                
                except Exception as e:
                    continue
            
            # Remove duplicates based on text and class
            unique_elements = []
            seen = set()
            
            for elem in all_elements:
                key = (elem['text'], elem['class'], elem['href'])
                if key not in seen:
                    seen.add(key)
                    unique_elements.append(elem)
            
            print(f"\n📋 Found {len(unique_elements)} potentially relevant clickable elements:")
            
            for i, elem in enumerate(unique_elements):
                print(f"\n[{i+1}] {elem['tag'].upper()}")
                if elem['text']:
                    print(f"    Text: '{elem['text']}'")
                if elem['class']:
                    print(f"    Class: '{elem['class']}'")
                if elem['id']:
                    print(f"    ID: '{elem['id']}'")
                if elem['data-testid']:
                    print(f"    data-testid: '{elem['data-testid']}'")
                if elem['data-e2e']:
                    print(f"    data-e2e: '{elem['data-e2e']}'")
                if elem['role']:
                    print(f"    Role: '{elem['role']}'")
                if elem['href']:
                    print(f"    Href: '{elem['href']}'")
                if elem['tabindex']:
                    print(f"    Tabindex: '{elem['tabindex']}'")
                print(f"    Found by: {elem['selector_used']}")
            
            self.discovered_selectors['login_methods'] = unique_elements
            return unique_elements
            
        except Exception as e:
            print(f"❌ Element discovery failed: {str(e)}")
            return []
    
    def discover_form_elements(self):
        """Discover form input elements"""
        try:
            print("\n📝 Discovering form elements...")
            
            form_elements = []
            
            # Find all input elements
            inputs = self.driver.find_elements(By.TAG_NAME, 'input')
            
            for inp in inputs:
                if inp.is_displayed():
                    try:
                        element_info = {
                            'tag': inp.tag_name,
                            'type': inp.get_attribute('type'),
                            'name': inp.get_attribute('name'),
                            'placeholder': inp.get_attribute('placeholder'),
                            'class': inp.get_attribute('class'),
                            'id': inp.get_attribute('id'),
                            'autocomplete': inp.get_attribute('autocomplete'),
                            'data-testid': inp.get_attribute('data-testid'),
                            'data-e2e': inp.get_attribute('data-e2e')
                        }
                        form_elements.append(element_info)
                    except:
                        continue
            
            print(f"\n📋 Found {len(form_elements)} form elements:")
            
            for i, elem in enumerate(form_elements):
                print(f"\n[{i+1}] INPUT")
                for key, value in elem.items():
                    if value:
                        print(f"    {key}: '{value}'")
            
            self.discovered_selectors['form_elements'] = form_elements
            return form_elements
            
        except Exception as e:
            print(f"❌ Form element discovery failed: {str(e)}")
            return []
    
    def generate_updated_selectors(self):
        """Generate updated selectors based on discoveries"""
        try:
            print("\n🔧 Generating updated selectors...")
            
            login_methods = self.discovered_selectors['login_methods']
            form_elements = self.discovered_selectors['form_elements']
            
            # Generate CSS selectors for login methods
            css_selectors = []
            xpath_selectors = []
            
            for elem in login_methods:
                # Generate CSS selector
                css_parts = []
                if elem['tag']:
                    css_parts.append(elem['tag'])
                
                if elem['class']:
                    # Use exact class match
                    classes = elem['class'].split()
                    if classes:
                        css_parts.append(f'[class="{elem["class"]}"')
                
                if elem['data-testid']:
                    css_parts.append(f'[data-testid="{elem["data-testid"]}"')
                
                if elem['data-e2e']:
                    css_parts.append(f'[data-e2e="{elem["data-e2e"]}"')
                
                if elem['role']:
                    css_parts.append(f'[role="{elem["role"]}"')
                
                if elem['href']:
                    css_parts.append(f'[href="{elem["href"]}"')
                
                if css_parts:
                    css_selector = ''.join(css_parts)
                    css_selectors.append(css_selector)
                
                # Generate XPath selector for text
                if elem['text']:
                    xpath_selector = f"//{elem['tag']}[contains(text(), '{elem['text'][:20]}')]"
                    xpath_selectors.append(xpath_selector)
            
            # Generate selectors for form elements
            form_selectors = []
            for elem in form_elements:
                css_parts = ['input']
                
                if elem['type']:
                    css_parts.append(f'[type="{elem["type"]}"')
                
                if elem['name']:
                    css_parts.append(f'[name="{elem["name"]}"')
                
                if elem['placeholder']:
                    css_parts.append(f'[placeholder="{elem["placeholder"]}"')
                
                if css_parts:
                    form_selector = ''.join(css_parts)
                    form_selectors.append(form_selector)
            
            print("\n📋 Generated CSS selectors for login methods:")
            for i, selector in enumerate(css_selectors[:10]):  # Limit output
                print(f"   [{i+1}] {selector}")
            
            print("\n📋 Generated XPath selectors for login methods:")
            for i, selector in enumerate(xpath_selectors[:10]):  # Limit output
                print(f"   [{i+1}] {selector}")
            
            print("\n📋 Generated selectors for form elements:")
            for i, selector in enumerate(form_selectors[:10]):  # Limit output
                print(f"   [{i+1}] {selector}")
            
            # Save to file
            output = {
                'discovered_elements': self.discovered_selectors,
                'generated_selectors': {
                    'login_methods_css': css_selectors,
                    'login_methods_xpath': xpath_selectors,
                    'form_elements': form_selectors
                }
            }
            
            with open('/tmp/tiktok_selectors.json', 'w') as f:
                json.dump(output, f, indent=2)
            
            print("\n💾 Detailed results saved to: /tmp/tiktok_selectors.json")
            
            return output
            
        except Exception as e:
            print(f"❌ Selector generation failed: {str(e)}")
            return None
    
    def run_discovery(self):
        """Run the complete discovery process"""
        try:
            print("🚀 Starting TikTok Selector Discovery")
            print("=" * 50)
            
            if not self.setup_driver():
                return False
            
            if not self.navigate_and_prepare():
                return False
            
            # Take screenshot
            self.driver.save_screenshot('/tmp/tiktok_discovery.png')
            print("📸 Screenshot saved: /tmp/tiktok_discovery.png")
            
            clickable_elements = self.discover_clickable_elements()
            form_elements = self.discover_form_elements()
            
            if clickable_elements or form_elements:
                self.generate_updated_selectors()
                print("\n✅ Discovery completed successfully!")
                print("\n💡 Next steps:")
                print("   1. Review /tmp/tiktok_selectors.json for detailed results")
                print("   2. Update TikTokAuthenticator selectors with discovered elements")
                print("   3. Test the new selectors")
            else:
                print("\n❌ No relevant elements discovered")
                print("\n💡 This might indicate:")
                print("   1. Page structure has changed significantly")
                print("   2. JavaScript is required to load login elements")
                print("   3. Region-specific login page variations")
            
            return True
            
        except Exception as e:
            print(f"❌ Discovery process failed: {str(e)}")
            return False
        finally:
            if self.driver:
                self.driver.quit()

if __name__ == "__main__":
    discovery = SelectorDiscovery()
    discovery.run_discovery()