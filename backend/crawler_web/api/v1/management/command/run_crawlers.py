from django.core.management.base import BaseCommand
from django.utils import timezone
from backend.crawlers.api.v1.storage.models import CrawlerSchedule
from v1.tasks import execute_crawler_task
import concurrent.futures
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Run scheduled crawlers with parallel execution'
    
    def add_arguments(self, parser):
        parser.add_argument('--max-workers', type=int, default=5, help='Max parallel tasks')
        parser.add_argument('--limit', type=int, default=100, help='Max schedules to process')
    
    def handle(self, *args, **options):
        now = timezone.now()
        schedules = CrawlerSchedule.objects.filter(
            next_run__lte=now,
            is_active=True,
            start_time__lte=now,
            end_time__gte=now
        ).select_related('project')[:options['limit']]
        
        self.stdout.write(f"Found {len(schedules)} schedules to run")
        
        # Parallel execution
        with concurrent.futures.ThreadPoolExecutor(
            max_workers=options['max_workers']
        ) as executor:
            futures = {
                executor.submit(execute_crawler_task.delay, schedule.id): schedule
                for schedule in schedules
            }
            
            for future in concurrent.futures.as_completed(futures):
                schedule = futures[future]
                try:
                    future.result()
                    self.stdout.write(f"Started task for schedule {schedule.id}")
                except Exception as e:
                    self.stderr.write(f"Error for schedule {schedule.id}: {str(e)}")