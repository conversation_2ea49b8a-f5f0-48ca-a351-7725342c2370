from rest_framework import serializers
from .storage.models import Project, CrawlerSchedule, CrawlerTask, CrawledData

class DynamicFieldsSerializer(serializers.ModelSerializer):
    def __init__(self, *args, **kwargs):
        fields = kwargs.pop('fields', None)
        super().__init__(*args, **kwargs)
        
        if fields is not None:
            allowed = set(fields)
            existing = set(self.fields)
            for field_name in existing - allowed:
                self.fields.pop(field_name)

class ProjectSerializer(DynamicFieldsSerializer):
    platform_names = serializers.SerializerMethodField()
    url = serializers.URLField(required=False, allow_blank=True, allow_null=True)
    
    class Meta:
        model = Project
        fields = '__all__'
    
    def get_platform_names(self, obj):
        return [dict(Project.PLATFORMS)[p] for p in obj.platforms]

class ScheduleSerializer(DynamicFieldsSerializer):
    next_run = serializers.DateTimeField(read_only=True)
    project = serializers.PrimaryKeyRelatedField(queryset=Project.objects.all(), write_only=True)
    project_obj = ProjectSerializer(source='project', read_only=True)
    
    class Meta:
        model = CrawlerSchedule
        fields = '__all__'
        extra_fields = ['project_obj']

    def to_representation(self, instance):
        rep = super().to_representation(instance)
        # Add project_obj as 'project' for frontend compatibility
        if 'project_obj' in rep:
            rep['project'] = rep['project_obj']
            del rep['project_obj']
        return rep

class TaskSerializer(DynamicFieldsSerializer):
    schedule_project = serializers.CharField(source='schedule.project.name', read_only=True)
    crawled_data_count = serializers.IntegerField(read_only=True)
    error_message = serializers.CharField(read_only=True)
    duration = serializers.SerializerMethodField()

    class Meta:
        model = CrawlerTask
        fields = '__all__'
        read_only_fields = ('started_at', 'finished_at')

    def get_duration(self, obj):
        if obj.started_at and obj.finished_at:
            delta = obj.finished_at - obj.started_at
            return str(delta)
        return None

class TaskDetailSerializer(TaskSerializer):
    crawled_data_count = serializers.IntegerField(read_only=True)
    error_preview = serializers.CharField(source='error_message', read_only=True)
    
    class Meta(TaskSerializer.Meta):
        fields = '__all__'

class CrawledDataSerializer(DynamicFieldsSerializer):
    compression_ratio = serializers.FloatField(read_only=True)
    domain = serializers.CharField(read_only=True)
    
    class Meta:
        model = CrawledData
        fields = '__all__'
        read_only_fields = ('compressed_size', 'original_size')