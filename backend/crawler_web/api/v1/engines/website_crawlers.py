import zlib
import re
import time
from bs4 import BeautifulSoup, XMLParsedAsHTMLWarning
from urllib.parse import urljoin, urlparse, urldefrag
import requests
import warnings
from .base_crawlers import BaseCrawler
from ..storage.models import CrawledData
from django.db import transaction
from django.utils import timezone
from concurrent.futures import ThreadPoolExecutor, as_completed
from ..utils.user_agents import get_random_user_agent

warnings.filterwarnings("ignore", category=XMLParsedAsHTMLWarning)

class WebsiteCrawler(BaseCrawler):
    MAX_DEPTH = 2
    MAX_URLS = 100
    TIMEOUT = 60  # Increased from 15 to 30 seconds
   # USER_AGENT = "Mozilla/5.0 (compatible; CrawlerBot/1.0; +http://yourdomain.com/bot)"
    USER_AGENT = {'User-Agent': get_random_user_agent()}

    def __init__(self, task):
        super().__init__(task)
        self.base_url = self.get_project_base_url()
        self.visited_urls = set()
        self.session = self._create_session()
        self.url_queue = []
        self.discovered_sitemaps = []
        self.discovered_feeds = []

    def _create_session(self):
        session = requests.Session()
        session.headers.update({'User-Agent': self.USER_AGENT})
        session.max_redirects = 3
        return session

    def get_project_base_url(self):
        # Prefer explicit url field if set
        url = getattr(self.task.schedule.project, 'url', None)
        if url:
            return url
        # Fallback to keywords as before
        keywords = self.task.schedule.project.keywords.split(',')
        if keywords:
            domain = keywords[0].strip()
            return domain if domain.startswith('http') else f"https://{domain}"
        return ""

    def is_same_domain(self, url):
        return urlparse(url).netloc == urlparse(self.base_url).netloc

    def discover_resources(self):
        """Multi-strategy discovery with caching"""
        self._discover_robots_txt()
        self._discover_meta_links()
        return self.discovered_sitemaps + self.discovered_feeds

    def _discover_robots_txt(self):
        try:
            response = self.session.get(urljoin(self.base_url, "/robots.txt"), timeout=5)
            for line in response.text.splitlines():
                if line.lower().startswith("sitemap:"):
                    sitemap_url = line.split(":", 1)[1].strip()
                    self.discovered_sitemaps.append(sitemap_url)
        except:
            pass

    def _discover_meta_links(self):
        try:
            response = self.session.get(self.base_url, timeout=10)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find RSS/Atom feeds
            for link in soup.find_all('link', {'type': ['application/rss+xml', 'application/atom+xml']}):
                if href := link.get('href'):
                    self.discovered_feeds.append(urljoin(self.base_url, href))
            
            # Find sitemaps
            for link in soup.find_all('a', href=re.compile(r'.*sitemap.*\.xml$')):
                if href := link.get('href'):
                    self.discovered_sitemaps.append(urljoin(self.base_url, href))
        except:
            pass

    def parse_sitemap(self, sitemap_url):
        try:
            response = self.session.get(sitemap_url, timeout=10)
            content = response.content
            
            # Try parsing as XML sitemap
            if b'<sitemapindex' in content:
                return self._parse_sitemap_index(content)
            elif b'<urlset' in content:
                return self._parse_urlset(content)
        except Exception as e:
            self.log(f"Sitemap error: {str(e)}", 'error')
        return []

    def _parse_sitemap_index(self, content):
        soup = BeautifulSoup(content, 'xml')
        urls = []
        for sitemap in soup.find_all('sitemap'):
            if loc := sitemap.find('loc'):
                urls.extend(self.parse_sitemap(loc.text))
        return urls

    def _parse_urlset(self, content):
        soup = BeautifulSoup(content, 'xml')
        # Clean up URLs from sitemap
        return [loc.text.strip().rstrip('%20').rstrip() for loc in soup.find_all('loc') if loc.text]

    def parse_feed(self, feed_url):
        """Parse RSS/Atom feed and return a set of cleaned URLs."""
        urls = set()
        try:
            response = self.session.get(feed_url, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'xml')
            for item in soup.find_all(['item', 'entry']):
                link = item.find('link')
                href = None
                if link:
                    # Atom feeds may use href attribute
                    href = link.get('href') or link.text
                else:
                    # Some feeds use <link> as text
                    href = item.find('link')
                if href:
                    clean_url = urljoin(feed_url, href.strip())
                    # Remove whitespace and trailing %20 or similar
                    clean_url = clean_url.strip().rstrip('%20').rstrip()
                    if clean_url:
                        urls.add(clean_url)
        except Exception as e:
            self.log(f"Feed parse error: {str(e)}", 'error')
        return urls

    def crawl_url(self, url):
        try:
            # Normalize URL and check depth
            url = urldefrag(url)[0].strip().rstrip('%20').rstrip()
            if not url or url in self.visited_urls or len(self.visited_urls) >= self.MAX_URLS:
                return None, 0, 0
            self.visited_urls.add(url)
            response = self.session.get(url, timeout=self.TIMEOUT, stream=True)
            response.raise_for_status()
            
            # Stream content to avoid large memory usage
            content = b''
            for chunk in response.iter_content(chunk_size=8192):
                content += chunk
                if len(content) > 50 * 1024 * 1024:  # 50MB limit
                    raise ValueError("Content too large")
            
            return content, len(content), response
        except Exception as e:
            self.log(f"Error crawling {url}: {str(e)}", 'error')
            return None, 0, None

    def process_content(self, content, response, url):
        """Process and store content with optimization"""
        # Compress with adaptive strategy
        compressed = zlib.compress(content, level=9)
        compressed_size = len(compressed)
        
        # Create CrawledData instance
        return CrawledData(
            task=self.task,
            url=url,
            raw_data=compressed,
            content_type=response.headers.get('Content-Type', ''),
            original_size=len(content),
            compressed_size=compressed_size,
            domain=urlparse(url).netloc,
            metadata={
                'status_code': response.status_code,
                'encoding': response.encoding,
                'headers': dict(response.headers),
            }
        )

    def run_concurrent_crawling(self, urls):
        """Threaded crawling with rate limiting"""
        crawled_data = []
        with ThreadPoolExecutor(max_workers=10) as executor:
            future_to_url = {
                executor.submit(self.crawl_url, url): url 
                for url in urls[:self.MAX_URLS]
            }
            
            for future in as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    content, original_size, response = future.result()
                    if content and response:
                        crawled_data.append(
                            self.process_content(content, response, url)
                        )
                except Exception as e:
                    self.log(f"Thread error for {url}: {str(e)}", 'error')
                
                # Respect politeness delay
                time.sleep(0.5)
        
        return crawled_data

    def run(self):
        self.task.status = 'running'
        self.task.save()
        
        try:
            # Phase 1: Discover resources
            resources = self.discover_resources()
            self.log(f"Discovered {len(resources)} resources")
            
            # Phase 2: Extract URLs
            all_urls = set()
            for resource in resources:
                if 'sitemap' in resource:
                    all_urls.update(self.parse_sitemap(resource))
                else:
                    all_urls.update(self.parse_feed(resource))
            
            # Phase 3: Concurrent crawling
            unique_urls = list(all_urls)[:self.MAX_URLS]
            self.log(f"Starting crawling for {len(unique_urls)} URLs")
            
            crawled_objects = self.run_concurrent_crawling(unique_urls)
            
            # Bulk create with transaction
            with transaction.atomic():
                CrawledData.objects.bulk_create(crawled_objects, batch_size=100)
            
            self.task.status = 'completed'
            self.log(f"Crawled {len(crawled_objects)} URLs successfully")
        except Exception as e:
            self.task.status = 'failed'
            self.task.error_message = str(e)
            self.log(f"Crawling failed: {str(e)}", 'error')
        finally:
            self.task.finished_at = timezone.now()
            self.task.save()