import logging
import requests

from requests.adapters import HTTPAdapter

from urllib3.util.retry import Retry

logger = logging.getLogger(__name__)

class BaseCrawler:

	def __init__(self, task):
		self.task = task
		self.session = self._create_session()

	def _create_session(self):
		session = requests.Session()
		retry = Retry(
			total=3,
			backoff_factor=0.5,
			status_forcelist=[500, 502, 503, 504],
			allowed_methods=["GET", "POST"]
		)
		adapter = HTTPAdapter(max_retries=retry, pool_connections=100, pool_maxsize=100)
		session.mount('http://', adapter)
		session.mount('https://', adapter)
		return session

	def log(self, message, level='info'):
		log_message = f"[Task {self.task.id}] {message}"
		if level == 'error':
			logger.error(log_message)
		else:
			logger.info(log_message)
		# Simpan ke task log
		self.task.logs += f"\n{log_message}"
		self.task.save(update_fields=['logs'])

	def run(self):
		raise NotImplementedError("Subclasses must implement run method")