import zlib
import lzma
import bz2
from django.db import models
from django.db.models import F, Func

class CompressionManager(models.Manager):
    COMPRESSION_THRESHOLD = 1024 * 1024  # 1MB
    
    def compress_data(self, data):
        """Adaptive compression based on content type"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        # Use different algorithms based on size
        if len(data) > self.COMPRESSION_THRESHOLD:
            # Best compression for large data
            compressed = lzma.compress(data, preset=9)
            algo = 'lzma'
        else:
            # Faster compression for small data
            compressed = zlib.compress(data, level=6)
            algo = 'zlib'
        
        return compressed, algo, len(data), len(compressed)

class ArchiveFunc(Func):
    function = 'PG_COMPRESS'
    template = "%(function)s(%(expressions)s, 'lz4')"

class DecompressFunc(Func):
    function = 'PG_DECOMPRESS'
    template = "%(function)s(%(expressions)s)"