import zlib
from django.db import models
from django.utils import timezone
from django.core.validators import MinLengthValidator
from django.contrib.postgres.indexes import GinIndex
from django.contrib.postgres.search import SearchVectorField
from django.db.models.signals import post_save
from django.dispatch import receiver

class Project(models.Model):
    PLATFORMS = [
        ('TW', 'Twitter'),
        ('FB', 'Facebook'),
        ('IG', 'Instagram'),
        ('TT', 'TikTok'),
        ('RS', 'RSS'),
        ('WB', 'Website'),
    ]
    
    name = models.CharField(max_length=255, validators=[MinLengthValidator(3)])
    keywords = models.TextField(help_text="Kata kunci dipisahkan koma")
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    platforms = models.JSONField(default=list)
    url = models.URLField(max_length=1000, blank=True, null=True, help_text="Target website or RSS feed URL")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['start_date', 'end_date']),
            models.Index(fields=['platforms'], name='platforms_idx'),
        ]

class CrawlerSchedule(models.Model):
    FREQUENCY_CHOICES = [
        ('minute', 'Per Minute'),
        ('hour', 'Per Hour'),
        ('day', 'Per Day'),
    ]
    
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='schedules', db_index=True)
    frequency = models.CharField(max_length=10, choices=FREQUENCY_CHOICES)
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    next_run = models.DateTimeField(null=True, blank=True, db_index=True)
    is_active = models.BooleanField(default=True, db_index=True)

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # Remove update_celery_beat from here (will use signal)

    class Meta:
        indexes = [
            models.Index(fields=['next_run', 'is_active']),
        ]

class CrawlerTask(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    
    schedule = models.ForeignKey(CrawlerSchedule, on_delete=models.CASCADE, related_name='tasks', db_index=True)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='pending', db_index=True)
    started_at = models.DateTimeField(null=True, blank=True)
    finished_at = models.DateTimeField(null=True, blank=True)
    logs = models.TextField(blank=True)
    error_message = models.TextField(blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['started_at']),
        ]

class CrawledData(models.Model):
    task = models.ForeignKey(CrawlerTask, on_delete=models.CASCADE, related_name='crawled_data', db_index=True)
    url = models.URLField(max_length=1000, db_index=True)
    raw_data = models.BinaryField()
    content_type = models.CharField(max_length=100, null=True, blank=True)
    original_size = models.PositiveIntegerField()
    compressed_size = models.PositiveIntegerField()
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    domain = models.CharField(max_length=255, db_index=True)
    metadata = models.JSONField(default=dict)
    search_vector = SearchVectorField(null=True)

    class Meta:
        indexes = [
            models.Index(fields=['url']),
            models.Index(fields=['domain']),
            models.Index(fields=['created_at']),
            GinIndex(fields=['search_vector']),
        ]

    def compress_data(self, data):
        """Optimized compression with adaptive strategy"""
        compressed = zlib.compress(data, level=9)
        return compressed, len(compressed)

    def save(self, *args, **kwargs):
        from urllib.parse import urlparse
        # Optimize storage
        if not self.compressed_size:
            self.raw_data, self.compressed_size = self.compress_data(self.raw_data)
        
        # Extract domain for faster filtering
        parsed = urlparse(self.url)
        self.domain = parsed.netloc
        
        super().save(*args, **kwargs)

# Signal to update celery beat after save
@receiver(post_save, sender=CrawlerSchedule)
def update_celery_beat_after_save(sender, instance, **kwargs):
    from ..tasks import update_celery_beat
    update_celery_beat(instance)