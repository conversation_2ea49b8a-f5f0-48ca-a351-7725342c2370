from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from .storage.models import Project, CrawlerSchedule, CrawlerTask, CrawledData
from .serializers import (
    ProjectSerializer,
    ScheduleSerializer,
    TaskSerializer,
    CrawledDataSerializer,
    TaskDetailSerializer
)
from django.db.models import Count, F, Sum, Q, Avg
from django.utils import timezone
from bs4 import BeautifulSoup
from rest_framework import filters
from concurrent.futures import ThreadPoolExecutor
from .engines.website_crawlers import WebsiteCrawler

class CustomPagination(PageNumberPagination):
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 1000

class ProjectViewSet(viewsets.ModelViewSet):
    queryset = Project.objects.annotate(
        active_schedules=Count('schedules', filter=Q(schedules__is_active=True)),
        total_tasks=Count('schedules__tasks')
    ).order_by('-created_at')
    serializer_class = ProjectSerializer
    pagination_class = CustomPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = []
    search_fields = ['name', 'keywords']

class ScheduleViewSet(viewsets.ModelViewSet):
    queryset = CrawlerSchedule.objects.all()
    serializer_class = ScheduleSerializer
    pagination_class = CustomPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    search_fields = ['project__name']

    def get_queryset(self):
        project_id = self.request.query_params.get('project_id')
        qs = CrawlerSchedule.objects.all()
        if project_id:
            qs = qs.filter(project_id=project_id)
        return qs.annotate(
            success_tasks=Count('tasks', filter=Q(tasks__status='completed')),
            failed_tasks=Count('tasks', filter=Q(tasks__status='failed'))
        ).select_related('project').order_by('-start_time')

    @action(detail=True, methods=['post'])
    def run_now(self, request, pk=None):
        schedule = self.get_object()
        # Create a new task for this schedule
        task = CrawlerTask.objects.create(schedule=schedule, status='pending')

        def run_crawler(task):
            project = schedule.project
            # For now, only handle Website/RSS
            if 'WB' in project.platforms or 'RS' in project.platforms:
                crawler = WebsiteCrawler(task)
                crawler.run()
            else:
                task.status = 'failed'
                task.error_message = 'No crawler implemented for this platform.'
                task.save()

        # Run the crawler in a background thread
        executor = ThreadPoolExecutor(max_workers=1)
        executor.submit(run_crawler, task)

        serializer = TaskSerializer(task)
        return Response(serializer.data, status=201)

    def update(self, request, *args, **kwargs):
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Schedule update data: {request.data}")
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        logger.info(f"Old start_time: {instance.start_time}, Old end_time: {instance.end_time}")
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        # Explicitly update start_time if present
        if 'start_time' in serializer.validated_data:
            logger.info(f"New start_time: {serializer.validated_data['start_time']}")
            instance.start_time = serializer.validated_data['start_time']
        if 'end_time' in serializer.validated_data:
            logger.info(f"New end_time: {serializer.validated_data['end_time']}")
            instance.end_time = serializer.validated_data['end_time']
        if 'frequency' in serializer.validated_data:
            instance.frequency = serializer.validated_data['frequency']
        if 'project' in serializer.validated_data:
            instance.project = serializer.validated_data['project']
        instance.save()
        logger.info(f"Saved start_time: {instance.start_time}, Saved end_time: {instance.end_time}")
        return Response(self.get_serializer(instance).data)

class TaskViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = CrawlerTask.objects.all()
    serializer_class = TaskSerializer
    pagination_class = CustomPagination

    def get_queryset(self):
        schedule_id = self.kwargs.get('schedule_pk')
        project_id = self.request.query_params.get('project')
        qs = CrawlerTask.objects.all().select_related('schedule__project')
        if schedule_id:
            qs = qs.filter(schedule_id=schedule_id)
        if project_id:
            qs = qs.filter(schedule__project_id=project_id)
        # Annotate with crawled_data_count for frontend
        from django.db.models import Count
        return qs.annotate(crawled_data_count=Count('crawled_data'))

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return TaskDetailSerializer
        return TaskSerializer

class CrawledDataViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = CrawledDataSerializer
    pagination_class = CustomPagination

    def get_queryset(self):
        # Support filtering by project_id or task_pk
        project_id = self.request.query_params.get('project_id')
        task_pk = self.kwargs.get('task_pk')
        qs = CrawledData.objects.all()
        if project_id:
            qs = qs.filter(task__schedule__project_id=project_id)
        elif task_pk:
            qs = qs.filter(task_id=task_pk)
        return qs.only(
            'url', 'domain', 'content_type', 'original_size', 
            'compressed_size', 'created_at'
        ).order_by('-created_at')

class MonitoringViewSet(viewsets.ViewSet):
    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        # Real-time stats with single query
        stats = CrawlerTask.objects.aggregate(
            total=Count('id'),
            running=Count('id', filter=Q(status='running')),
            pending=Count('id', filter=Q(status='pending')),
            completed=Count('id', filter=Q(status='completed')),
            failed=Count('id', filter=Q(status='failed')),
            avg_duration=Avg(F('finished_at') - F('started_at'), filter=Q(status='completed'))
        )
        
        # Storage stats
        storage = CrawledData.objects.aggregate(
            total_size=Sum('compressed_size'),
            avg_ratio=Avg(F('compressed_size') / F('original_size'))
        )
        
        return Response({
            'task_stats': stats,
            'storage_stats': storage
        })
    
    @action(detail=False, methods=['get'])
    def active_tasks(self, request):
        tasks = CrawlerTask.objects.filter(
            status='running'
        ).values(
            'id', 'schedule__project__name', 'started_at'
        ).order_by('-started_at')[:20]
        
        return Response(tasks)
    
    @action(detail=True, methods=['get'], url_path='content-preview')
    def content_preview(self, request, pk=None):
        """Decompress and show text preview"""
        from zlib import decompress
        
        try:
            data = CrawledData.objects.get(id=pk)
            decompressed = decompress(data.raw_data)
            
            # Extract text content (simplified)
            soup = BeautifulSoup(decompressed, 'html.parser')
            text = soup.get_text(separator=' ', strip=True)[:5000] + '...'
            
            return Response({
                'url': data.url,
                'preview': text
            })
        except Exception as e:
            return Response({'error': str(e)}, status=400)
    
    @action(detail=False, methods=['get'])
    def platform_stats(self, request):
        # Aggregate stats by platform
        from django.db.models import Avg, Count
        platforms = {}
        projects = Project.objects.all()
        for project in projects:
            for platform in project.platforms:
                if platform not in platforms:
                    platforms[platform] = {'tasks': 0, 'avg_size': 0, 'total_size': 0, 'count': 0}
                # Count tasks for this platform
                task_qs = CrawlerTask.objects.filter(schedule__project=project)
                platforms[platform]['tasks'] += task_qs.count()
                # Average size of crawled data for this platform
                crawled_qs = CrawledData.objects.filter(task__schedule__project=project)
                avg_size = crawled_qs.aggregate(avg=Avg('compressed_size'))['avg'] or 0
                total_size = crawled_qs.aggregate(total=Sum('compressed_size'))['total'] or 0
                platforms[platform]['avg_size'] += avg_size
                platforms[platform]['total_size'] += total_size
                platforms[platform]['count'] += 1
        # Normalize avg_size
        for p in platforms:
            if platforms[p]['count'] > 0:
                platforms[p]['avg_size'] = platforms[p]['avg_size'] / platforms[p]['count']
        return Response({'platforms': platforms})
    
    @action(detail=False, methods=['get'])
    def task_history(self, request):
        from django.utils import timezone
        from django.db.models.functions import TruncDate
        import datetime
        today = timezone.now().date()
        week_ago = today - datetime.timedelta(days=6)
        qs = CrawlerTask.objects.filter(started_at__date__gte=week_ago)
        data = (
            qs.annotate(day=TruncDate('started_at'))
            .values('day')
            .annotate(tasks=Count('id'))
            .order_by('day')
        )
        # Fill missing days
        result = []
        for i in range(7):
            day = week_ago + datetime.timedelta(days=i)
            found = next((d for d in data if d['day'] == day), None)
            result.append({'date': str(day), 'tasks': found['tasks'] if found else 0})
        return Response(result)