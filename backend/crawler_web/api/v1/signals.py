from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.db import transaction
from .storage.models import CrawlerSchedule, CrawledData
from .tasks import update_celery_beat
import logging

logger = logging.getLogger(__name__)

@receiver(post_save, sender=CrawlerSchedule)
def schedule_updated(sender, instance, created, **kwargs):
    """Update Celery Beat on schedule changes"""
    transaction.on_commit(
        lambda: (
            update_celery_beat(instance),
            logger.info(f"Schedule {instance.id} updated in Celery Beat")
        )
    )

@receiver(pre_save, sender=CrawledData)
def preprocess_crawled_data(sender, instance, **kwargs):
    """Preprocessing before saving"""
    if not instance.original_size:
        instance.original_size = len(instance.raw_data)
    
    # Extract domain
    from urllib.parse import urlparse
    parsed = urlparse(instance.url)
    instance.domain = parsed.netloc
    
    # Extract metadata
    if not instance.metadata:
        instance.metadata = {
            'protocol': parsed.scheme,
            'path_depth': parsed.path.count('/')
        }