from django.urls import path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views
from .views import CrawledDataViewSet

router = DefaultRouter()
router.register(r'projects', views.ProjectViewSet)
router.register(r'schedules', views.ScheduleViewSet)
router.register(r'tasks', views.TaskViewSet)
router.register(r'tasks/(?P<task_id>\d+)/data', views.CrawledDataViewSet, basename='task-data')
router.register(r'monitoring', views.MonitoringViewSet, basename='monitoring')

# Explicit monitoring action routes
monitoring_dashboard = views.MonitoringViewSet.as_view({'get': 'dashboard'})
monitoring_active_tasks = views.MonitoringViewSet.as_view({'get': 'active_tasks'})
monitoring_platform_stats = views.MonitoringViewSet.as_view({'get': 'platform_stats'})
monitoring_task_history = views.MonitoringViewSet.as_view({'get': 'task_history'})

# Add a direct route for project crawled data
project_crawled_data = CrawledDataViewSet.as_view({'get': 'list'})

# Add a direct route for crawled data preview
crawled_data_preview = CrawledDataViewSet.as_view({'get': 'content_preview'})

urlpatterns = router.urls + [
    path('monitoring/dashboard/', monitoring_dashboard, name='monitoring-dashboard'),
    path('monitoring/active-tasks/', monitoring_active_tasks, name='monitoring-active-tasks'),
    path('monitoring/platform-stats/', monitoring_platform_stats, name='monitoring-platform-stats'),
    path('monitoring/task-history/', monitoring_task_history, name='monitoring-task-history'),
    path('crawled-data/', project_crawled_data, name='project-crawled-data'),
    path('crawled-data/<int:pk>/preview/', views.MonitoringViewSet.as_view({'get': 'content_preview'}), name='crawled-data-preview'),
]