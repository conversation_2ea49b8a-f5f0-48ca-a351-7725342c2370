from celery import shared_task
from celery.exceptions import MaxRetriesExceededError
from django.utils import timezone
from datetime import timedelta
from django.db import transaction
from .storage.models import CrawlerSchedule, CrawlerTask
from .engines import WebsiteCrawler, TwitterCrawler
import logging
from django_celery_beat.models import IntervalSchedule, PeriodicTask


logger = logging.getLogger(__name__)

@shared_task(
    bind=True, 
    autoretry_for=(Exception,), 
    retry_backoff=60, 
    retry_backoff_max=600,
    max_retries=3,
    soft_time_limit=300,
    time_limit=360
)
def execute_crawler_task(self, schedule_id):
    try:
        with transaction.atomic():
            schedule = CrawlerSchedule.objects.select_for_update().get(id=schedule_id)
            # Try to find a pending task for this schedule
            task = CrawlerTask.objects.filter(schedule=schedule, status='pending', started_at__isnull=True).order_by('id').first()
            if task:
                task.status = 'running'
                task.started_at = timezone.now()
                task.save()
            else:
                # Create task with lock if not found
                task = CrawlerTask.objects.create(
                    schedule=schedule,
                    status='running',
                    started_at=timezone.now()
                )
            # Execute platform-specific crawlers
            platforms = schedule.project.platforms
            if 'WB' in platforms or 'RS' in platforms:
                WebsiteCrawler(task).run()
            if 'TW' in platforms:
                TwitterCrawler(task).run()
            # Update schedule
            self.update_schedule_next_run(schedule_id)
            
        return f"Task {task.id} completed"
    
    except Exception as e:
        logger.error(f"Task failed: {str(e)}")
        
        # Update task status if created
        if 'task' in locals():
            task.status = 'failed'
            task.error_message = str(e)
            task.finished_at = timezone.now()
            task.save()
        
        # Retry logic
        try:
            self.retry(exc=e)
        except MaxRetriesExceededError:
            logger.critical(f"Max retries exceeded for schedule {schedule_id}")
            return f"Permanent failure: {str(e)}"

def update_schedule_next_run(schedule_id):
    schedule = CrawlerSchedule.objects.get(id=schedule_id)
    now = timezone.now()
    
    if schedule.end_time > now:
        if schedule.frequency == 'minute':
            next_run = now + timedelta(minutes=1)
        elif schedule.frequency == 'hour':
            next_run = now + timedelta(hours=1)
        elif schedule.frequency == 'day':
            next_run = now + timedelta(days=1)
        
        schedule.next_run = next_run
        schedule.save()
        
        # Update Celery Beat schedule
        update_celery_beat(schedule)

def update_celery_beat(schedule):
    # Create or update periodic task
    schedule_name = f"crawl_schedule_{schedule.id}"
    
    # Create interval schedule
    if schedule.frequency == 'minute':
        interval, _ = IntervalSchedule.objects.get_or_create(
            every=1,
            period=IntervalSchedule.MINUTES
        )
    elif schedule.frequency == 'hour':
        interval, _ = IntervalSchedule.objects.get_or_create(
            every=1,
            period=IntervalSchedule.HOURS
        )
    else:
        interval, _ = IntervalSchedule.objects.get_or_create(
            every=1,
            period=IntervalSchedule.DAYS
        )
    
    # Create or update periodic task
    PeriodicTask.objects.update_or_create(
        name=schedule_name,
        defaults={
            'interval': interval,
            'task': 'crawlers.api.v1.tasks.execute_crawler_task',
            'args': f'[{schedule.id}]',
            'enabled': schedule.is_active,
            'start_time': schedule.start_time,
            'expires': schedule.end_time
        }
    )