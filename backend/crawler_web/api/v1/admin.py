from django.contrib import admin
from .storage.models import Project, CrawlerSchedule, CrawlerTask, CrawledData
from django.utils.html import format_html
import zlib
from bs4 import BeautifulSoup

@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ('name', 'platforms_list', 'start_date', 'end_date', 'schedule_count')
    list_filter = ('platforms',)
    search_fields = ('name', 'keywords')
    
    def platforms_list(self, obj):
        return ", ".join([dict(Project.PLATFORMS)[p] for p in obj.platforms])
    platforms_list.short_description = 'Platforms'
    
    def schedule_count(self, obj):
        return obj.schedules.count()

@admin.register(CrawlerSchedule)
class ScheduleAdmin(admin.ModelAdmin):
    list_display = ('project', 'frequency', 'is_active', 'next_run', 'task_stats')
    list_filter = ('frequency', 'is_active')
    readonly_fields = ('next_run',)
    
    def task_stats(self, obj):
        return f"C: {obj.tasks.completed_count()}, F: {obj.tasks.failed_count()}"
    task_stats.short_description = 'Tasks'

@admin.register(CrawlerTask)
class TaskAdmin(admin.ModelAdmin):
    list_display = ('schedule', 'status', 'started_at', 'duration', 'data_count')
    list_filter = ('status', 'schedule__project')
    readonly_fields = ('logs_preview',)
    
    def duration(self, obj):
        if obj.finished_at and obj.started_at:
            return obj.finished_at - obj.started_at
        return None
    
    def data_count(self, obj):
        return obj.crawled_data.count()
    
    def logs_preview(self, obj):
        return format_html('<pre>{}</pre>', obj.logs[:2000])

@admin.register(CrawledData)
class CrawledDataAdmin(admin.ModelAdmin):
    list_display = ('url', 'domain', 'compression_ratio', 'created_at')
    list_filter = ('domain', 'content_type')
    readonly_fields = ('content_preview',)
    search_fields = ('url', 'domain')
    
    def compression_ratio(self, obj):
        return f"{obj.compressed_size/obj.original_size:.2%}"
    
    def content_preview(self, obj):
        try:
            decompressed = zlib.decompress(obj.raw_data)
            text = BeautifulSoup(decompressed, 'html.parser').get_text()[:500]
            return format_html('<pre>{}</pre>', text)
        except:
            return "Unable to decompress"