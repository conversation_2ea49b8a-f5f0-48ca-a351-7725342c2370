from django.apps import AppConfig
from django.db.models.signals import post_migrate

def init_celery_beat(sender, **kwargs):
    """Initialize Celery Beat schedules after migration"""
    from .storage.models import CrawlerSchedule
    from .tasks import update_celery_beat
    
    for schedule in CrawlerSchedule.objects.filter(is_active=True):
        update_celery_beat(schedule)

class CrawlersConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'crawlers'
    
    def ready(self):
        # Connect signals
        from . import signals
        post_migrate.connect(init_celery_beat, sender=self)