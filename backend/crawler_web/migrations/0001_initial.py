# Generated by Django 5.2.3 on 2025-06-20 06:52

import django.contrib.postgres.indexes
import django.contrib.postgres.search
import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CrawlerSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('frequency', models.CharField(choices=[('minute', 'Per Minute'), ('hour', 'Per Hour'), ('day', 'Per Day')], max_length=10)),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField()),
                ('next_run', models.DateTimeField(blank=True, db_index=True, null=True)),
                ('is_active', models.BooleanField(db_index=True, default=True)),
            ],
        ),
        migrations.CreateModel(
            name='CrawlerTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed')], db_index=True, default='pending', max_length=10)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('finished_at', models.DateTimeField(blank=True, null=True)),
                ('logs', models.TextField(blank=True)),
                ('error_message', models.TextField(blank=True)),
                ('schedule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='crawlers.crawlerschedule')),
            ],
        ),
        migrations.CreateModel(
            name='CrawledData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.URLField(db_index=True, max_length=1000)),
                ('raw_data', models.BinaryField()),
                ('content_type', models.CharField(blank=True, max_length=100, null=True)),
                ('original_size', models.PositiveIntegerField()),
                ('compressed_size', models.PositiveIntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('domain', models.CharField(db_index=True, max_length=255)),
                ('metadata', models.JSONField(default=dict)),
                ('search_vector', django.contrib.postgres.search.SearchVectorField(null=True)),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='crawled_data', to='crawlers.crawlertask')),
            ],
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, validators=[django.core.validators.MinLengthValidator(3)])),
                ('keywords', models.TextField(help_text='Kata kunci dipisahkan koma')),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
                ('platforms', models.JSONField(default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'indexes': [models.Index(fields=['start_date', 'end_date'], name='crawlers_pr_start_d_8f7610_idx'), models.Index(fields=['platforms'], name='platforms_idx')],
            },
        ),
        migrations.AddField(
            model_name='crawlerschedule',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='crawlers.project'),
        ),
        migrations.AddIndex(
            model_name='crawlertask',
            index=models.Index(fields=['status'], name='crawlers_cr_status_f7171f_idx'),
        ),
        migrations.AddIndex(
            model_name='crawlertask',
            index=models.Index(fields=['started_at'], name='crawlers_cr_started_ddc5ea_idx'),
        ),
        migrations.AddIndex(
            model_name='crawleddata',
            index=models.Index(fields=['url'], name='crawlers_cr_url_ecc652_idx'),
        ),
        migrations.AddIndex(
            model_name='crawleddata',
            index=models.Index(fields=['domain'], name='crawlers_cr_domain_fa4309_idx'),
        ),
        migrations.AddIndex(
            model_name='crawleddata',
            index=models.Index(fields=['created_at'], name='crawlers_cr_created_c177eb_idx'),
        ),
        migrations.AddIndex(
            model_name='crawleddata',
            index=django.contrib.postgres.indexes.GinIndex(fields=['search_vector'], name='crawlers_cr_search__731d9e_gin'),
        ),
        migrations.AddIndex(
            model_name='crawlerschedule',
            index=models.Index(fields=['next_run', 'is_active'], name='crawlers_cr_next_ru_beea42_idx'),
        ),
    ]
