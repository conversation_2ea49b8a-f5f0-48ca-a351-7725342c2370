from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    # New Actor System endpoints
    get_available_platforms, create_actor_account, authenticate_actor_account,
    get_actor_accounts, get_actor_account_details, update_actor_account, delete_actor_account,
    create_actor_task, execute_actor_task, get_actor_tasks, update_actor_task, delete_actor_task,
    migrate_tiktok_account, get_actor_scraped_data, get_data_labeling_stats,
    create_actor_scraped_data, update_actor_scraped_data, delete_actor_scraped_data, bulk_delete_actor_scraped_data,
    # Existing TikTok endpoints (backward compatibility)
    simple_login_test, system_status, scrape_content_by_keyword,
    get_search_history, get_search_presets,
    get_health_status, get_accounts, get_tasks, get_task_statistics,
    get_sessions, get_scraped_data, get_scraped_data_by_task,
    get_active_sessions, get_content_stats
)

urlpatterns = [
    # ============================================================================
    # NEW ACTOR SYSTEM ENDPOINTS
    # ============================================================================

    # Platform management
    path('api/actor/platforms/', get_available_platforms, name='available-platforms'),

    # Account management (new Actor system)
    path('api/actor/accounts/create/', create_actor_account, name='create-actor-account'),
    path('api/actor/accounts/authenticate/', authenticate_actor_account, name='authenticate-actor-account'),
    path('api/actor/accounts/list/', get_actor_accounts, name='get-actor-accounts'),
    path('api/actor/accounts/migrate-tiktok/', migrate_tiktok_account, name='migrate-tiktok-account'),
    path('api/actor/accounts/<int:account_id>/', update_actor_account, name='update-actor-account'),
    path('api/actor/accounts/<int:account_id>/details/', get_actor_account_details, name='get-actor-account-details'),
    path('api/actor/accounts/<int:account_id>/delete/', delete_actor_account, name='delete-actor-account'),

    # Task management (new Actor system)
    path('api/actor/tasks/create/', create_actor_task, name='create-actor-task'),
    path('api/actor/tasks/execute/', execute_actor_task, name='execute-actor-task'),
    path('api/actor/tasks/list/', get_actor_tasks, name='get-actor-tasks'),
    path('api/actor/tasks/<int:task_id>/', update_actor_task, name='update-actor-task'),
    path('api/actor/tasks/<int:task_id>/delete/', delete_actor_task, name='delete-actor-task'),

    # Data management (new Actor system)
    path('api/actor/data/', get_actor_scraped_data, name='get-actor-scraped-data'),
    path('api/actor/data/create/', create_actor_scraped_data, name='create-actor-scraped-data'),
    path('api/actor/data/<int:data_id>/', update_actor_scraped_data, name='update-actor-scraped-data'),
    path('api/actor/data/<int:data_id>/delete/', delete_actor_scraped_data, name='delete-actor-scraped-data'),
    path('api/actor/data/bulk-delete/', bulk_delete_actor_scraped_data, name='bulk-delete-actor-scraped-data'),
    path('api/actor/data/stats/', get_data_labeling_stats, name='get-data-labeling-stats'),

    # ============================================================================
    # BACKWARD COMPATIBILITY - EXISTING TIKTOK ENDPOINTS
    # ============================================================================

    # Health and system endpoints
    path('api/actor/health/', get_health_status, name='health-status'),
    path('api/actor/system-status/', system_status, name='system-status'),

    # Account management (legacy TikTok)
    path('api/actor/accounts/', get_accounts, name='accounts'),

    # Task management (legacy endpoints)
    path('api/actor/tasks/', get_tasks, name='tasks'),
    path('api/actor/tasks/stats/', get_task_statistics, name='task-statistics'),

    # Task CRUD operations (legacy comprehensive endpoints)
    path('api/actor/', include('actor.task_urls')),

    # Session management
    path('api/actor/sessions/', get_sessions, name='sessions'),
    path('api/actor/active-sessions/', get_active_sessions, name='active-sessions'),

    # Scraped data management
    path('api/actor/scraped-data/', get_scraped_data, name='scraped-data'),
    path('api/actor/scraped-data/by_task/', get_scraped_data_by_task, name='scraped-data-by-task'),

    # Search functionality
    path('api/actor/search-history/', get_search_history, name='search-history'),
    path('api/actor/search-presets/', get_search_presets, name='get-search-presets'),

    # Content scraping endpoints
    path('api/actor/simple-login/', simple_login_test, name='simple-login-test'),
    path('api/actor/scrape-keyword/', scrape_content_by_keyword, name='scrape-keyword'),
    path('api/actor/content-stats/', get_content_stats, name='content-stats'),
]