from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Count, Avg
from .models import TikTokUserAccount, ActorTask, ActorScrapedData, TikTokSession

@admin.register(TikTokUserAccount)
class TikTokUserAccountAdmin(admin.ModelAdmin):
    list_display = [
        'tiktok_username', 'user', 'is_active', 'session_valid_display',
        'last_login', 'total_tasks', 'login_attempts', 'created_at'
    ]
    list_filter = [
        'is_active', 'is_blocked', 'created_at', 'last_login'
    ]
    search_fields = ['tiktok_username', 'user__username', 'tiktok_user_id']
    readonly_fields = [
        'encrypted_session_data', 'session_expires_at', 'last_login',
        'login_attempts', 'created_at', 'updated_at', 'session_valid_display'
    ]
    
    fieldsets = (
        ('Account Information', {
            'fields': ('user', 'tiktok_username', 'tiktok_user_id')
        }),
        ('Account Status', {
            'fields': ('is_active', 'is_blocked', 'blocked_until')
        }),
        ('Session Information', {
            'fields': (
                'session_expires_at', 'last_login',
                'login_attempts', 'last_attempt_at', 'session_valid_display'
            ),
            'classes': ('collapse',)
        }),
        ('Security', {
            'fields': ('encrypted_session_data',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    actions = ['refresh_sessions', 'invalidate_sessions', 'mark_as_active']
    
    def total_tasks(self, obj):
        """Display total number of tasks for this account"""
        return obj.tasks.count()
    total_tasks.short_description = 'Total Tasks'
    
    def session_valid_display(self, obj):
        """Display session health status"""
        if not obj.is_session_valid():
            return format_html('<span style="color: red;">Invalid</span>')
        
        if obj.session_expires_at and obj.session_expires_at < timezone.now():
            return format_html('<span style="color: orange;">Expired</span>')
        
        return format_html('<span style="color: green;">Valid</span>')
    session_valid_display.short_description = 'Session Status'
    
    def refresh_sessions(self, request, queryset):
        """Refresh sessions for selected accounts"""
        count = 0
        for account in queryset:
            if account.is_session_valid():
                # Extend session expiry
                account.session_expires_at = timezone.now() + timezone.timedelta(days=7)
                account.save()
                count += 1
        
        self.message_user(request, f'Refreshed {count} sessions.')
    refresh_sessions.short_description = 'Refresh selected sessions'
    
    def invalidate_sessions(self, request, queryset):
        """Invalidate sessions for selected accounts"""
        count = queryset.update(
            encrypted_session_data='',
            session_expires_at=None
        )
        self.message_user(request, f'Invalidated {count} sessions.')
    invalidate_sessions.short_description = 'Invalidate selected sessions'
    
    def mark_as_active(self, request, queryset):
        """Mark selected accounts as active"""
        count = queryset.update(is_active=True)
        self.message_user(request, f'Marked {count} accounts as active.')
    mark_as_active.short_description = 'Mark as active'

@admin.register(ActorTask)
class ActorTaskAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'task_type', 'status', 'tiktok_account', 'task_name',
        'progress_percentage', 'items_scraped', 'created_at'
    ]
    list_filter = [
        'task_type', 'status', 'created_at',
        'started_at', 'completed_at'
    ]
    search_fields = ['id', 'tiktok_account__tiktok_username', 'task_name', 'target_identifier']
    readonly_fields = [
        'created_at', 'updated_at', 'started_at', 'completed_at',
        'progress_percentage', 'items_scraped', 'celery_task_id',
        'error_message', 'task_duration_display'
    ]
    
    fieldsets = (
        ('Task Information', {
            'fields': (
                'task_name', 'task_type', 'tiktok_account', 'status', 'target_identifier'
            )
        }),
        ('Configuration', {
            'fields': ('max_items', 'scrape_interval', 'use_stealth_mode', 'randomize_delays'),
            'classes': ('collapse',)
        }),
        ('Date Range', {
            'fields': ('start_date', 'end_date'),
            'classes': ('collapse',)
        }),
        ('Progress', {
            'fields': (
                'progress_percentage', 'items_scraped', 'total_items_found',
                'task_duration_display'
            ),
            'classes': ('collapse',)
        }),
        ('System', {
            'fields': ('celery_task_id', 'error_message'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'started_at', 'completed_at'),
            'classes': ('collapse',)
        })
    )
    
    actions = ['retry_failed_tasks', 'cancel_pending_tasks', 'mark_as_completed']
    
    def task_duration_display(self, obj):
        """Display task duration"""
        if obj.started_at and obj.completed_at:
            duration = obj.completed_at - obj.started_at
            return str(duration)
        elif obj.started_at:
            duration = timezone.now() - obj.started_at
            return f"{duration} (running)"
        return "Not started"
    task_duration_display.short_description = 'Duration'
    
    def retry_failed_tasks(self, request, queryset):
        """Retry failed tasks"""
        failed_tasks = queryset.filter(status='failed')
        count = failed_tasks.update(
            status='pending',
            error_message=None,
            retry_count=0
        )
        self.message_user(request, f'Retrying {count} failed tasks.')
    retry_failed_tasks.short_description = 'Retry failed tasks'
    
    def cancel_pending_tasks(self, request, queryset):
        """Cancel pending tasks"""
        pending_tasks = queryset.filter(status__in=['pending', 'running'])
        count = pending_tasks.update(status='cancelled')
        self.message_user(request, f'Cancelled {count} pending tasks.')
    cancel_pending_tasks.short_description = 'Cancel pending tasks'
    
    def mark_as_completed(self, request, queryset):
        """Mark selected tasks as completed"""
        count = queryset.update(
            status='completed',
            completed_at=timezone.now(),
            progress_percentage=100
        )
        self.message_user(request, f'Marked {count} tasks as completed.')
    mark_as_completed.short_description = 'Mark as completed'

@admin.register(ActorScrapedData)
class ActorScrapedDataAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'task', 'data_type', 'tiktok_id',
        'data_size_display', 'is_complete', 'scraped_at'
    ]
    list_filter = [
        'data_type', 'scraped_at', 'is_complete',
        'task__task_type', 'task__tiktok_account'
    ]
    search_fields = [
        'tiktok_id', 'task__id', 'task__task_name'
    ]
    readonly_fields = [
        'scraped_at', 'data_size_display', 'content_preview'
    ]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('task', 'data_type', 'tiktok_id')
        }),
        ('Content Data', {
            'fields': ('content', 'data_size_display', 'content_preview'),
            'classes': ('collapse',)
        }),
        ('Quality Metrics', {
            'fields': ('is_complete',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('scraped_at',),
            'classes': ('collapse',)
        })
    )
    
    def data_size_display(self, obj):
        """Display data size in human readable format"""
        import json
        try:
            size = len(json.dumps(obj.content))
            if size < 1024:
                return f"{size} B"
            elif size < 1024 * 1024:
                return f"{size / 1024:.1f} KB"
            else:
                return f"{size / (1024 * 1024):.1f} MB"
        except:
            return "Unknown"
    data_size_display.short_description = 'Data Size'
    
    def content_preview(self, obj):
        """Display preview of scraped data"""
        import json
        try:
            data_str = json.dumps(obj.content, indent=2)[:500]
            if len(data_str) >= 500:
                data_str += "..."
            return format_html('<pre>{}</pre>', data_str)
        except:
            return "Invalid JSON data"
    content_preview.short_description = 'Content Preview'

@admin.register(TikTokSession)
class TikTokSessionAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'tiktok_account', 'session_id', 'is_healthy',
        'detection_score', 'created_at', 'expires_at'
    ]
    list_filter = [
        'is_healthy', 'created_at', 'expires_at', 'last_activity'
    ]
    search_fields = ['tiktok_account__tiktok_username', 'session_id']
    readonly_fields = [
        'created_at', 'last_activity', 'detection_score',
        'requests_made', 'successful_requests', 'failed_requests'
    ]
    
    fieldsets = (
        ('Session Information', {
            'fields': (
                'tiktok_account', 'session_id', 'user_agent', 'proxy_used'
            )
        }),
        ('Health Metrics', {
            'fields': ('requests_made', 'successful_requests', 'failed_requests', 
                      'captcha_challenges', 'rate_limit_hits'),
            'classes': ('collapse',)
        }),
        ('Detection Status', {
            'fields': ('detection_score', 'is_healthy'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'expires_at', 'last_activity'),
            'classes': ('collapse',)
        })
    )
    
    actions = ['mark_unhealthy', 'reset_metrics']
    

    
    def mark_unhealthy(self, request, queryset):
        """Mark selected sessions as unhealthy"""
        count = queryset.update(is_healthy=False, detection_score=1.0)
        self.message_user(request, f'Marked {count} sessions as unhealthy.')
    mark_unhealthy.short_description = 'Mark as unhealthy'
    
    def reset_metrics(self, request, queryset):
        """Reset metrics for selected sessions"""
        count = queryset.update(
            requests_made=0,
            successful_requests=0,
            failed_requests=0,
            captcha_challenges=0,
            rate_limit_hits=0,
            detection_score=0.0,
            is_healthy=True
        )
        self.message_user(request, f'Reset metrics for {count} sessions.')
    reset_metrics.short_description = 'Reset metrics'
