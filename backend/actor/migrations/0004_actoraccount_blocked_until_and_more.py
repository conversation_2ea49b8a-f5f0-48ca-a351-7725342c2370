# Generated by Django 5.2.4 on 2025-07-25 04:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('actor', '0003_remove_actoraccount_blocked_until_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='actoraccount',
            name='blocked_until',
            field=models.DateTimeField(blank=True, help_text='When the block expires', null=True),
        ),
        migrations.AddField(
            model_name='actoraccount',
            name='encrypted_session_data',
            field=models.TextField(blank=True, help_text='Encrypted session data', null=True),
        ),
        migrations.AddField(
            model_name='actoraccount',
            name='is_blocked',
            field=models.Bo<PERSON>an<PERSON>ield(default=False, help_text='Whether account is temporarily blocked'),
        ),
        migrations.AddField(
            model_name='actoraccount',
            name='last_authenticated_at',
            field=models.DateTimeField(blank=True, help_text='Last successful authentication', null=True),
        ),
        migrations.AddField(
            model_name='actoraccount',
            name='login_attempts',
            field=models.IntegerField(default=0, help_text='Number of login attempts'),
        ),
        migrations.AddField(
            model_name='actoraccount',
            name='session_expires_at',
            field=models.DateTimeField(blank=True, help_text='When the session expires', null=True),
        ),
    ]
