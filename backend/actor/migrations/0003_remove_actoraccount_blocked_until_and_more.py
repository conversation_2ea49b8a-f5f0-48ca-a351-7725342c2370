# Generated by Django 5.2.4 on 2025-07-25 03:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('actor', '0002_remove_quality_score'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='actoraccount',
            name='blocked_until',
        ),
        migrations.RemoveField(
            model_name='actoraccount',
            name='encrypted_session_data',
        ),
        migrations.RemoveField(
            model_name='actoraccount',
            name='is_blocked',
        ),
        migrations.RemoveField(
            model_name='actoraccount',
            name='last_attempt_at',
        ),
        migrations.RemoveField(
            model_name='actoraccount',
            name='last_login',
        ),
        migrations.RemoveField(
            model_name='actoraccount',
            name='login_attempts',
        ),
        migrations.RemoveField(
            model_name='actoraccount',
            name='platform_user_id',
        ),
        migrations.RemoveField(
            model_name='actoraccount',
            name='session_expires_at',
        ),
        migrations.AddField(
            model_name='actoraccount',
            name='last_used',
            field=models.DateTimeField(blank=True, help_text='Last time this account was used for scraping', null=True),
        ),
        migrations.AddField(
            model_name='actoraccount',
            name='webdriver_active',
            field=models.BooleanField(default=False, help_text='Whether webdriver window is currently active'),
        ),
    ]
