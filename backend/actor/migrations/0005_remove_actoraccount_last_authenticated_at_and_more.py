# Generated by Django 5.2.4 on 2025-07-25 06:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('actor', '0004_actoraccount_blocked_until_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='actoraccount',
            name='last_authenticated_at',
        ),
        migrations.RemoveField(
            model_name='actoraccount',
            name='last_used',
        ),
        migrations.RemoveField(
            model_name='actoraccount',
            name='webdriver_active',
        ),
        migrations.AddField(
            model_name='actoraccount',
            name='last_attempt_at',
            field=models.DateTimeField(blank=True, help_text='Last login attempt', null=True),
        ),
        migrations.AddField(
            model_name='actoraccount',
            name='last_login',
            field=models.DateTimeField(blank=True, help_text='Last successful login', null=True),
        ),
        migrations.AddField(
            model_name='actoraccount',
            name='platform_user_id',
            field=models.Char<PERSON>ield(blank=True, help_text='User ID on the platform', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='actorscrapeddata',
            name='quality_score',
            field=models.FloatField(default=1.0, help_text='Data quality score (0.0 to 1.0)'),
        ),
        migrations.AlterField(
            model_name='actoraccount',
            name='encrypted_session_data',
            field=models.TextField(default={}, help_text='Encrypted session data'),
            preserve_default=False,
        ),
    ]
