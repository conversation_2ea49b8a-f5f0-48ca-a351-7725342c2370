from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from actor.scrapers.twitter_scraper import TwitterScraper


class Command(BaseCommand):
    help = 'Clean up duplicate and unused actor accounts'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user-id',
            type=int,
            help='Clean up actors for a specific user ID only',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting',
        )

    def handle(self, *args, **options):
        user_id = options.get('user_id')
        dry_run = options.get('dry_run')
        
        user = None
        if user_id:
            try:
                user = User.objects.get(id=user_id)
                self.stdout.write(f"Cleaning up actors for user: {user.username} (ID: {user.id})")
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f"User with ID {user_id} does not exist")
                )
                return
        else:
            self.stdout.write("Cleaning up actors for all users")

        if dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN MODE - No actual deletions will be performed"))

        # Call the cleanup method from TwitterS<PERSON>raper
        try:
            if dry_run:
                # For dry run, we'll simulate the cleanup
                self._dry_run_cleanup(user)
            else:
                result = TwitterScraper.cleanup_duplicate_actors(user=user)
                
                if result.get('success'):
                    deleted_count = result.get('deleted_count', 0)
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Successfully cleaned up {deleted_count} duplicate/unused actor accounts"
                        )
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR(f"Cleanup failed: {result.get('error')}")
                    )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Cleanup command failed: {str(e)}")
            )

    def _dry_run_cleanup(self, user=None):
        """Simulate cleanup without actually deleting anything"""
        try:
            from actor.models import ActorAccount, ActorTask
            from django.db.models import Count, Q
            from django.utils import timezone
            from datetime import timedelta
            
            # Filter by user if provided
            accounts_query = ActorAccount.objects.filter(platform='twitter')
            if user:
                accounts_query = accounts_query.filter(user=user)
            
            # Find duplicate accounts
            duplicates = accounts_query.values('user', 'platform', 'platform_username').annotate(
                count=Count('id')
            ).filter(count__gt=1)
            
            duplicate_count = 0
            for duplicate in duplicates:
                duplicate_accounts = ActorAccount.objects.filter(
                    user_id=duplicate['user'],
                    platform=duplicate['platform'],
                    platform_username=duplicate['platform_username']
                ).order_by('-last_login', '-created_at')
                
                accounts_to_delete = duplicate_accounts[1:]  # Skip the first (most recent)
                
                for account in accounts_to_delete:
                    active_tasks = ActorTask.objects.filter(
                        actor_account=account,
                        status__in=['PENDING', 'RUNNING']
                    )
                    
                    if not active_tasks.exists():
                        self.stdout.write(f"[DRY RUN] Would delete duplicate: {account.platform}/@{account.platform_username} (ID: {account.id})")
                        duplicate_count += 1
                    else:
                        self.stdout.write(f"[DRY RUN] Would skip (has active tasks): {account.platform}/@{account.platform_username}")
            
            # Find unused accounts
            thirty_days_ago = timezone.now() - timedelta(days=30)
            unused_accounts = accounts_query.filter(
                Q(last_login__lt=thirty_days_ago) | Q(last_login__isnull=True),
                is_active=True
            )
            
            unused_count = 0
            for account in unused_accounts:
                has_tasks = ActorTask.objects.filter(actor_account=account).exists()
                
                if not has_tasks:
                    self.stdout.write(f"[DRY RUN] Would delete unused: {account.platform}/@{account.platform_username} (ID: {account.id})")
                    unused_count += 1
                else:
                    self.stdout.write(f"[DRY RUN] Would mark inactive: {account.platform}/@{account.platform_username} (has tasks)")
            
            total_would_delete = duplicate_count + unused_count
            self.stdout.write(
                self.style.SUCCESS(
                    f"[DRY RUN] Would delete {total_would_delete} accounts ({duplicate_count} duplicates, {unused_count} unused)"
                )
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Dry run simulation failed: {str(e)}")
            )