"""Configuration for bot detection and anti-detection measures"""

from datetime import timedelta

# Login attempt limits and blocking - Less aggressive to allow more attempts
LOGIN_ATTEMPT_LIMITS = {
    'max_attempts_per_session': 5,  # Increased from 2
    'max_attempts_per_hour': 8,    # Increased from 3
    'max_attempts_per_day': 20,    # Increased from 8
    'cooldown_after_failure': 180, # Reduced from 600 (3 minutes)
    'progressive_delay': True,
    'base_delay': 60,   # Reduced from 120 (1 minute)
    'max_delay': 1800,  # Reduced from 3600 (30 minutes)
}

# Session rotation thresholds
SESSION_ROTATION_THRESHOLDS = {
    'anti_bot_score': 0.3,
    'session_quality': 0.4,
    'login_success_rate': 0.6,
    'max_session_age_days': 7,
}

# Account health scoring weights
HEALTH_SCORE_WEIGHTS = {
    'session_quality': 0.3,
    'anti_bot_score': 0.25,
    'login_success_rate': 0.2,
    'account_age': 0.15,
    'recent_activity': 0.1,
}

# Risk level thresholds
RISK_LEVELS = {
    'low': 0.7,
    'medium': 0.4,
    'high': 0.2,
    # Below 0.2 is critical
}

# Cooldown periods for different failure types
COOLDOWN_PERIODS = {
    'bot_detection': timedelta(hours=6),
    'rate_limiting': timedelta(hours=2),
    'captcha_timeout': timedelta(hours=1),
    'general_failure': timedelta(minutes=30),
    'quality_issues': timedelta(minutes=15),
}

# Bot detection indicators
BOT_DETECTION_INDICATORS = {
    'keywords': [
        'bot detected',
        'suspicious activity',
        'automated behavior',
        'please verify',
        'security check',
        'unusual activity',
        'verify your identity',
        'account temporarily restricted',
        'rate limit exceeded',
        'too many requests'
    ],
    'css_selectors': [
        '[data-testid="captcha"]',
        '.captcha-container',
        '#captcha',
        '.security-check',
        '.bot-detection',
        '.verification-required',
        '.rate-limit-warning',
        '.suspicious-activity'
    ]
}

# Rate limiting detection
RATE_LIMITING_INDICATORS = {
    'keywords': [
        'rate limit',
        'too many requests',
        'slow down',
        'try again later',
        'temporarily unavailable',
        'service temporarily unavailable'
    ],
    'css_selectors': [
        '.rate-limit',
        '.too-many-requests',
        '.service-unavailable'
    ]
}

# CAPTCHA detection
CAPTCHA_INDICATORS = {
    'css_selectors': [
        '[data-testid="captcha"]',
        '.captcha-container',
        '#captcha',
        '.recaptcha',
        '.hcaptcha',
        '.captcha-challenge',
        'iframe[src*="captcha"]',
        'iframe[src*="recaptcha"]',
        'iframe[src*="hcaptcha"]'
    ],
    'timeout_seconds': 120  # How long to wait for manual CAPTCHA resolution
}

# Human behavior simulation settings - Enhanced for better stealth
HUMAN_BEHAVIOR_CONFIG = {
    'typing_delays': {
        'enabled': True,
        'min_char_delay': 0.12,
        'max_char_delay': 0.35,
        'word_pause_min': 0.5,
        'word_pause_max': 1.2,
        'thinking_pause_chance': 0.25,
        'thinking_pause_min': 1.2,
        'thinking_pause_max': 3.5,
        'backspace_chance': 0.08,
        'correction_delay': 0.8
    },
    'mouse_movements': {
        'enabled': True,
        'frequency': 0.4,
        'max_distance': 200,
        'smooth_movement': True,
        'hover_elements': True,
        'random_clicks': 0.1
    },
    'scrolling': {
        'enabled': True,
        'scroll_count_range': (3, 7),
        'scroll_amount_range': (150, 800),
        'scroll_delay_range': (2.0, 6.0),
        'reverse_scroll_chance': 0.2
    },
    'page_interactions': {
        'pre_login_wait_range': (5.0, 12.0),
        'post_login_wait_range': (3.0, 8.0),
        'read_page_delay': (3.0, 8.0),
        'form_fill_delay': (2.0, 5.0),
        'element_focus_delay': (0.8, 2.0),
        'tab_navigation_chance': 0.3
    }
}

# Browser fingerprint rotation settings
FINGERPRINT_ROTATION = {
    'rotate_on_detection': True,
    'rotate_on_quality_drop': True,
    'min_rotation_interval_hours': 24,
    'user_agents': [
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0'
    ]
}

# Session quality metrics
SESSION_QUALITY_METRICS = {
    'required_cookies': [
        'sessionid',
        'csrftoken',
        'tt_webid',
        'tt_webid_v2'
    ],
    'quality_indicators': {
        'has_user_info': 0.3,
        'has_valid_cookies': 0.4,
        'session_age_appropriate': 0.2,
        'no_error_indicators': 0.1
    }
}

# Retry configuration (updated for multi-strategy approach)
RETRY_CONFIG = {
    'max_retries': 3,      # Increased from 2
    'base_delay': 120,     # Reduced from 180 (2 minutes)
    'max_delay': 1200,     # Reduced from 3600 (20 minutes)
    'exponential_base': 2, # Reduced from 3
    'jitter_range': (0.9, 1.1),  # Reduced jitter
    'retry_on_errors': [
        'network_error',
        'timeout'
    ],
    'no_retry_errors': [
        'invalid_credentials',
        'account_locked'
        # Removed bot_detection, rate_limiting, captcha_timeout to allow retries with different strategies
    ],
    'session_rotation_on_retry': True,
    'ip_rotation_on_retry': False  # Set to True if proxy rotation is available
}

# Monitoring and alerting
MONITORING_CONFIG = {
    'health_check_interval_minutes': 30,
    'alert_thresholds': {
        'unhealthy_accounts_percentage': 0.5,
        'failed_login_rate': 0.3,
        'bot_detection_rate': 0.1
    },
    'cleanup_interval_hours': 6
}

# Development and testing settings
DEVELOPMENT_CONFIG = {
    'debug_mode': False,
    'simulate_failures': False,
    'log_detailed_errors': True,
    'save_screenshots_on_failure': True,
    'test_mode_delays': {
        'enabled': False,
        'multiplier': 0.1  # Speed up delays in test mode
    }
}