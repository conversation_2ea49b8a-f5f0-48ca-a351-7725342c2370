from rest_framework import serializers
from django.contrib.auth.models import User
from .models import TikTok<PERSON><PERSON><PERSON>ccount, ActorTask, ActorScrapedData, TikTokSession
from django.utils import timezone
import re

class TikTokUserAccountSerializer(serializers.ModelSerializer):
    """Serializer for TikTok user accounts"""
    user_id = serializers.PrimaryKeyRelatedField(source='user', read_only=True)
    username = serializers.CharField(source='user.username', read_only=True)
    session_valid = serializers.SerializerMethodField()
    password = serializers.CharField(write_only=True, help_text="TikTok account password")
    status = serializers.SerializerMethodField(help_text="Account status")
    
    def to_representation(self, instance):
        """Convert boolean is_active to string status"""
        data = super().to_representation(instance)
        data['status'] = 'ACTIVE' if instance.is_active else 'INACTIVE'
        return data
    
    def to_internal_value(self, data):
        """Convert string status to boolean is_active"""
        if 'status' in data:
            status_value = data['status']
            if status_value == 'ACTIVE':
                data['is_active'] = True
            elif status_value == 'INACTIVE':
                data['is_active'] = False
            else:
                raise serializers.ValidationError({'status': 'Status must be either ACTIVE or INACTIVE'})
            # Remove status from data since we're using is_active internally
            data = data.copy()
            del data['status']
        return super().to_internal_value(data)
    
    class Meta:
        model = TikTokUserAccount
        fields = [
            'id', 'user_id', 'username', 'tiktok_username', 'email', 'password',
            'status', 'last_login', 'session_expires_at', 'created_at',
            'updated_at', 'login_attempts', 'is_blocked', 'blocked_until',
            'session_valid'
        ]
        read_only_fields = [
            'id', 'user_id', 'username', 'status', 'created_at', 'updated_at',
            'login_attempts', 'is_blocked', 'blocked_until', 'session_valid'
        ]
    
    def get_session_valid(self, obj):
        return obj.is_session_valid()
    
    def get_status(self, obj):
        """Convert boolean is_active to string status"""
        return 'ACTIVE' if obj.is_active else 'INACTIVE'
    
    def create(self, validated_data):
        """Create account with encrypted password"""
        password = validated_data.pop('password')
        
        # Only set user if request context exists and user is authenticated
        if hasattr(self, 'context') and 'request' in self.context and self.context['request'].user.is_authenticated:
            validated_data['user'] = self.context['request'].user
        
        account = TikTokUserAccount(**validated_data)
        account.encrypt_password(password)
        account.save()
        return account
    
    def update(self, instance, validated_data):
        """Update account with encrypted password if provided"""
        password = validated_data.pop('password', None)
        
        if password:
            instance.encrypt_password(password)
        
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        instance.save()
        return instance

class TikTokLoginSerializer(serializers.Serializer):
    """Serializer for TikTok login credentials"""
    tiktok_username = serializers.CharField(max_length=255, help_text="TikTok username or email")
    tiktok_password = serializers.CharField(max_length=255, write_only=True, help_text="TikTok password")
    use_2fa = serializers.BooleanField(default=False, help_text="Whether to use 2FA")
    two_factor_code = serializers.CharField(max_length=10, required=False, allow_blank=True, help_text="2FA code if required")
    remember_session = serializers.BooleanField(default=True, help_text="Whether to remember the session")
    
    def validate_tiktok_username(self, value):
        """Validate TikTok username format"""
        if not value:
            raise serializers.ValidationError("TikTok username is required.")
        
        # Remove @ if present
        if value.startswith('@'):
            value = value[1:]
        
        # Basic username validation
        if not re.match(r'^[a-zA-Z0-9._-]+$', value):
            raise serializers.ValidationError("Invalid TikTok username format.")
        
        return value
    
    def validate(self, data):
        """Cross-field validation"""
        if data.get('use_2fa') and not data.get('two_factor_code'):
            raise serializers.ValidationError({
                'two_factor_code': 'Two-factor authentication code is required when 2FA is enabled.'
            })
        
        return data

class ActorTaskSerializer(serializers.ModelSerializer):
    """Serializer for actor tasks"""
    user_id = serializers.PrimaryKeyRelatedField(source='user', read_only=True)
    username = serializers.CharField(source='user.username', read_only=True)
    tiktok_username = serializers.CharField(source='tiktok_account.tiktok_username', read_only=True)
    scraped_data_count = serializers.SerializerMethodField()
    estimated_completion = serializers.SerializerMethodField()
    
    class Meta:
        model = ActorTask
        fields = [
            'id', 'user_id', 'username', 'tiktok_account', 'tiktok_username',
            'task_name', 'task_type', 'target_identifier', 'status',
            'max_items', 'scrape_interval', 'use_stealth_mode', 'randomize_delays',
            'start_date', 'end_date', 'celery_task_id', 'created_at', 'updated_at',
            'started_at', 'completed_at', 'error_message', 'items_scraped',
            'total_items_found', 'progress_percentage', 'scraped_data_count',
            'estimated_completion'
        ]
        read_only_fields = [
            'id', 'user_id', 'username', 'tiktok_username', 'status',
            'celery_task_id', 'created_at', 'updated_at', 'started_at',
            'completed_at', 'error_message', 'items_scraped', 'total_items_found',
            'progress_percentage', 'scraped_data_count', 'estimated_completion'
        ]
    
    def get_scraped_data_count(self, obj):
        return obj.scraped_data.count()
    
    def get_estimated_completion(self, obj):
        """Estimate completion time based on progress"""
        if obj.status == 'COMPLETED':
            return obj.completed_at
        
        if obj.status == 'RUNNING' and obj.started_at and obj.progress_percentage > 0:
            elapsed = timezone.now() - obj.started_at
            total_estimated = elapsed / (obj.progress_percentage / 100)
            return obj.started_at + total_estimated
        
        return None
    
    def validate(self, data):
        """Validate task data"""
        task_type = data.get('task_type')
        target_identifier = data.get('target_identifier')
        
        # Validate target identifier based on task type
        if task_type in ['TARGETED_USER', 'COMPETITOR_ANALYSIS']:
            if not target_identifier:
                raise serializers.ValidationError({
                    'target_identifier': f'Target identifier is required for {task_type} tasks.'
                })
            
            # Validate username format
            username = target_identifier.strip()
            if username.startswith('@'):
                username = username[1:]
            
            if not re.match(r'^[a-zA-Z0-9._-]+$', username):
                raise serializers.ValidationError({
                    'target_identifier': 'Invalid TikTok username format.'
                })
        
        elif task_type == 'HASHTAG_ANALYSIS':
            if not target_identifier:
                raise serializers.ValidationError({
                    'target_identifier': 'Hashtag is required for hashtag analysis tasks.'
                })
            
            # Validate hashtag format
            hashtag = target_identifier.strip()
            if not hashtag.startswith('#'):
                hashtag = '#' + hashtag
            
            if not re.match(r'^#[a-zA-Z0-9_]+$', hashtag):
                raise serializers.ValidationError({
                    'target_identifier': 'Invalid hashtag format.'
                })
        
        # Validate date range
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError({
                'end_date': 'End date must be after start date.'
            })
        
        # Validate max_items
        max_items = data.get('max_items', 100)
        if max_items < 1 or max_items > 10000:
            raise serializers.ValidationError({
                'max_items': 'Max items must be between 1 and 10,000.'
            })
        
        # Validate scrape_interval
        scrape_interval = data.get('scrape_interval', 60)
        if scrape_interval < 10 or scrape_interval > 3600:
            raise serializers.ValidationError({
                'scrape_interval': 'Scrape interval must be between 10 and 3600 seconds.'
            })
        
        return data

class ActorTaskCreateSerializer(ActorTaskSerializer):
    """Serializer for creating actor tasks"""
    tiktok_account_id = serializers.IntegerField(write_only=True, help_text="ID of the TikTok account to use")
    
    class Meta(ActorTaskSerializer.Meta):
        fields = ActorTaskSerializer.Meta.fields + ['tiktok_account_id']
        read_only_fields = [
            'id', 'user_id', 'username', 'tiktok_username', 'tiktok_account',
            'status', 'celery_task_id', 'created_at', 'updated_at', 'started_at',
            'completed_at', 'error_message', 'items_scraped', 'total_items_found',
            'progress_percentage', 'scraped_data_count', 'estimated_completion'
        ]
    
    def validate_tiktok_account_id(self, value):
        """Validate that the TikTok account belongs to the current user"""
        user = self.context['request'].user
        try:
            account = TikTokUserAccount.objects.get(id=value, user=user)
            if not account.is_session_valid():
                raise serializers.ValidationError("TikTok account session is invalid or expired.")
            return value
        except TikTokUserAccount.DoesNotExist:
            raise serializers.ValidationError("TikTok account not found or does not belong to you.")
    
    def create(self, validated_data):
        """Create a new actor task"""
        tiktok_account_id = validated_data.pop('tiktok_account_id')
        tiktok_account = TikTokUserAccount.objects.get(id=tiktok_account_id)
        
        validated_data['user'] = self.context['request'].user
        validated_data['tiktok_account'] = tiktok_account
        
        return super().create(validated_data)

class ActorScrapedDataSerializer(serializers.ModelSerializer):
    """Serializer for actor scraped data"""
    task_id = serializers.PrimaryKeyRelatedField(source='task', read_only=True)
    task_name = serializers.CharField(source='task.task_name', read_only=True)
    task_type = serializers.CharField(source='task.task_type', read_only=True)
    
    class Meta:
        model = ActorScrapedData
        fields = [
            'id', 'task_id', 'task_name', 'task_type', 'data_type',
            'content', 'tiktok_id', 'scraped_at', 'is_complete'
        ]
        read_only_fields = [
            'id', 'task_id', 'task_name', 'task_type', 'data_type',
            'content', 'tiktok_id', 'scraped_at', 'is_complete'
        ]

class TikTokSessionSerializer(serializers.ModelSerializer):
    """Serializer for TikTok sessions"""
    tiktok_username = serializers.CharField(source='tiktok_account.tiktok_username', read_only=True)
    success_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = TikTokSession
        fields = [
            'id', 'tiktok_username', 'session_id', 'user_agent', 'proxy_used',
            'requests_made', 'successful_requests', 'failed_requests',
            'captcha_challenges', 'rate_limit_hits', 'detection_score',
            'last_activity', 'is_healthy', 'created_at', 'expires_at',
            'success_rate'
        ]
        read_only_fields = [
            'id', 'tiktok_username', 'session_id', 'user_agent', 'proxy_used',
            'requests_made', 'successful_requests', 'failed_requests',
            'captcha_challenges', 'rate_limit_hits', 'detection_score',
            'last_activity', 'is_healthy', 'created_at', 'expires_at',
            'success_rate'
        ]
    
    def get_success_rate(self, obj):
        """Calculate success rate percentage"""
        if obj.requests_made == 0:
            return 0.0
        return (obj.successful_requests / obj.requests_made) * 100

class TaskStatsSerializer(serializers.Serializer):
    """Serializer for task statistics"""
    total_tasks = serializers.IntegerField()
    pending_tasks = serializers.IntegerField()
    running_tasks = serializers.IntegerField()
    completed_tasks = serializers.IntegerField()
    failed_tasks = serializers.IntegerField()
    success_rate = serializers.FloatField()
    average_completion_time = serializers.FloatField()
    total_items_scraped = serializers.IntegerField()
    active_accounts = serializers.IntegerField()