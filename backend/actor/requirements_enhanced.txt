# Enhanced TikTok Actor Requirements
# Additional dependencies for enhanced features

# Core enhanced dependencies
undetected-chromedriver>=3.5.5
fake-useragent>=2.2.0
selenium>=4.34.2
webdriver-manager>=4.0.2

# Proxy and networking
requests>=2.32.4
urllib3>=2.5.0
PySocks>=1.7.1

# Encryption and security
cryptography>=45.0.5

# Data processing and storage
beautifulsoup4>=4.13.4

# Async and task processing
celery>=5.5.3
redis>=6.2.0

# Django and web framework
Django>=5.2.4
djangorestframework>=3.16.0

# Utilities
python-dateutil>=2.9.0.post0
python-dotenv>=1.1.1

# Optional: Advanced features
# Uncomment if you need these features

# For advanced image processing (captcha handling)
# Pillow>=10.0.0
# opencv-python>=4.8.0

# For machine learning-based detection avoidance
# scikit-learn>=1.3.0
# numpy>=1.24.0

# For advanced proxy management
# aiohttp>=3.8.0
# asyncio-throttle>=1.0.0

# For monitoring and metrics
# prometheus-client>=0.17.0
# psutil>=5.9.0

# For advanced logging
# structlog>=23.1.0
# colorlog>=6.7.0

# For database optimization
# psycopg2-binary>=2.9.0  # For PostgreSQL
# mysqlclient>=2.2.0      # For MySQL

# Development and testing dependencies
# pytest>=7.4.0
# pytest-django>=4.5.0
# pytest-asyncio>=0.21.0
# pytest-mock>=3.11.0
# factory-boy>=3.3.0
# coverage>=7.3.0

# Code quality
# black>=23.7.0
# flake8>=6.0.0
# isort>=5.12.0
# mypy>=1.5.0
