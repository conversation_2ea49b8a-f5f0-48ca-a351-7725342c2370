import json
import logging
import time
import pickle
import base64
import hashlib
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from django.utils import timezone
from django.core.cache import cache
from cryptography.fernet import Fernet
from django.conf import settings
from ..models import TikTokUserAccount, TikTokSession
from ..config.bot_detection_config import (
    SESSION_ROTATION_THRESHOLDS,
    HEALTH_SCORE_WEIGHTS,
    RISK_LEVELS,
    COOLDOWN_PERIODS,
    SESSION_QUALITY_METRICS
)

logger = logging.getLogger(__name__)

class SessionManager:
    """
    Manages TikTok session data with encryption and persistence
    """
    
    def __init__(self):
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)
        self.session_timeout = 3600  # 1 hour default
        self.max_session_age = 86400 * 7  # 7 days
    
    def _get_or_create_encryption_key(self) -> bytes:
        """
        Get or create encryption key for session data
        
        Returns:
            bytes: Encryption key
        """
        try:
            # Try to get key from settings
            if hasattr(settings, 'TIKTOK_SESSION_KEY'):
                return settings.TIKTOK_SESSION_KEY.encode()
            
            # Generate new key if not found
            key = Fernet.generate_key()
            logger.warning("Generated new encryption key. Sessions may be invalidated.")
            return key
        
        except Exception as e:
            logger.error(f"Error getting encryption key: {str(e)}")
            return Fernet.generate_key()
    
    def save_session(self, account_id: int, session_data: Dict[str, Any]) -> bool:
        """
        Save encrypted session data for a TikTok account
        
        Args:
            account_id: TikTokUserAccount ID
            session_data: Session data to save
        
        Returns:
            bool: True if saved successfully, False otherwise
        """
        try:
            # Get the account
            account = TikTokUserAccount.objects.get(id=account_id)
            
            # Encrypt session data
            encrypted_data = self._encrypt_session_data(session_data)
            
            # Update account with encrypted session
            account.encrypted_session_data = encrypted_data
            account.session_expires_at = timezone.now() + timedelta(seconds=self.max_session_age)
            account.last_login_at = timezone.now()
            account.is_session_valid = True
            account.save()
            
            # Create or update session record
            session, created = TikTokSession.objects.get_or_create(
                account=account,
                defaults={
                    'session_start': timezone.now(),
                    'last_activity': timezone.now(),
                    'is_active': True,
                    'session_quality_score': self._calculate_session_quality(session_data),
                    'anti_bot_score': session_data.get('anti_bot_score', 0.0),
                    'login_attempts': 1,
                    'captcha_encounters': session_data.get('captcha_count', 0),
                    'session_metadata': {
                        'user_agent': session_data.get('user_agent', ''),
                        'ip_address': session_data.get('ip_address', ''),
                        'browser_fingerprint': session_data.get('browser_fingerprint', ''),
                        'creation_timestamp': timezone.now().isoformat()
                    }
                }
            )
            
            if not created:
                # Update existing session
                session.last_activity = timezone.now()
                session.is_active = True
                session.session_quality_score = self._calculate_session_quality(session_data)
                session.anti_bot_score = session_data.get('anti_bot_score', session.anti_bot_score)
                session.save()
            
            # Cache session for quick access
            cache_key = f"tiktok_session_{account_id}"
            cache.set(cache_key, session_data, timeout=self.session_timeout)
            
            logger.info(f"Session saved for account {account_id}")
            return True
        
        except TikTokUserAccount.DoesNotExist:
            logger.error(f"Account {account_id} not found")
            return False
        except Exception as e:
            logger.error(f"Error saving session: {str(e)}")
            return False
    
    def load_session(self, account_id: int) -> Optional[Dict[str, Any]]:
        """
        Load and decrypt session data for a TikTok account
        
        Args:
            account_id: TikTokUserAccount ID
        
        Returns:
            dict: Decrypted session data or None if not found/invalid
        """
        try:
            # Try cache first
            cache_key = f"tiktok_session_{account_id}"
            cached_session = cache.get(cache_key)
            if cached_session:
                logger.debug(f"Session loaded from cache for account {account_id}")
                return cached_session
            
            # Get from database
            account = TikTokUserAccount.objects.get(id=account_id)
            
            # Check if session is still valid
            if not self._is_session_valid(account):
                logger.warning(f"Session expired for account {account_id}")
                return None
            
            # Decrypt session data
            session_data = self._decrypt_session_data(account.encrypted_session_data)
            
            if session_data:
                # Update cache
                cache.set(cache_key, session_data, timeout=self.session_timeout)
                
                # Update last activity
                self._update_session_activity(account_id)
                
                logger.info(f"Session loaded for account {account_id}")
                return session_data
            
            return None
        
        except TikTokUserAccount.DoesNotExist:
            logger.error(f"Account {account_id} not found")
            return None
        except Exception as e:
            logger.error(f"Error loading session: {str(e)}")
            return None
    
    def invalidate_session(self, account_id: int) -> bool:
        """
        Invalidate session for a TikTok account
        
        Args:
            account_id: TikTokUserAccount ID
        
        Returns:
            bool: True if invalidated successfully, False otherwise
        """
        try:
            # Update account
            account = TikTokUserAccount.objects.get(id=account_id)
            account.is_session_valid = False
            account.encrypted_session_data = None
            account.session_expires_at = None
            account.save()
            
            # Update session record
            try:
                session = TikTokSession.objects.get(account=account)
                session.is_active = False
                session.session_end = timezone.now()
                session.save()
            except TikTokSession.DoesNotExist:
                pass
            
            # Clear cache
            cache_key = f"tiktok_session_{account_id}"
            cache.delete(cache_key)
            
            logger.info(f"Session invalidated for account {account_id}")
            return True
        
        except TikTokUserAccount.DoesNotExist:
            logger.error(f"Account {account_id} not found")
            return False
        except Exception as e:
            logger.error(f"Error invalidating session: {str(e)}")
            return False
    
    def refresh_session(self, account_id: int, new_session_data: Dict[str, Any]) -> bool:
        """
        Refresh session with new data
        
        Args:
            account_id: TikTokUserAccount ID
            new_session_data: New session data
        
        Returns:
            bool: True if refreshed successfully, False otherwise
        """
        try:
            # Load existing session
            existing_session = self.load_session(account_id)
            
            if existing_session:
                # Merge with new data
                merged_session = {**existing_session, **new_session_data}
            else:
                merged_session = new_session_data
            
            # Save updated session
            return self.save_session(account_id, merged_session)
        
        except Exception as e:
            logger.error(f"Error refreshing session: {str(e)}")
            return False
    
    def validate_session_health(self, account_id: int) -> Dict[str, Any]:
        """
        Validate session health and return status
        
        Args:
            account_id: TikTokUserAccount ID
        
        Returns:
            dict: Session health status
        """
        try:
            account = TikTokUserAccount.objects.get(id=account_id)
            session_data = self.load_session(account_id)
            
            health_status = {
                'is_valid': False,
                'is_expired': True,
                'quality_score': 0.0,
                'anti_bot_score': 0.0,
                'last_activity': None,
                'time_remaining': 0,
                'recommendations': []
            }
            
            if session_data:
                health_status['is_valid'] = True
                health_status['is_expired'] = False
                
                # Get session record for detailed metrics
                try:
                    session = TikTokSession.objects.get(account=account)
                    health_status['quality_score'] = session.session_quality_score
                    health_status['anti_bot_score'] = session.anti_bot_score
                    health_status['last_activity'] = session.last_activity.isoformat()
                    
                    # Calculate time remaining
                    if account.session_expires_at:
                        time_remaining = (account.session_expires_at - timezone.now()).total_seconds()
                        health_status['time_remaining'] = max(0, int(time_remaining))
                    
                    # Generate recommendations
                    health_status['recommendations'] = self._generate_session_recommendations(session)
                
                except TikTokSession.DoesNotExist:
                    pass
            
            return health_status
        
        except TikTokUserAccount.DoesNotExist:
            logger.error(f"Account {account_id} not found")
            return {'is_valid': False, 'error': 'Account not found'}
        except Exception as e:
            logger.error(f"Error validating session health: {str(e)}")
            return {'is_valid': False, 'error': str(e)}
    
    def cleanup_expired_sessions(self) -> int:
        """
        Clean up expired sessions
        
        Returns:
            int: Number of sessions cleaned up
        """
        try:
            expired_count = 0
            
            # Find expired accounts
            expired_accounts = TikTokUserAccount.objects.filter(
                session_expires_at__lt=timezone.now(),
                is_session_valid=True
            )
            
            for account in expired_accounts:
                if self.invalidate_session(account.id):
                    expired_count += 1
            
            # Clean up old session records
            old_sessions = TikTokSession.objects.filter(
                last_activity__lt=timezone.now() - timedelta(days=30),
                is_active=False
            )
            
            deleted_sessions = old_sessions.count()
            old_sessions.delete()
            
            logger.info(f"Cleaned up {expired_count} expired sessions and {deleted_sessions} old records")
            return expired_count + deleted_sessions
        
        except Exception as e:
            logger.error(f"Error cleaning up sessions: {str(e)}")
            return 0
    
    def get_session_statistics(self) -> Dict[str, Any]:
        """
        Get session statistics
        
        Returns:
            dict: Session statistics
        """
        try:
            stats = {
                'total_accounts': TikTokUserAccount.objects.count(),
                'active_sessions': TikTokUserAccount.objects.filter(is_session_valid=True).count(),
                'expired_sessions': TikTokUserAccount.objects.filter(
                    session_expires_at__lt=timezone.now(),
                    is_session_valid=True
                ).count(),
                'recent_logins': TikTokUserAccount.objects.filter(
                    last_login_at__gte=timezone.now() - timedelta(hours=24)
                ).count(),
                'avg_session_quality': 0.0,
                'avg_anti_bot_score': 0.0,
                'total_login_attempts': 0,
                'total_captcha_encounters': 0
            }
            
            # Calculate averages from session records
            active_sessions = TikTokSession.objects.filter(is_active=True)
            
            if active_sessions.exists():
                stats['avg_session_quality'] = active_sessions.aggregate(
                    avg_quality=models.Avg('session_quality_score')
                )['avg_quality'] or 0.0
                
                stats['avg_anti_bot_score'] = active_sessions.aggregate(
                    avg_anti_bot=models.Avg('anti_bot_score')
                )['avg_anti_bot'] or 0.0
                
                stats['total_login_attempts'] = active_sessions.aggregate(
                    total_attempts=models.Sum('login_attempts')
                )['total_attempts'] or 0
                
                stats['total_captcha_encounters'] = active_sessions.aggregate(
                    total_captcha=models.Sum('captcha_encounters')
                )['total_captcha'] or 0
            
            return stats
        
        except Exception as e:
            logger.error(f"Error getting session statistics: {str(e)}")
            return {}
    
    def _encrypt_session_data(self, session_data: Dict[str, Any]) -> str:
        """
        Encrypt session data
        
        Args:
            session_data: Session data to encrypt
        
        Returns:
            str: Encrypted session data
        """
        try:
            json_data = json.dumps(session_data)
            encrypted_data = self.cipher_suite.encrypt(json_data.encode())
            return encrypted_data.decode()
        
        except Exception as e:
            logger.error(f"Error encrypting session data: {str(e)}")
            raise
    
    def _decrypt_session_data(self, encrypted_data: str) -> Optional[Dict[str, Any]]:
        """
        Decrypt session data
        
        Args:
            encrypted_data: Encrypted session data
        
        Returns:
            dict: Decrypted session data or None if failed
        """
        try:
            if not encrypted_data:
                return None
            
            decrypted_data = self.cipher_suite.decrypt(encrypted_data.encode())
            return json.loads(decrypted_data.decode())
        
        except Exception as e:
            logger.error(f"Error decrypting session data: {str(e)}")
            return None
    
    def _is_session_valid(self, account: TikTokUserAccount) -> bool:
        """
        Check if session is still valid
        
        Args:
            account: TikTokUserAccount instance
        
        Returns:
            bool: True if valid, False otherwise
        """
        try:
            if not account.is_session_valid:
                return False
            
            if not account.session_expires_at:
                return False
            
            if account.session_expires_at < timezone.now():
                return False
            
            return True
        
        except Exception as e:
            logger.error(f"Error checking session validity: {str(e)}")
            return False
    
    def _update_session_activity(self, account_id: int):
        """
        Update session last activity timestamp
        
        Args:
            account_id: TikTokUserAccount ID
        """
        try:
            account = TikTokUserAccount.objects.get(id=account_id)
            
            # Update session record
            try:
                session = TikTokSession.objects.get(account=account)
                session.last_activity = timezone.now()
                session.save()
            except TikTokSession.DoesNotExist:
                pass
        
        except Exception as e:
            logger.error(f"Error updating session activity: {str(e)}")
    
    def _calculate_session_quality(self, session_data: Dict[str, Any]) -> float:
        """
        Calculate session quality score
        
        Args:
            session_data: Session data
        
        Returns:
            float: Quality score (0-100)
        """
        try:
            score = 0.0  # Start with 0 and use config weights
            
            # Get quality indicators from config
            quality_indicators = SESSION_QUALITY_METRICS['quality_indicators']
            
            # Check for essential cookies using config
            cookies = session_data.get('cookies', {})
            required_cookies = SESSION_QUALITY_METRICS['required_cookies']
            if cookies and required_cookies:
                cookie_score = sum(1 for cookie in required_cookies if cookie in cookies) / len(required_cookies)
                score += cookie_score * quality_indicators['has_valid_cookies'] * 100
            
            # Check for user information
            if session_data.get('user_info'):
                score += quality_indicators['has_user_info'] * 100
            
            # Check for local storage
            if 'local_storage' in session_data and session_data['local_storage']:
                score += 15
            
            # Check for session storage
            if 'session_storage' in session_data and session_data['session_storage']:
                score += 10
            
            # Anti-bot score bonus
            anti_bot_score = session_data.get('anti_bot_score', 0)
            if anti_bot_score > 80:
                score += 5
            elif anti_bot_score < 50:
                score -= 10
            
            # Check for error indicators using config
            if not session_data.get('has_errors', False):
                score += quality_indicators['no_error_indicators'] * 100
            
            # Captcha penalty
            captcha_count = session_data.get('captcha_count', 0)
            score -= min(20, captcha_count * 5)
            
            return max(0, min(100, score))
        
        except Exception as e:
            logger.error(f"Error calculating session quality: {str(e)}")
            return 0.0
    
    def _generate_session_recommendations(self, session: TikTokSession) -> List[str]:
        """
        Generate recommendations for session improvement
        
        Args:
            session: TikTokSession instance
        
        Returns:
            list: List of recommendations
        """
        recommendations = []
        
        try:
            # Quality score recommendations
            if session.session_quality_score < 70:
                recommendations.append("Consider refreshing session data for better quality")
            
            # Anti-bot score recommendations
            if session.anti_bot_score < 60:
                recommendations.append("Use more anti-detection measures to improve bot score")
            
            # Activity recommendations
            if session.last_activity < timezone.now() - timedelta(hours=6):
                recommendations.append("Session has been inactive for a while, consider refreshing")
            
            # Captcha recommendations
            if session.captcha_encounters > 3:
                recommendations.append("High captcha encounters detected, consider changing approach")
            
            # Login attempts recommendations
            if session.login_attempts > 5:
                recommendations.append("Multiple login attempts detected, account may be flagged")
            
            return recommendations
        
        except Exception as e:
            logger.error(f"Error generating recommendations: {str(e)}")
            return []
    
    def export_session_data(self, account_id: int) -> Optional[Dict[str, Any]]:
        """
        Export session data for backup or transfer
        
        Args:
            account_id: TikTokUserAccount ID
        
        Returns:
            dict: Exportable session data or None
        """
        try:
            account = TikTokUserAccount.objects.get(id=account_id)
            session_data = self.load_session(account_id)
            
            if not session_data:
                return None
            
            # Get session metadata
            try:
                session = TikTokSession.objects.get(account=account)
                metadata = {
                    'session_start': session.session_start.isoformat(),
                    'last_activity': session.last_activity.isoformat(),
                    'quality_score': session.session_quality_score,
                    'anti_bot_score': session.anti_bot_score,
                    'login_attempts': session.login_attempts,
                    'captcha_encounters': session.captcha_encounters
                }
            except TikTokSession.DoesNotExist:
                metadata = {}
            
            export_data = {
                'account_id': account_id,
                'username': account.username,
                'session_data': session_data,
                'metadata': metadata,
                'exported_at': timezone.now().isoformat(),
                'export_version': '1.0'
            }
            
            logger.info(f"Session data exported for account {account_id}")
            return export_data
        
        except TikTokUserAccount.DoesNotExist:
            logger.error(f"Account {account_id} not found")
            return None
        except Exception as e:
            logger.error(f"Error exporting session data: {str(e)}")
            return None
    
    def import_session_data(self, export_data: Dict[str, Any]) -> bool:
        """
        Import session data from export
        
        Args:
            export_data: Exported session data
        
        Returns:
            bool: True if imported successfully, False otherwise
        """
        try:
            account_id = export_data.get('account_id')
            session_data = export_data.get('session_data')
            
            if not account_id or not session_data:
                logger.error("Invalid export data format")
                return False
            
            # Verify account exists
            try:
                TikTokUserAccount.objects.get(id=account_id)
            except TikTokUserAccount.DoesNotExist:
                logger.error(f"Account {account_id} not found for import")
                return False
            
            # Import session
            success = self.save_session(account_id, session_data)
            
            if success:
                logger.info(f"Session data imported for account {account_id}")
            
            return success
        
        except Exception as e:
            logger.error(f"Error importing session data: {str(e)}")
            return False

    def create_session_backup(self, account_id: int) -> Optional[str]:
        """
        Create a backup of session data

        Args:
            account_id: TikTokUserAccount ID

        Returns:
            Backup ID or None if failed
        """
        try:
            session_data = self.get_session(account_id)
            if not session_data:
                return None

            # Create backup with timestamp
            backup_id = f"backup_{account_id}_{int(time.time())}"
            backup_data = {
                'account_id': account_id,
                'session_data': session_data,
                'created_at': timezone.now().isoformat(),
                'backup_id': backup_id
            }

            # Encrypt and store backup
            encrypted_backup = self.cipher_suite.encrypt(
                json.dumps(backup_data).encode()
            )

            cache_key = f"session_backup_{backup_id}"
            cache.set(cache_key, encrypted_backup.decode(), timeout=86400 * 30)  # 30 days

            logger.info(f"Session backup created: {backup_id}")
            return backup_id

        except Exception as e:
            logger.error(f"Error creating session backup: {str(e)}")
            return None

    def restore_session_backup(self, backup_id: str) -> bool:
        """
        Restore session from backup

        Args:
            backup_id: Backup ID to restore

        Returns:
            True if successful, False otherwise
        """
        try:
            cache_key = f"session_backup_{backup_id}"
            encrypted_backup = cache.get(cache_key)

            if not encrypted_backup:
                logger.error(f"Backup not found: {backup_id}")
                return False

            # Decrypt backup data
            decrypted_data = self.cipher_suite.decrypt(encrypted_backup.encode())
            backup_data = json.loads(decrypted_data.decode())

            account_id = backup_data['account_id']
            session_data = backup_data['session_data']

            # Restore session
            success = self.save_session(account_id, session_data)

            if success:
                logger.info(f"Session restored from backup: {backup_id}")

            return success

        except Exception as e:
            logger.error(f"Error restoring session backup: {str(e)}")
            return False

    def list_session_backups(self, account_id: int) -> List[Dict]:
        """
        List available backups for an account

        Args:
            account_id: TikTokUserAccount ID

        Returns:
            List of backup information
        """
        try:
            # This is a simplified implementation
            # In production, you might want to store backup metadata separately
            backups = []

            # Search for backups in cache (this is not efficient for large numbers)
            # In production, consider using a separate backup storage system
            for i in range(10):  # Check last 10 potential backups
                backup_id = f"backup_{account_id}_{int(time.time()) - (i * 3600)}"
                cache_key = f"session_backup_{backup_id}"

                if cache.get(cache_key):
                    backups.append({
                        'backup_id': backup_id,
                        'created_at': datetime.fromtimestamp(
                            int(backup_id.split('_')[-1])
                        ).isoformat()
                    })

            return backups

        except Exception as e:
            logger.error(f"Error listing session backups: {str(e)}")
            return []

# Import models for aggregation
from django.db import models
import random
from typing import Tuple

class EnhancedSessionManager(SessionManager):
    """
    Enhanced session manager with session rotation and account health monitoring
    """
    
    def __init__(self):
        super().__init__()
        self.rotation_threshold = SESSION_ROTATION_THRESHOLDS['anti_bot_score']
        self.health_check_interval = 3600  # 1 hour
        self.max_failed_attempts = 3
        # Cooldown periods are now accessed directly from config
    
    def should_rotate_session(self, account_id: int) -> Tuple[bool, str]:
        """
        Determine if session should be rotated based on health metrics
        
        Args:
            account_id: TikTokUserAccount ID
        
        Returns:
            tuple: (should_rotate, reason)
        """
        try:
            health_status = self.validate_session_health(account_id)
            
            if not health_status.get('is_valid', False):
                return True, "Session invalid or expired"
            
            # Check anti-bot score
            anti_bot_score = health_status.get('anti_bot_score', 0) / 100.0
            if anti_bot_score < self.rotation_threshold:
                return True, f"Low anti-bot score: {anti_bot_score:.2f}"
            
            # Check session quality
            quality_score = health_status.get('quality_score', 0) / 100.0
            if quality_score < SESSION_ROTATION_THRESHOLDS['session_quality']:
                return True, f"Low quality score: {quality_score:.2f}"
            
            # Check account status
            account = TikTokUserAccount.objects.get(id=account_id)
            
            # Check if account is blocked
            if account.is_blocked:
                if account.blocked_until and account.blocked_until > timezone.now():
                    return True, "Account temporarily blocked"
            
            # Check recent login attempts
            if account.login_attempts >= self.max_failed_attempts:
                return True, f"Too many failed attempts: {account.login_attempts}"
            
            # Check session age
            if account.session_expires_at:
                time_remaining = (account.session_expires_at - timezone.now()).total_seconds()
                if time_remaining < 3600:  # Less than 1 hour remaining
                    return True, "Session expiring soon"
            
            return False, "Session healthy"
        
        except Exception as e:
            logger.error(f"Error checking session rotation: {str(e)}")
            return True, f"Error checking session: {str(e)}"
    
    def rotate_session_if_needed(self, account_id: int) -> Dict[str, Any]:
        """
        Rotate session if needed based on health metrics
        
        Args:
            account_id: TikTokUserAccount ID
        
        Returns:
            dict: Rotation result
        """
        try:
            should_rotate, reason = self.should_rotate_session(account_id)
            
            result = {
                'rotated': False,
                'reason': reason,
                'new_session_created': False,
                'cooldown_applied': False,
                'recommendations': []
            }
            
            if should_rotate:
                logger.info(f"Rotating session for account {account_id}: {reason}")
                
                # Invalidate current session
                self.invalidate_session(account_id)
                
                # Apply cooldown if needed
                cooldown_applied = self._apply_cooldown_if_needed(account_id, reason)
                result['cooldown_applied'] = cooldown_applied
                
                # Reset login attempts if session was rotated due to quality issues
                if 'score' in reason.lower():
                    account = TikTokUserAccount.objects.get(id=account_id)
                    account.reset_login_attempts()
                
                result['rotated'] = True
                result['recommendations'] = self._get_rotation_recommendations(reason)
            
            return result
        
        except Exception as e:
            logger.error(f"Error rotating session: {str(e)}")
            return {
                'rotated': False,
                'reason': f"Error: {str(e)}",
                'error': True
            }
    
    def get_account_health_score(self, account_id: int) -> Dict[str, Any]:
        """
        Calculate comprehensive account health score
        
        Args:
            account_id: TikTokUserAccount ID
        
        Returns:
            dict: Health score and metrics
        """
        try:
            account = TikTokUserAccount.objects.get(id=account_id)
            session_health = self.validate_session_health(account_id)
            
            # Base metrics
            metrics = {
                'overall_score': 0.0,
                'session_quality': session_health.get('quality_score', 0) / 100.0,
                'anti_bot_score': session_health.get('anti_bot_score', 0) / 100.0,
                'login_success_rate': 0.0,
                'account_age_score': 0.0,
                'activity_score': 0.0,
                'risk_level': 'unknown',
                'recommendations': []
            }
            
            # Calculate login success rate
            total_attempts = max(account.login_attempts, 1)
            failed_attempts = account.login_attempts
            success_rate = max(0, (total_attempts - failed_attempts) / total_attempts)
            metrics['login_success_rate'] = success_rate
            
            # Calculate account age score (newer accounts are riskier)
            if account.created_at:
                account_age_days = (timezone.now() - account.created_at).days
                age_score = min(1.0, account_age_days / 30.0)  # Max score after 30 days
                metrics['account_age_score'] = age_score
            
            # Calculate activity score
            if account.last_login_at:
                hours_since_login = (timezone.now() - account.last_login_at).total_seconds() / 3600
                activity_score = max(0, 1.0 - (hours_since_login / 168))  # Decay over 1 week
                metrics['activity_score'] = activity_score
            
            # Calculate overall score (weighted average)
            weights = HEALTH_SCORE_WEIGHTS
            
            overall_score = sum(
                metrics[key] * weight 
                for key, weight in weights.items() 
                if key in metrics
            )
            metrics['overall_score'] = overall_score
            
            # Determine risk level
            for level, threshold in RISK_LEVELS.items():
                if overall_score >= threshold:
                    metrics['risk_level'] = level
                    break
            else:
                metrics['risk_level'] = 'critical'
            
            # Generate recommendations
            metrics['recommendations'] = self._generate_health_recommendations(metrics, account)
            
            return metrics
        
        except Exception as e:
            logger.error(f"Error calculating health score: {str(e)}")
            return {'overall_score': 0.0, 'error': str(e)}
    
    def select_best_account_for_task(self, available_accounts: List[int]) -> Optional[int]:
        """
        Select the best account for a task based on health scores
        
        Args:
            available_accounts: List of account IDs
        
        Returns:
            int: Best account ID or None
        """
        try:
            if not available_accounts:
                return None
            
            account_scores = []
            
            for account_id in available_accounts:
                try:
                    # Check if account is blocked
                    account = TikTokUserAccount.objects.get(id=account_id)
                    if account.is_blocked and account.blocked_until and account.blocked_until > timezone.now():
                        continue
                    
                    health = self.get_account_health_score(account_id)
                    if health.get('overall_score', 0) > 0:
                        account_scores.append((account_id, health['overall_score']))
                
                except Exception as e:
                    logger.warning(f"Error evaluating account {account_id}: {str(e)}")
                    continue
            
            if not account_scores:
                return None
            
            # Sort by score (highest first) and return best account
            account_scores.sort(key=lambda x: x[1], reverse=True)
            best_account_id = account_scores[0][0]
            
            logger.info(f"Selected account {best_account_id} with score {account_scores[0][1]:.2f}")
            return best_account_id
        
        except Exception as e:
            logger.error(f"Error selecting best account: {str(e)}")
            return available_accounts[0] if available_accounts else None
    
    def _apply_cooldown_if_needed(self, account_id: int, reason: str) -> bool:
        """
        Apply cooldown period based on failure reason
        
        Args:
            account_id: TikTokUserAccount ID
            reason: Reason for rotation
        
        Returns:
            bool: True if cooldown applied
        """
        try:
            account = TikTokUserAccount.objects.get(id=account_id)
            
            # Determine cooldown period based on reason using config
            cooldown_duration = COOLDOWN_PERIODS.get('general_failure', timedelta(minutes=10))
            
            if 'bot' in reason.lower() or 'detection' in reason.lower():
                cooldown_duration = COOLDOWN_PERIODS['bot_detection']
            elif 'rate' in reason.lower() or 'limit' in reason.lower():
                cooldown_duration = COOLDOWN_PERIODS['rate_limiting']
            elif 'captcha' in reason.lower():
                cooldown_duration = COOLDOWN_PERIODS['captcha_timeout']
            elif 'attempt' in reason.lower():
                cooldown_duration = COOLDOWN_PERIODS['general_failure']
            
            # Apply cooldown
            account.is_blocked = True
            account.blocked_until = timezone.now() + cooldown_duration
            account.save()
            
            logger.info(f"Applied {cooldown_duration.total_seconds()/60:.1f} minute cooldown to account {account_id}: {reason}")
            return True
        
        except Exception as e:
            logger.error(f"Error applying cooldown: {str(e)}")
            return False
    
    def _get_rotation_recommendations(self, reason: str) -> List[str]:
        """
        Get recommendations based on rotation reason
        
        Args:
            reason: Rotation reason
        
        Returns:
            list: Recommendations
        """
        recommendations = []
        
        if 'anti-bot' in reason.lower() or 'bot' in reason.lower():
            recommendations.extend([
                "Use different browser fingerprint",
                "Rotate IP address or use proxy",
                "Increase human behavior simulation",
                "Wait longer between actions"
            ])
        
        elif 'quality' in reason.lower():
            recommendations.extend([
                "Refresh session cookies and storage",
                "Perform more human-like browsing",
                "Update browser user agent"
            ])
        
        elif 'attempt' in reason.lower():
            recommendations.extend([
                "Wait before next login attempt",
                "Use different login approach",
                "Check account credentials"
            ])
        
        elif 'expiring' in reason.lower():
            recommendations.extend([
                "Refresh session proactively",
                "Perform light activity to extend session"
            ])
        
        return recommendations
    
    def _generate_health_recommendations(self, metrics: Dict[str, Any], account: TikTokUserAccount) -> List[str]:
        """
        Generate health improvement recommendations
        
        Args:
            metrics: Health metrics
            account: TikTokUserAccount instance
        
        Returns:
            list: Recommendations
        """
        recommendations = []
        
        # Session quality recommendations
        if metrics.get('session_quality', 0) < 0.7:
            recommendations.append("Improve session quality by refreshing cookies and storage")
        
        # Anti-bot score recommendations
        if metrics.get('anti_bot_score', 0) < 0.6:
            recommendations.append("Enhance anti-detection measures and human behavior simulation")
        
        # Login success rate recommendations
        if metrics.get('login_success_rate', 0) < 0.8:
            recommendations.append("Review login process and reduce failed attempts")
        
        # Activity recommendations
        if metrics.get('activity_score', 0) < 0.5:
            recommendations.append("Increase account activity to maintain session health")
        
        # Risk level recommendations
        risk_level = metrics.get('risk_level', 'unknown')
        if risk_level == 'critical':
            recommendations.append("Account requires immediate attention - consider rotation")
        elif risk_level == 'high':
            recommendations.append("Account needs improvement - monitor closely")
        
        return recommendations