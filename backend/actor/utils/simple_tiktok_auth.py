#!/usr/bin/env python3
"""
Simple TikTok Authenticator

A focused, working TikTok login implementation without complex strategies.
Designed to actually work with real TikTok accounts.
"""

import time
import random
import logging
from typing import Dict, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import undetected_chromedriver as uc

logger = logging.getLogger(__name__)

class SimpleTikTokAuthenticator:
    """
    Simple, focused TikTok authenticator that actually works
    """
    
    def __init__(self):
        """Initialize simple authenticator"""
        self.base_url = "https://www.tiktok.com"
        self.login_url = "https://www.tiktok.com/login/phone-or-email/email"
    
    def login(self, username: str, password: str) -> Dict:
        """
        Simple, working TikTok login
        
        Args:
            username: TikTok username or email
            password: Tik<PERSON>ok password
            
        Returns:
            Dict with success status and session info
        """
        logger.info(f"🚀 Starting simple TikTok login for: {username}")
        
        driver = None
        try:
            # Setup undetected Chrome with minimal configuration
            logger.info("Setting up Chrome driver...")
            options = uc.ChromeOptions()
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-blink-features=AutomationControlled")

            # Create driver with minimal options
            driver = uc.Chrome(options=options, version_main=None)

            # Execute anti-detection script
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Navigate to TikTok homepage first
            logger.info("Navigating to TikTok homepage...")
            driver.get(self.base_url)
            time.sleep(random.uniform(3, 5))
            
            # Handle any initial popups
            self._handle_popups(driver)
            
            # Navigate to login page
            logger.info("Navigating to login page...")
            driver.get(self.login_url)
            time.sleep(random.uniform(3, 5))
            
            # Handle popups again
            self._handle_popups(driver)
            
            # Wait for login form
            logger.info("Waiting for login form...")
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[type="text"], input[name="username"]'))
            )
            
            # Find and fill username
            logger.info("Finding username field...")
            username_field = self._find_username_field(driver)
            if not username_field:
                return {'success': False, 'error': 'Username field not found'}
            
            logger.info("Filling username...")
            self._human_type(username_field, username)
            time.sleep(random.uniform(1, 2))
            
            # Find and fill password
            logger.info("Finding password field...")
            password_field = self._find_password_field(driver)
            if not password_field:
                return {'success': False, 'error': 'Password field not found'}
            
            logger.info("Filling password...")
            self._human_type(password_field, password)
            time.sleep(random.uniform(1, 2))
            
            # Submit form
            logger.info("Submitting login form...")
            success = self._submit_form(driver, password_field)
            if not success:
                return {'success': False, 'error': 'Could not submit form'}
            
            # Wait for login result
            logger.info("Waiting for login result...")
            time.sleep(10)
            
            # Check if login was successful
            current_url = driver.current_url
            logger.info(f"Current URL after login: {current_url}")
            
            # Check for success indicators
            if self._check_login_success(driver):
                logger.info("✅ Login successful!")
                
                # Get session data
                cookies = driver.get_cookies()
                session_info = {
                    'cookies': cookies,
                    'user_agent': driver.execute_script("return navigator.userAgent;"),
                    'current_url': current_url
                }
                
                return {
                    'success': True,
                    'session_info': session_info,
                    'message': 'Login successful'
                }
            else:
                # Check for error messages
                error_msg = self._get_error_message(driver)
                logger.warning(f"❌ Login failed: {error_msg}")
                
                return {
                    'success': False,
                    'error': error_msg or 'Login failed - unknown reason',
                    'current_url': current_url
                }
                
        except Exception as e:
            logger.error(f"❌ Login failed with exception: {str(e)}")
            return {'success': False, 'error': str(e)}
            
        finally:
            if driver:
                try:
                    # Keep browser open for a moment to see result
                    logger.info("Keeping browser open for 10 seconds...")
                    time.sleep(10)
                    driver.quit()
                except:
                    pass
    
    def _handle_popups(self, driver):
        """Handle common popups and overlays"""
        try:
            # Cookie consent
            cookie_selectors = [
                '[data-testid="cookie-consent-accept"]',
                'button[aria-label="Accept"]',
                'button:contains("Accept")',
                '.cookie-accept'
            ]
            
            for selector in cookie_selectors:
                try:
                    if ':contains(' in selector:
                        element = driver.find_element(By.XPATH, "//button[contains(text(), 'Accept')]")
                    else:
                        element = driver.find_element(By.CSS_SELECTOR, selector)
                    element.click()
                    logger.info("Clicked cookie consent")
                    time.sleep(1)
                    break
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"No popups to handle: {str(e)}")
    
    def _find_username_field(self, driver):
        """Find username/email field"""
        selectors = [
            'input[name="username"]',
            'input[type="text"]',
            'input[placeholder*="email"]',
            'input[placeholder*="Email"]',
            'input[placeholder*="username"]',
            'input[placeholder*="Username"]'
        ]
        
        for selector in selectors:
            try:
                element = driver.find_element(By.CSS_SELECTOR, selector)
                logger.info(f"Found username field with: {selector}")
                return element
            except:
                continue
        return None
    
    def _find_password_field(self, driver):
        """Find password field"""
        selectors = [
            'input[type="password"]',
            'input[name="password"]'
        ]
        
        for selector in selectors:
            try:
                element = driver.find_element(By.CSS_SELECTOR, selector)
                logger.info(f"Found password field with: {selector}")
                return element
            except:
                continue
        return None
    
    def _human_type(self, element, text):
        """Type text in human-like manner"""
        element.clear()
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.05, 0.15))
    
    def _submit_form(self, driver, password_field):
        """Submit the login form"""
        try:
            # Try clicking submit button first
            submit_selectors = [
                'button[type="submit"]',
                'button[data-testid="login-submit"]',
                'input[type="submit"]'
            ]
            
            for selector in submit_selectors:
                try:
                    button = driver.find_element(By.CSS_SELECTOR, selector)
                    button.click()
                    logger.info(f"Clicked submit button: {selector}")
                    return True
                except:
                    continue
            
            # Fallback: press Enter on password field
            password_field.send_keys(Keys.RETURN)
            logger.info("Pressed Enter on password field")
            return True
            
        except Exception as e:
            logger.error(f"Could not submit form: {str(e)}")
            return False
    
    def _check_login_success(self, driver):
        """Check if login was successful"""
        try:
            current_url = driver.current_url
            
            # Success indicators
            success_indicators = [
                'foryou' in current_url.lower(),
                'following' in current_url.lower(),
                '/home' in current_url.lower(),
                driver.current_url == self.base_url
            ]
            
            if any(success_indicators):
                return True
            
            # Check for user profile elements
            profile_selectors = [
                '[data-testid="avatar"]',
                '[data-e2e="profile-icon"]',
                '.avatar'
            ]
            
            for selector in profile_selectors:
                try:
                    driver.find_element(By.CSS_SELECTOR, selector)
                    return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.debug(f"Error checking login success: {str(e)}")
            return False
    
    def _get_error_message(self, driver):
        """Get error message from page"""
        try:
            error_selectors = [
                '.error-message',
                '[data-testid="error-message"]',
                '.login-error',
                '.form-error'
            ]
            
            for selector in error_selectors:
                try:
                    element = driver.find_element(By.CSS_SELECTOR, selector)
                    return element.text
                except:
                    continue
            
            # Check page text for common error messages
            page_text = driver.page_source.lower()
            if 'maximum number of attempts' in page_text:
                return 'Maximum number of attempts reached. Try again later.'
            elif 'incorrect' in page_text:
                return 'Incorrect username or password'
            elif 'captcha' in page_text:
                return 'Captcha verification required'
            
            return None
            
        except Exception as e:
            logger.debug(f"Error getting error message: {str(e)}")
            return None
