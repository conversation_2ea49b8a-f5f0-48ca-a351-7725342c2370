"""
Compatibility module for handling Python version differences
"""

import sys

# Handle distutils compatibility for Python 3.13+
try:
    from distutils.version import LooseVersion
except ImportError:
    # For Python 3.13+ where distutils was removed
    try:
        from packaging.version import Version as LooseVersion
    except ImportError:
        # Fallback implementation
        class LooseVersion:
            def __init__(self, version_string):
                self.version_string = str(version_string)
                self.version = self._parse_version(version_string)
            
            def _parse_version(self, version_string):
                # Simple version parsing
                parts = []
                for part in str(version_string).split('.'):
                    try:
                        parts.append(int(part))
                    except ValueError:
                        # Handle non-numeric parts
                        parts.append(part)
                return parts
            
            def __lt__(self, other):
                return self.version < other.version
            
            def __le__(self, other):
                return self.version <= other.version
            
            def __gt__(self, other):
                return self.version > other.version
            
            def __ge__(self, other):
                return self.version >= other.version
            
            def __eq__(self, other):
                return self.version == other.version
            
            def __ne__(self, other):
                return self.version != other.version
            
            def __str__(self):
                return self.version_string

# Monkey patch distutils if needed
if sys.version_info >= (3, 13):
    import sys
    import types
    
    # Create a fake distutils module
    distutils_module = types.ModuleType('distutils')
    version_module = types.ModuleType('distutils.version')
    version_module.LooseVersion = LooseVersion
    distutils_module.version = version_module
    
    sys.modules['distutils'] = distutils_module
    sys.modules['distutils.version'] = version_module
