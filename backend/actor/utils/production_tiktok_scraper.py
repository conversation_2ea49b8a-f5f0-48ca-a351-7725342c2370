#!/usr/bin/env python3
"""
Production TikTok Scraper for Prabowo Content

This is the production-ready version that integrates with Django models
and can be used for continuous Prabowo content monitoring.
"""

import logging
import time
import random
from datetime import datetime
from typing import Dict, List, Optional
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import undetected_chromedriver as uc

from .simple_tiktok_auth import SimpleTikTokAuthenticator

logger = logging.getLogger(__name__)

class ProductionTikTokScraper:
    """
    Production-ready TikTok scraper for Prabowo content
    """
    
    def __init__(self):
        """Initialize production scraper"""
        self.authenticator = SimpleTikTokAuthenticator()
        self.default_keywords = ['prabowo', 'Prabowo', 'PRABOWO']
        
    def scrape_prabowo_content(self, username: str, password: str, 
                              max_videos: int = 20) -> Dict:
        """
        Scrape Prabowo content from TikTok
        
        Args:
            username: Tik<PERSON>ok username
            password: TikTok password
            max_videos: Maximum number of videos to scrape
            
        Returns:
            Dict with scraping results
        """
        logger.info(f"🚀 Starting production Prabowo scraping for: {username}")
        
        try:
            # Step 1: Login
            logger.info("Step 1: Authenticating with TikTok...")
            login_result = self.authenticator.login(username, password)
            
            if not login_result['success']:
                return {
                    'success': False,
                    'error': f"Login failed: {login_result.get('error')}",
                    'videos': []
                }
            
            logger.info("✅ Authentication successful!")
            
            # Step 2: Setup scraping session
            logger.info("Step 2: Setting up scraping session...")
            driver = self._setup_scraping_driver(login_result['session_info'])
            
            if not driver:
                return {
                    'success': False,
                    'error': "Failed to setup scraping session",
                    'videos': []
                }
            
            try:
                # Step 3: Search for content
                logger.info("Step 3: Searching for content...")
                videos = self._search_and_extract_videos(driver, max_videos, self.default_keywords)
                
                logger.info(f"✅ Successfully scraped {len(videos)} Prabowo videos!")
                
                return {
                    'success': True,
                    'videos': videos,
                    'total_found': len(videos),
                    'timestamp': datetime.now().isoformat()
                }
                
            finally:
                driver.quit()
                
        except Exception as e:
            logger.error(f"❌ Production scraping failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'videos': []
            }

    def scrape_content_by_keyword(self, username: str, password: str,
                                 keyword: str, max_videos: int = 20,
                                 filters: Dict = None) -> Dict:
        """
        Scrape content by any keyword (dynamic search)

        Args:
            username: TikTok username
            password: TikTok password
            keyword: Search keyword
            max_videos: Maximum number of videos to scrape
            filters: Additional filters for search

        Returns:
            Dict with scraping results
        """
        logger.info(f"🚀 Starting dynamic scraping for keyword: {keyword}")

        try:
            # Step 1: Login
            logger.info("Step 1: Authenticating with TikTok...")
            login_result = self.authenticator.login(username, password)

            if not login_result['success']:
                return {
                    'success': False,
                    'error': f"Login failed: {login_result.get('error')}",
                    'videos': []
                }

            logger.info("✅ Authentication successful!")

            # Step 2: Setup scraping session
            logger.info("Step 2: Setting up scraping session...")
            driver = self._setup_scraping_driver(login_result['session_info'])

            if not driver:
                return {
                    'success': False,
                    'error': "Failed to setup scraping session",
                    'videos': []
                }

            try:
                # Step 3: Search for keyword content
                logger.info(f"Step 3: Searching for '{keyword}' content...")
                videos = self._search_and_extract_videos(driver, max_videos, [keyword])

                logger.info(f"✅ Successfully scraped {len(videos)} videos for keyword: {keyword}")

                return {
                    'success': True,
                    'videos': videos,
                    'total_found': len(videos),
                    'keyword': keyword,
                    'timestamp': datetime.now().isoformat()
                }

            finally:
                driver.quit()

        except Exception as e:
            logger.error(f"❌ Dynamic scraping failed for {keyword}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'videos': []
            }
    
    def _setup_scraping_driver(self, session_info: Dict):
        """Setup Chrome driver with session cookies"""
        try:
            options = uc.ChromeOptions()
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            
            driver = uc.Chrome(options=options, version_main=None)
            
            # Navigate to TikTok and add cookies
            driver.get("https://www.tiktok.com")
            time.sleep(3)
            
            # Add session cookies
            cookies = session_info.get('cookies', [])
            for cookie in cookies:
                try:
                    driver.add_cookie(cookie)
                except:
                    continue
            
            # Refresh to apply cookies
            driver.refresh()
            time.sleep(3)
            
            return driver
            
        except Exception as e:
            logger.error(f"Failed to setup scraping driver: {str(e)}")
            return None
    
    def _search_and_extract_videos(self, driver, max_videos: int, keywords: List[str] = None) -> List[Dict]:
        """Search for content and extract video data"""
        videos = []

        # Use provided keywords or default ones
        search_keywords = keywords or self.default_keywords

        for keyword in search_keywords:
            if len(videos) >= max_videos:
                break
                
            logger.info(f"Searching for keyword: {keyword}")
            
            # Navigate to search
            search_url = f"https://www.tiktok.com/search?q={keyword}&t=videos"
            driver.get(search_url)
            time.sleep(5)
            
            # Extract videos from this search
            keyword_videos = self._extract_videos_from_page(driver, max_videos - len(videos))
            videos.extend(keyword_videos)
            
            # Random delay between searches
            time.sleep(random.uniform(3, 6))
        
        return videos[:max_videos]
    
    def _extract_videos_from_page(self, driver, max_videos: int) -> List[Dict]:
        """Extract video data from current page"""
        videos = []
        
        try:
            # Find video elements
            video_selectors = [
                'a[href*="/video/"]',
                '[data-e2e="search-result"]',
                '[data-e2e="user-post-item"]'
            ]
            
            video_elements = []
            for selector in video_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        video_elements = elements[:max_videos]
                        logger.info(f"Found {len(video_elements)} videos with selector: {selector}")
                        break
                except:
                    continue
            
            # Extract data from each video
            for i, element in enumerate(video_elements):
                try:
                    video_data = self._extract_video_data(element, i + 1)
                    if video_data:
                        videos.append(video_data)
                except Exception as e:
                    logger.debug(f"Error extracting video {i+1}: {str(e)}")
                    continue
            
        except Exception as e:
            logger.error(f"Error extracting videos from page: {str(e)}")
        
        return videos
    
    def _extract_video_data(self, element, index: int) -> Optional[Dict]:
        """Extract data from a single video element"""
        try:
            video_data = {
                'index': index,
                'extracted_at': datetime.now().isoformat()
            }
            
            # Extract video URL
            try:
                link = element.get_attribute('href')
                if not link:
                    link = element.find_element(By.CSS_SELECTOR, 'a').get_attribute('href')
                video_data['url'] = link
                
                # Extract video ID from URL
                if '/video/' in link:
                    video_id = link.split('/video/')[-1].split('?')[0]
                    video_data['video_id'] = video_id
                    
            except:
                video_data['url'] = None
                video_data['video_id'] = None
            
            # Extract author username
            try:
                author_selectors = [
                    '[data-e2e="search-card-user-unique-id"]',
                    '.author-uniqueId',
                    'span[data-e2e="search-card-user-unique-id"]'
                ]
                
                for selector in author_selectors:
                    try:
                        author_elem = element.find_element(By.CSS_SELECTOR, selector)
                        video_data['author'] = author_elem.text.strip()
                        break
                    except:
                        continue
                
                if 'author' not in video_data and video_data.get('url'):
                    # Extract from URL
                    url_parts = video_data['url'].split('/')
                    for part in url_parts:
                        if part.startswith('@'):
                            video_data['author'] = part
                            break
                            
            except:
                video_data['author'] = None
            
            # Extract description/title
            try:
                desc_selectors = [
                    '[data-e2e="search-card-desc"]',
                    '[data-e2e="video-desc"]',
                    '.video-meta-title',
                    'h3'
                ]
                
                for selector in desc_selectors:
                    try:
                        desc_elem = element.find_element(By.CSS_SELECTOR, selector)
                        description = desc_elem.text.strip()
                        if description:
                            video_data['description'] = description
                            break
                    except:
                        continue
                        
            except:
                video_data['description'] = None
            
            # Extract engagement metrics
            try:
                metrics = {}
                metric_selectors = [
                    '[data-e2e="like-count"]',
                    '[data-e2e="comment-count"]',
                    '[data-e2e="share-count"]',
                    '.video-count'
                ]
                
                for selector in metric_selectors:
                    try:
                        metric_elements = element.find_elements(By.CSS_SELECTOR, selector)
                        for metric_elem in metric_elements:
                            metric_text = metric_elem.text.strip()
                            if metric_text and any(char.isdigit() for char in metric_text):
                                if 'likes' not in metrics:
                                    metrics['likes'] = metric_text
                                elif 'comments' not in metrics:
                                    metrics['comments'] = metric_text
                                elif 'shares' not in metrics:
                                    metrics['shares'] = metric_text
                    except:
                        continue
                
                video_data['metrics'] = metrics
                
            except:
                video_data['metrics'] = {}
            
            # Only return if we have essential data
            if video_data.get('url') or video_data.get('video_id'):
                logger.info(f"✅ Extracted video {index}: {video_data.get('author', 'Unknown')} - {video_data.get('url', 'No URL')}")
                return video_data
            else:
                return None
                
        except Exception as e:
            logger.debug(f"Error extracting video data: {str(e)}")
            return None
    
    def save_to_database(self, videos: List[Dict], user_id: int, account_id: int):
        """Save scraped videos to database"""
        try:
            from django.contrib.auth.models import User
            from actor_tiktok.models import TikTokUserAccount, ActorScrapedData
            
            user = User.objects.get(id=user_id)
            account = TikTokUserAccount.objects.get(id=account_id)
            
            saved_count = 0
            for video in videos:
                try:
                    # Check if video already exists
                    video_id = video.get('video_id')
                    if video_id and ActorScrapedData.objects.filter(
                        tiktok_video_id=video_id
                    ).exists():
                        logger.debug(f"Video {video_id} already exists, skipping")
                        continue
                    
                    # Create new record
                    scraped_data = ActorScrapedData.objects.create(
                        user=user,
                        tiktok_account=account,
                        tiktok_video_id=video_id,
                        tiktok_video_url=video.get('url'),
                        tiktok_author=video.get('author'),
                        tiktok_description=video.get('description'),
                        tiktok_likes=self._parse_metric(video.get('metrics', {}).get('likes')),
                        tiktok_comments=self._parse_metric(video.get('metrics', {}).get('comments')),
                        tiktok_shares=self._parse_metric(video.get('metrics', {}).get('shares')),
                        scraped_at=datetime.now(),
                        raw_data=video
                    )
                    
                    saved_count += 1
                    logger.info(f"✅ Saved video: {video.get('author')} - {video_id}")
                    
                except Exception as e:
                    logger.error(f"Error saving video {video.get('video_id', 'unknown')}: {str(e)}")
                    continue
            
            logger.info(f"✅ Saved {saved_count} new Prabowo videos to database")
            return saved_count
            
        except Exception as e:
            logger.error(f"Error saving to database: {str(e)}")
            return 0
    
    def _parse_metric(self, metric_text: str) -> int:
        """Parse metric text to integer"""
        if not metric_text:
            return 0
        
        try:
            # Remove non-numeric characters except K, M, B
            clean_text = ''.join(c for c in metric_text if c.isdigit() or c in 'KMB.')
            
            if 'K' in clean_text:
                return int(float(clean_text.replace('K', '')) * 1000)
            elif 'M' in clean_text:
                return int(float(clean_text.replace('M', '')) * 1000000)
            elif 'B' in clean_text:
                return int(float(clean_text.replace('B', '')) * 1000000000)
            else:
                return int(clean_text)
                
        except:
            return 0
