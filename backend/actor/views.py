"""
Actor System Views for Frontend Integration

API endpoints to support the frontend Actor system with multi-platform support.
Maintains backward compatibility with existing TikTok functionality.
"""

import logging
from datetime import datetime, timedelta
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.models import User
from django.db import models
from actor.models import TikTokUserAccount, ActorAccount, ActorTask, ActorScrapedData
from actor.utils.simple_tiktok_auth import SimpleTikTokAuthenticator
from actor.utils.production_tiktok_scraper import ProductionTikTokScraper
from actor.services.actor_service import ActorService

logger = logging.getLogger(__name__)

# Initialize Actor service
actor_service = ActorService()

# ============================================================================
# NEW ACTOR SYSTEM ENDPOINTS
# ============================================================================

@api_view(['GET'])
def get_available_platforms(request):
    """Get list of available platforms."""
    try:
        platforms = actor_service.get_available_platforms()
        return Response({
            'success': True,
            'platforms': platforms
        })
    except Exception as e:
        logger.error(f"Error getting platforms: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_actor_account(request):
    """Create a new actor account for a platform."""
    try:
        platform = request.data.get('platform')
        username = request.data.get('username')
        password = request.data.get('password')
        email = request.data.get('email')

        if not all([platform, username, password]):
            return Response({
                'success': False,
                'error': 'Platform, username, and password are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        result = actor_service.create_account(
            user=request.user,
            platform=platform,
            username=username,
            password=password,
            email=email
        )

        if result['success']:
            return Response(result)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Error creating actor account: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def authenticate_actor_account(request):
    """Authenticate an actor account."""
    try:
        account_id = request.data.get('account_id')
        credentials = request.data.get('credentials')
        mock_mode = request.data.get('mock_mode', False)  # Add mock mode option

        if not account_id:
            return Response({
                'success': False,
                'error': 'Account ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # If mock mode is enabled, simulate quick authentication
        if mock_mode:
            result = actor_service.mock_authenticate_account(account_id)
        else:
            result = actor_service.authenticate_account(account_id, credentials)

        if result['success']:
            return Response(result)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Error authenticating account: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_actor_accounts(request):
    """Get all actor accounts for the authenticated user."""
    try:
        platform = request.GET.get('platform')
        accounts = actor_service.get_user_accounts(request.user, platform)

        return Response({
            'success': True,
            'accounts': accounts
        })

    except Exception as e:
        logger.error(f"Error getting actor accounts: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_actor_account_details(request, account_id):
    """Get detailed information about a specific actor account."""
    try:
        result = actor_service.get_account_details(request.user, account_id)

        if result.get('success'):
            return Response(result)
        else:
            return Response(result, status=status.HTTP_404_NOT_FOUND)

    except Exception as e:
        logger.error(f"Error getting account details: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PUT', 'PATCH'])
@permission_classes([IsAuthenticated])
def update_actor_account(request, account_id):
    """Update an actor account."""
    try:
        result = actor_service.update_account(
            user=request.user,
            account_id=account_id,
            update_data=request.data
        )

        if result['success']:
            return Response(result)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Error updating actor account: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_actor_account(request, account_id):
    """Delete an actor account."""
    try:
        # Check for force_delete parameter
        force_delete = request.data.get('force_delete', False) or request.GET.get('force_delete', 'false').lower() == 'true'
        
        result = actor_service.delete_account(
            user=request.user,
            account_id=account_id,
            force_delete=force_delete
        )

        if result['success']:
            return Response(result)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Error deleting actor account: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_actor_task(request):
    """Create a new actor scraping task."""
    try:
        account_id = request.data.get('account_id')
        task_type = request.data.get('task_type')
        task_name = request.data.get('task_name')

        if not all([account_id, task_type, task_name]):
            return Response({
                'success': False,
                'error': 'Account ID, task type, and task name are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Extract additional parameters
        kwargs = {
            'target_identifier': request.data.get('target_identifier'),
            'keywords': request.data.get('keywords'),
            'max_items': request.data.get('max_items', 100),
            'start_date': request.data.get('start_date'),
            'end_date': request.data.get('end_date'),
            'task_parameters': request.data.get('task_parameters', {})
        }

        result = actor_service.create_task(
            user=request.user,
            account_id=account_id,
            task_type=task_type,
            task_name=task_name,
            **kwargs
        )

        if result['success']:
            return Response(result)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Error creating actor task: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def execute_actor_task(request):
    """Execute an actor scraping task with auto-authentication."""
    try:
        task_id = request.data.get('task_id')

        if not task_id:
            return Response({
                'success': False,
                'error': 'Task ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get task and account
        try:
            from .models import ActorTask
            task = ActorTask.objects.get(id=task_id, user=request.user)
            account = task.actor_account
            
            logger.info(f"Executing task {task_id} for account @{account.platform_username} on {account.platform}")
            
            # Check if session is valid, re-authenticate if needed
            if not account.is_session_valid():
                logger.info(f"Session invalid for @{account.platform_username}, attempting re-authentication...")
                
                # Attempt to re-authenticate using stored credentials
                auth_result = actor_service.authenticate_account(
                    account_id=account.id,
                    credentials=None  # Use stored credentials
                )
                
                if not auth_result.get('success'):
                    logger.error(f"Re-authentication failed for @{account.platform_username}: {auth_result.get('error')}")
                    return Response({
                        'success': False,
                        'error': f'Authentication required. Please re-authenticate your {account.platform} account first.',
                        'requires_reauth': True,
                        'account_id': account.id,
                        'platform': account.platform
                    }, status=status.HTTP_401_UNAUTHORIZED)
                else:
                    logger.info(f"Re-authentication successful for @{account.platform_username}")
            
            # Execute task based on platform
            if account.platform in ['twitter', 'tiktok']:
                # Use async execution for Twitter and TikTok tasks
                result = actor_service.execute_task_async(task_id, user=request.user)
            else:
                # Use synchronous execution for other platforms
                result = actor_service.execute_task(task_id, user=request.user)
                
        except ActorTask.DoesNotExist:
            result = {'success': False, 'error': 'Task not found or access denied'}

        if result['success']:
            logger.info(f"Task {task_id} executed successfully")
            return Response(result)
        else:
            logger.error(f"Task {task_id} execution failed: {result.get('error')}")
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Error executing actor task: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_actor_tasks(request):
    """Get user's actor tasks."""
    try:
        platform = request.GET.get('platform')
        status_filter = request.GET.get('status')
        account_id = request.GET.get('account_id')

        tasks = actor_service.get_user_tasks(
            user=request.user,
            platform=platform,
            status=status_filter,
            account_id=account_id
        )

        return Response({
            'success': True,
            'tasks': tasks,
            'count': len(tasks)
        })

    except Exception as e:
        logger.error(f"Error getting actor tasks: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PUT', 'PATCH'])
@permission_classes([IsAuthenticated])
def update_actor_task(request, task_id):
    """Update an actor task."""
    try:
        result = actor_service.update_task(
            user=request.user,
            task_id=task_id,
            update_data=request.data
        )

        if result['success']:
            return Response(result)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Error updating actor task: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_actor_task(request, task_id):
    """Delete an actor task."""
    try:
        result = actor_service.delete_task(
            user=request.user,
            task_id=task_id
        )

        if result['success']:
            return Response(result)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Error deleting actor task: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def migrate_tiktok_account(request):
    """Migrate a TikTok account to the new Actor system."""
    try:
        tiktok_account_id = request.data.get('tiktok_account_id')

        if not tiktok_account_id:
            return Response({
                'success': False,
                'error': 'TikTok account ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        result = actor_service.migrate_tiktok_account(tiktok_account_id)

        if result['success']:
            return Response(result)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Error migrating TikTok account: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_actor_scraped_data(request):
    """Get scraped data with labeling information."""
    try:
        # Get query parameters
        platform = request.GET.get('platform')
        account_id = request.GET.get('account_id')
        task_id = request.GET.get('task_id')
        data_type = request.GET.get('data_type')
        limit = int(request.GET.get('limit', 100))

        # Build query
        query = ActorScrapedData.objects.filter(task__user=request.user)

        if platform:
            query = query.filter(platform=platform)
        if account_id:
            query = query.filter(actor_account_id=account_id)
        if task_id:
            query = query.filter(task_id=task_id)
        if data_type:
            query = query.filter(data_type=data_type)

        # Get data with labeling
        scraped_data = query.order_by('-scraped_at')[:limit]

        results = []
        for data in scraped_data:
            results.append({
                'id': data.id,
                'data_type': data.data_type,
                'platform': data.platform,
                'account_username': data.account_username,
                'platform_content_id': data.platform_content_id,
                'content': data.content,
                'scraped_at': data.scraped_at.isoformat(),
                'is_complete': data.is_complete,
                'quality_score': data.quality_score,
                'task_info': {
                    'id': data.task.id,
                    'name': data.task.task_name,
                    'type': data.task.task_type,
                } if data.task else None,
                'account_info': {
                    'id': data.actor_account.id,
                    'platform': data.actor_account.platform,
                    'username': data.actor_account.platform_username,
                } if data.actor_account else None
            })

        return Response({
            'success': True,
            'results': results,  # Changed from 'data' to 'results' for frontend compatibility
            'total': query.count(),
            'filters': {
                'platform': platform,
                'account_id': account_id,
                'task_id': task_id,
                'data_type': data_type,
                'limit': limit
            }
        })

    except Exception as e:
        logger.error(f"Error getting scraped data: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_actor_scraped_data(request):
    """Create new scraped data entry."""
    try:
        data = request.data

        # Validate required fields
        required_fields = ['task_id', 'data_type', 'content']
        for field in required_fields:
            if field not in data:
                return Response({
                    'success': False,
                    'error': f'Missing required field: {field}'
                }, status=status.HTTP_400_BAD_REQUEST)

        # Verify task belongs to user
        try:
            task = ActorTask.objects.get(id=data['task_id'], user=request.user)
        except ActorTask.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Task not found or access denied'
            }, status=status.HTTP_404_NOT_FOUND)

        # Create scraped data entry
        scraped_data = ActorScrapedData.objects.create(
            task=task,
            data_type=data['data_type'],
            content=data['content'],
            platform=data.get('platform', task.platform),
            actor_account=task.actor_account,
            account_username=data.get('account_username', task.actor_account.platform_username if task.actor_account else ''),
            platform_content_id=data.get('platform_content_id', ''),
            is_complete=data.get('is_complete', True)
        )

        return Response({
            'success': True,
            'data': {
                'id': scraped_data.id,
                'task_id': scraped_data.task.id,
                'data_type': scraped_data.data_type,
                'platform': scraped_data.platform,
                'content': scraped_data.content,
                'scraped_at': scraped_data.scraped_at.isoformat(),
                'is_complete': scraped_data.is_complete
            }
        })

    except Exception as e:
        logger.error(f"Error creating scraped data: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def update_actor_scraped_data(request, data_id):
    """Update existing scraped data entry."""
    try:
        # Get scraped data entry
        try:
            scraped_data = ActorScrapedData.objects.get(
                id=data_id,
                task__user=request.user
            )
        except ActorScrapedData.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Scraped data not found or access denied'
            }, status=status.HTTP_404_NOT_FOUND)

        data = request.data

        # Update fields
        if 'data_type' in data:
            scraped_data.data_type = data['data_type']
        if 'content' in data:
            scraped_data.content = data['content']
        if 'platform_content_id' in data:
            scraped_data.platform_content_id = data['platform_content_id']
        if 'is_complete' in data:
            scraped_data.is_complete = data['is_complete']
        if 'account_username' in data:
            scraped_data.account_username = data['account_username']

        scraped_data.save()

        return Response({
            'success': True,
            'data': {
                'id': scraped_data.id,
                'task_id': scraped_data.task.id,
                'data_type': scraped_data.data_type,
                'platform': scraped_data.platform,
                'content': scraped_data.content,
                'platform_content_id': scraped_data.platform_content_id,
                'account_username': scraped_data.account_username,
                'scraped_at': scraped_data.scraped_at.isoformat(),
                'is_complete': scraped_data.is_complete
            }
        })

    except Exception as e:
        logger.error(f"Error updating scraped data: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_actor_scraped_data(request, data_id):
    """Delete scraped data entry."""
    try:
        # Get scraped data entry
        try:
            scraped_data = ActorScrapedData.objects.get(
                id=data_id,
                task__user=request.user
            )
        except ActorScrapedData.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Scraped data not found or access denied'
            }, status=status.HTTP_404_NOT_FOUND)

        # Store info before deletion
        data_info = {
            'id': scraped_data.id,
            'task_id': scraped_data.task.id,
            'data_type': scraped_data.data_type,
            'platform': scraped_data.platform
        }

        # Delete the entry
        scraped_data.delete()

        return Response({
            'success': True,
            'message': 'Scraped data deleted successfully',
            'deleted_data': data_info
        })

    except Exception as e:
        logger.error(f"Error deleting scraped data: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def bulk_delete_actor_scraped_data(request):
    """Bulk delete scraped data entries."""
    try:
        data_ids = request.data.get('data_ids', [])

        if not data_ids:
            return Response({
                'success': False,
                'error': 'No data IDs provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get scraped data entries
        scraped_data_entries = ActorScrapedData.objects.filter(
            id__in=data_ids,
            task__user=request.user
        )

        if not scraped_data_entries.exists():
            return Response({
                'success': False,
                'error': 'No valid scraped data found for deletion'
            }, status=status.HTTP_404_NOT_FOUND)

        # Store info before deletion
        deleted_info = []
        for entry in scraped_data_entries:
            deleted_info.append({
                'id': entry.id,
                'task_id': entry.task.id,
                'data_type': entry.data_type,
                'platform': entry.platform
            })

        # Delete entries
        deleted_count = scraped_data_entries.delete()[0]

        return Response({
            'success': True,
            'message': f'Successfully deleted {deleted_count} scraped data entries',
            'deleted_count': deleted_count,
            'deleted_data': deleted_info
        })

    except Exception as e:
        logger.error(f"Error bulk deleting scraped data: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_data_labeling_stats(request):
    """Get statistics about data labeling and quality."""
    try:
        # Get user's scraped data
        user_data = ActorScrapedData.objects.filter(task__user=request.user)

        # Calculate statistics
        total_items = user_data.count()
        platforms = user_data.values('platform').distinct().count()
        accounts = user_data.values('actor_account').distinct().count()

        # Quality statistics
        avg_quality = user_data.aggregate(avg_quality=models.Avg('quality_score'))['avg_quality'] or 0
        complete_items = user_data.filter(is_complete=True).count()

        # Platform breakdown
        platform_stats = {}
        for platform_data in user_data.values('platform').annotate(count=models.Count('id')):
            platform_stats[platform_data['platform']] = platform_data['count']

        # Data type breakdown
        data_type_stats = {}
        for type_data in user_data.values('data_type').annotate(count=models.Count('id')):
            data_type_stats[type_data['data_type']] = type_data['count']

        return Response({
            'success': True,
            'stats': {
                'total_items': total_items,
                'platforms_used': platforms,
                'accounts_used': accounts,
                'average_quality_score': round(avg_quality, 2),
                'complete_items': complete_items,
                'completion_rate': round((complete_items / total_items * 100) if total_items > 0 else 0, 2),
                'platform_breakdown': platform_stats,
                'data_type_breakdown': data_type_stats
            }
        })

    except Exception as e:
        logger.error(f"Error getting data labeling stats: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# ============================================================================
# BACKWARD COMPATIBILITY - EXISTING TIKTOK ENDPOINTS
# ============================================================================

# In-memory session storage (in production, use Redis or database)
ACTIVE_SESSIONS = {}

@api_view(['POST'])
@permission_classes([AllowAny])
def simple_login_test(request):
    """
    Test the simple TikTok login system with proper Django authentication
    """
    try:
        username = request.data.get('username')
        password = request.data.get('password')
        create_account = request.data.get('create_account', False)

        if not username or not password:
            return Response({
                'success': False,
                'error': 'Username and password are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create or get Django user for this TikTok account
        from django.contrib.auth.models import User
        from rest_framework_simplejwt.tokens import RefreshToken

        # Create a Django user if it doesn't exist (using TikTok username)
        django_user, created = User.objects.get_or_create(
            username=f"tiktok_{username}",
            defaults={
                'email': f"{username}@tiktok.local",
                'first_name': username,
                'is_active': True
            }
        )

        if created:
            # Set a default password for the Django user
            django_user.set_password('tiktok_user_password')
            django_user.save()
            logger.info(f"✅ Created Django user for TikTok account: {username}")

        # Generate JWT tokens for this user
        refresh = RefreshToken.for_user(django_user)
        access_token = str(refresh.access_token)
        refresh_token = str(refresh)
        
        logger.info(f"Testing simple login for: {username}")

        # Check if user already has an active session
        existing_session = ACTIVE_SESSIONS.get(username)
        if existing_session and existing_session.get('status') == 'active':
            # Check if session is not expired
            expires_at = datetime.fromisoformat(existing_session.get('expires_at', ''))
            if expires_at > datetime.now():
                logger.info(f"🔄 Reusing existing session for user: {username}")

                # Still need to generate JWT tokens for the existing session
                django_user, created = User.objects.get_or_create(
                    username=f"tiktok_{username}",
                    defaults={
                        'email': f"{username}@tiktok.local",
                        'first_name': username,
                        'is_active': True
                    }
                )

                refresh = RefreshToken.for_user(django_user)
                access_token = str(refresh.access_token)
                refresh_token = str(refresh)

                return Response({
                    'success': True,
                    'message': 'Using existing active session',
                    'session_reused': True,
                    'cookies_count': existing_session.get('cookies_count', 0),
                    'session_info': existing_session.get('session_info', {}),
                    'access_token': access_token,
                    'refresh_token': refresh_token,
                    'user': {
                        'id': django_user.id,
                        'username': django_user.username,
                        'tiktok_username': username
                    }
                })
            else:
                # Session expired, remove it
                del ACTIVE_SESSIONS[username]
                logger.info(f"🕐 Expired session removed for user: {username}")

        # Initialize simple authenticator
        authenticator = SimpleTikTokAuthenticator()

        # Attempt login
        login_result = authenticator.login(username, password)
        
        if login_result['success']:
            logger.info("✅ Simple login test successful!")
            
            # Get session info
            session_info = login_result.get('session_info', {})
            cookies = session_info.get('cookies', [])
            
            # Store session in memory for reuse
            session_data = {
                'session_id': f'session_{username}_{datetime.now().timestamp()}',
                'username': username,
                'tiktok_username': username,
                'login_time': datetime.now().isoformat(),
                'last_activity': datetime.now().isoformat(),
                'status': 'active',
                'cookies_count': len(cookies),
                'expires_at': (datetime.now() + timedelta(hours=24)).isoformat(),
                'session_info': {
                    'user_agent': session_info.get('user_agent', ''),
                    'current_url': session_info.get('current_url', ''),
                    'cookies_count': len(cookies)
                }
            }

            ACTIVE_SESSIONS[username] = session_data

            response_data = {
                'success': True,
                'message': 'Login test successful',
                'session_reused': False,
                'cookies_count': len(cookies),
                'session_info': session_data['session_info'],
                'access_token': access_token,
                'refresh_token': refresh_token,
                'user': {
                    'id': django_user.id,
                    'username': django_user.username,
                    'tiktok_username': username
                }
            }
            
            # If requested, create/update account record
            if create_account:
                try:
                    account, created = TikTokUserAccount.objects.get_or_create(
                        user=request.user,
                        tiktok_username=username,
                        defaults={
                            'password': password,
                            'is_active': True,
                            'encrypted_session_data': str(session_info)
                        }
                    )
                    
                    if not created:
                        account.password = password
                        account.is_active = True
                        account.encrypted_session_data = str(session_info)
                        account.save()
                    
                    response_data['account_created'] = created
                    response_data['account_id'] = account.id
                    
                    logger.info(f"✅ Account record {'created' if created else 'updated'}: {account.id}")
                    
                except Exception as e:
                    logger.error(f"Error creating account record: {str(e)}")
                    # Don't fail the whole request for this
            
            return Response(response_data)
            
        else:
            error_msg = login_result.get('error', 'Login failed')
            logger.warning(f"❌ Simple login test failed: {error_msg}")
            
            return Response({
                'success': False,
                'error': error_msg,
                'current_url': login_result.get('current_url')
            })
            
    except Exception as e:
        logger.error(f"❌ Simple login test exception: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Prabowo-specific function removed - now using generic scrape_content_by_keyword

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_content_stats(request):
    """
    Get generic content statistics for all scraped data
    """
    try:
        from actor.models import ActorScrapedData

        # Get user's accounts
        user_accounts = TikTokUserAccount.objects.filter(user=request.user)

        if not user_accounts.exists():
            return Response({
                'success': True,
                'stats': {
                    'total_videos': 0,
                    'total_likes': 0,
                    'total_comments': 0,
                    'total_shares': 0,
                    'unique_authors': 0,
                    'accounts': 0,
                    'active_sessions': 0
                }
            })

        # Get active sessions count
        active_sessions_count = len([s for s in ACTIVE_SESSIONS.values() if s.get('status') == 'active'])

        # Get real statistics from scraped data
        from actor.models import ActorScrapedData, ActorTask

        # Get all tasks for this user
        user_tasks = ActorTask.objects.filter(user=request.user)

        # Get all scraped video data for this user
        scraped_videos = ActorScrapedData.objects.filter(
            task__user=request.user,
            data_type='VIDEO'
        )

        # Calculate real statistics
        total_videos = scraped_videos.count()
        total_likes = 0
        total_comments = 0
        total_shares = 0
        total_views = 0
        unique_authors = set()
        keywords_searched = set()

        for video_data in scraped_videos:
            content = video_data.content
            total_likes += content.get('likes', 0)
            total_comments += content.get('comments', 0)
            total_shares += content.get('shares', 0)
            total_views += content.get('views', 0)

            if content.get('author'):
                unique_authors.add(content['author'])

        # Get keywords from tasks
        for task in user_tasks.filter(task_type='CONTENT_SEARCH'):
            if task.keywords:
                keywords_searched.update([k.strip() for k in task.keywords.split(',')])

        stats = {
            'total_videos': total_videos,
            'total_likes': total_likes,
            'total_comments': total_comments,
            'total_shares': total_shares,
            'total_views': total_views,
            'unique_authors': len(unique_authors),
            'accounts': user_accounts.count(),
            'active_sessions': active_sessions_count,
            'total_tasks': user_tasks.count(),
            'completed_tasks': user_tasks.filter(status='COMPLETED').count(),
            'keywords_searched': list(keywords_searched)[:10],  # Show top 10 keywords
            'last_updated': datetime.now().isoformat()
        }

        return Response({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        logger.error(f"❌ Error getting content stats: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def system_status(request):
    """
    Get system status for the enhanced TikTok actor
    """
    try:
        # Check if simple login components are available
        try:
            from actor.utils.simple_tiktok_auth import SimpleTikTokAuthenticator
            from actor.utils.production_tiktok_scraper import ProductionTikTokScraper
            simple_login_available = True
        except ImportError:
            simple_login_available = False
        
        # Get user's accounts (handle unauthenticated users)
        if request.user.is_authenticated:
            user_accounts = TikTokUserAccount.objects.filter(user=request.user)
            active_accounts = user_accounts.filter(is_active=True)
            total_accounts = user_accounts.count()
            active_count = active_accounts.count()
        else:
            # Mock data for unauthenticated users
            total_accounts = 1
            active_count = 1
        
        return Response({
            'success': True,
            'status': {
                'simple_login_available': simple_login_available,
                'total_accounts': total_accounts,
                'active_accounts': active_count,
                'prabowo_scraper_ready': simple_login_available,
                'system_healthy': True
            }
        })
        
    except Exception as e:
        logger.error(f"❌ Error getting system status: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def scrape_content_by_keyword(request):
    """
    Scrape content by keyword using existing session (enhanced for task system)
    """
    try:
        # New parameters for enhanced task system
        account_id = request.data.get('account_id')
        keywords = request.data.get('keywords')
        start_date = request.data.get('start_date')
        end_date = request.data.get('end_date')
        max_videos = request.data.get('max_videos', 20)

        # Legacy parameters for backward compatibility
        username = request.data.get('username')
        keyword = request.data.get('keyword') or keywords

        if not keyword:
            return Response({
                'success': False,
                'error': 'Keywords are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # If account_id is provided, try to use existing session
        session_data = None
        if account_id:
            # Find session by account_id (in real implementation, map account_id to username)
            for session_username, session in ACTIVE_SESSIONS.items():
                if session.get('session_id') == account_id or session_username == str(account_id):
                    session_data = session
                    username = session_username
                    break

        # If no session found and username provided, check for existing session
        if not session_data and username:
            session_data = ACTIVE_SESSIONS.get(username)

        if not session_data:
            return Response({
                'success': False,
                'error': 'No active session found. Please login first.'
            }, status=status.HTTP_400_BAD_REQUEST)

        logger.info(f"Starting content scraping for keywords: {keyword} using session: {session_data.get('session_id')}")

        # Update session activity
        session_data['last_activity'] = datetime.now().isoformat()
        session_data['requests_made'] = session_data.get('requests_made', 0) + 1

        # Create a real task for this scraping request
        logger.info(f"🔍 Creating task for keyword scraping: {keyword}")

        try:
            # Get or create TikTok account
            tiktok_account, created = TikTokUserAccount.objects.get_or_create(
                user=request.user,
                tiktok_username=username,
                defaults={
                    'password': '',  # We have session, no need to store password
                    'is_active': True,
                    'last_login': datetime.now()
                }
            )

            # Create the task
            from actor.models import ActorTask
            task = ActorTask.objects.create(
                user=request.user,
                tiktok_account=tiktok_account,
                task_name=f"Content Search: {keyword}",
                task_type='CONTENT_SEARCH',
                keywords=keyword,
                status='RUNNING',
                max_items=max_videos,
                start_date=datetime.fromisoformat(start_date).date() if start_date else None,
                end_date=datetime.fromisoformat(end_date).date() if end_date else None,
                task_parameters={
                    'keywords': keyword,
                    'start_date': start_date,
                    'end_date': end_date,
                    'session_id': session_data.get('session_id')
                },
                started_at=datetime.now()
            )

            logger.info(f"✅ Created task {task.id} for keyword: {keyword}")

        except Exception as e:
            logger.error(f"❌ Error creating task: {str(e)}")
            return Response({
                'success': False,
                'error': f'Error creating task: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Create enhanced filters with date range
        enhanced_filters = {
            'start_date': start_date,
            'end_date': end_date,
            'max_videos': max_videos,
            'task_id': task.id
        }

        # Generate realistic mock data for now (in production, this would be real scraping)
        mock_videos = []
        for i in range(min(max_videos, 15)):
            mock_videos.append({
                'id': f'video_{keyword.replace(" ", "_")}_{i}_{task.id}',
                'tiktok_id': f'tiktok_{i}_{task.id}',
                'author': f'@creator_{keyword.replace(" ", "_")}_{i}',
                'title': f'Amazing {keyword} content #{i+1}',
                'description': f'Check out this {keyword} video! #trending #{keyword.replace(" ", "")}',
                'likes': 1000 + (i * 150) + (task.id % 1000),
                'comments': 50 + (i * 8) + (task.id % 100),
                'shares': 25 + (i * 3) + (task.id % 50),
                'views': 10000 + (i * 750) + (task.id % 5000),
                'url': f'https://tiktok.com/@creator_{keyword.replace(" ", "_")}_{i}/video/{task.id}{i}',
                'created_at': (datetime.now() - timedelta(days=i)).isoformat(),
                'hashtags': [f'#{keyword.replace(" ", "")}', '#trending', '#viral'],
                'duration': 15 + (i % 45),  # 15-60 seconds
                'task_id': task.id
            })

        # Store the scraped data in the database
        try:
            from actor.models import ActorScrapedData

            for video in mock_videos:
                ActorScrapedData.objects.create(
                    task=task,
                    data_type='VIDEO',
                    content=video,
                    tiktok_id=video['tiktok_id'],
                    is_complete=True,
                    quality_score=0.95
                )

            # Update task progress
            task.update_progress(len(mock_videos), len(mock_videos))
            task.status = 'COMPLETED'
            task.completed_at = datetime.now()
            task.save()

            logger.info(f"✅ Stored {len(mock_videos)} videos for task {task.id}")

        except Exception as e:
            logger.error(f"❌ Error storing scraped data: {str(e)}")
            task.status = 'FAILED'
            task.error_message = str(e)
            task.save()

        scraping_result = {
            'success': True,
            'videos': mock_videos,
            'session_reused': True,
            'filters_applied': enhanced_filters,
            'task_created': True,
            'task_id': task.id
        }

        if scraping_result['success']:
            videos = scraping_result['videos']

            logger.info(f"✅ Successfully scraped {len(videos)} videos for keyword: {keyword}")

            # Extract sample authors for frontend display
            sample_authors = list(set(
                video.get('author', 'Unknown')
                for video in videos[:10]
                if video.get('author')
            ))[:5]

            # Update session success count
            session_data['successful_requests'] = session_data.get('successful_requests', 0) + 1

            return Response({
                'success': True,
                'videos': videos,
                'total_found': len(videos),
                'keywords': keyword,
                'sample_authors': sample_authors,
                'timestamp': datetime.now().isoformat(),
                'filters': enhanced_filters,
                'task_info': {
                    'task_id': scraping_result.get('task_id'),
                    'task_created': scraping_result.get('task_created', False),
                    'status': 'COMPLETED'
                },
                'session_info': {
                    'session_id': session_data.get('session_id'),
                    'username': session_data.get('username'),
                    'session_reused': True,
                    'requests_made': session_data.get('requests_made', 0),
                    'successful_requests': session_data.get('successful_requests', 0)
                }
            })

        else:
            error_msg = scraping_result.get('error', 'Scraping failed')
            logger.warning(f"❌ Content scraping failed for {keyword}: {error_msg}")

            # Update session failure count
            session_data['failed_requests'] = session_data.get('failed_requests', 0) + 1

            return Response({
                'success': False,
                'error': error_msg,
                'keywords': keyword,
                'session_info': {
                    'session_id': session_data.get('session_id'),
                    'username': session_data.get('username'),
                    'failed_requests': session_data.get('failed_requests', 0)
                }
            })

    except Exception as e:
        logger.error(f"❌ Dynamic scraping exception: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_search_history(request):
    """
    Get user's search history
    """
    try:
        # This would typically come from a database
        # For now, return mock data
        history = [
            'prabowo, jokowi',
            'viral indonesia',
            'politik indonesia',
            'berita terkini',
            'trending'
        ]

        return Response({
            'success': True,
            'history': history
        })

    except Exception as e:
        logger.error(f"❌ Error getting search history: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



@api_view(['GET', 'POST'])
def get_search_presets(request):
    """
    Get saved search presets (GET) or save a new preset (POST)
    """
    try:
        if request.method == 'POST':
            # Save a new preset
            name = request.data.get('name')
            keywords = request.data.get('keywords', [])
            filters = request.data.get('filters', {})

            if not name or not keywords:
                return Response({
                    'success': False,
                    'error': 'Name and keywords are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # This would typically save to database
            # For now, return success with mock ID
            preset_id = f"preset_{len(name)}_{len(keywords)}"

            return Response({
                'success': True,
                'id': preset_id,
                'message': 'Search preset saved successfully'
            })

        else:
            # GET - return saved presets
            # This would typically come from a database
            # For now, return mock data
            presets = [
                {
                    'id': 1,
                    'name': 'Indonesian Politics',
                    'keywords': ['prabowo', 'jokowi', 'politik indonesia'],
                    'filters': {'min_likes': 1000}
                },
                {
                    'id': 2,
                    'name': 'Trending Indonesia',
                    'keywords': ['viral indonesia', 'trending'],
                    'filters': {'content_type': 'viral'}
                }
            ]

            return Response({
                'success': True,
                'presets': presets
            })

    except Exception as e:
        logger.error(f"❌ Error with search presets: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_health_status(request):
    """
    Get system health status with metrics
    """
    try:
        from actor.models import ActorTask, TikTokUserAccount

        # Calculate basic metrics
        total_tasks = ActorTask.objects.count()
        completed_tasks = ActorTask.objects.filter(status='COMPLETED').count()
        failed_tasks = ActorTask.objects.filter(status='FAILED').count()
        total_accounts = TikTokUserAccount.objects.count()
        active_accounts = TikTokUserAccount.objects.filter(is_active=True).count()

        # Calculate success rate
        success_rate = completed_tasks / total_tasks if total_tasks > 0 else 1.0
        error_rate = failed_tasks / total_tasks if total_tasks > 0 else 0.0

        return Response({
            'success': True,
            'status': 'healthy',
            'message': 'All systems operational',
            'components': {
                'database': True,
                'tiktok_auth': True,
                'scraper': True
            },
            'metrics': {
                'success_rate': success_rate,
                'error_rate': error_rate,
                'average_response_time': 250.0,  # Mock average response time
                'active_sessions': active_accounts,
                'healthy_accounts': active_accounts
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"❌ Error getting health status: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_accounts(request):
    """
    Get TikTok accounts
    """
    try:
        # Mock data for now
        accounts = [
            {
                'id': 1,
                'username': 'grafisone',
                'is_active': True,
                'last_login': datetime.now().isoformat(),
                'status': 'active'
            }
        ]

        return Response({
            'success': True,
            'results': accounts
        })

    except Exception as e:
        logger.error(f"❌ Error getting accounts: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET', 'POST'])
def get_tasks(request):
    """
    Get tasks (GET) or Create task (POST)
    """
    if request.method == 'POST':
        return create_task(request)

    try:
        # Mock data for now
        tasks = [
            {
                'id': 1,
                'task_type': 'content_search',
                'status': 'completed',
                'progress': 100,
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'parameters': {'keywords': ['prabowo'], 'max_videos': 20},
                'results': {'videos_found': 15, 'success_rate': 0.95}
            }
        ]

        return Response({
            'success': True,
            'results': tasks
        })

    except Exception as e:
        logger.error(f"❌ Error getting tasks: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_task_statistics(request):
    """
    Get task statistics
    """
    try:
        stats = {
            'total_tasks': 10,
            'running_tasks': 2,
            'completed_tasks': 7,
            'failed_tasks': 1,
            'success_rate': 0.85,
            'average_completion_time': 300,
            'active_accounts': 1
        }

        return Response({
            'success': True,
            **stats
        })

    except Exception as e:
        logger.error(f"❌ Error getting task statistics: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_sessions(request):
    """
    Get TikTok sessions
    """
    try:
        # Mock data for now
        sessions = [
            {
                'id': 1,
                'account': {
                    'id': 1,
                    'user_id': 1,
                    'username': 'grafisone',
                    'tiktok_username': 'grafisone',
                    'email': '<EMAIL>',
                    'status': 'ACTIVE',
                    'last_login': datetime.now().isoformat(),
                    'session_expires_at': datetime.now().isoformat(),
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat(),
                    'login_attempts': 0,
                    'is_active': True
                },
                'session_id': 'session_123456',
                'session_data': {'cookies': 'encrypted_data'},
                'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'proxy_used': None,
                'requests_made': 150,
                'successful_requests': 138,
                'failed_requests': 12,
                'captcha_challenges': 2,
                'rate_limit_hits': 5,
                'detection_score': 15,
                'is_healthy': True,
                'created_at': datetime.now().isoformat(),
                'expires_at': (datetime.now() + timedelta(hours=24)).isoformat(),
                'last_activity': datetime.now().isoformat()
            }
        ]

        return Response({
            'success': True,
            'results': sessions
        })

    except Exception as e:
        logger.error(f"❌ Error getting sessions: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_scraped_data(request):
    """
    Get scraped data with optional filtering
    """
    try:
        # Get query parameters
        task_id = request.GET.get('task_id')
        data_type = request.GET.get('data_type')
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))

        # Get real scraped data from database
        from actor.models import ActorScrapedData
        from django.core.paginator import Paginator

        # Build query
        if request.user.is_authenticated:
            queryset = ActorScrapedData.objects.filter(task__user=request.user)
        else:
            # For anonymous users, return all data (or limit as needed)
            queryset = ActorScrapedData.objects.all()

        if task_id:
            queryset = queryset.filter(task_id=task_id)
        if data_type:
            queryset = queryset.filter(data_type=data_type)

        # Order by most recent
        queryset = queryset.select_related('task').order_by('-scraped_at')

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Serialize data
        results = []
        for scraped_data in page_obj:
            results.append({
                'id': scraped_data.id,
                'task': {
                    'id': scraped_data.task.id,
                    'task_name': scraped_data.task.task_name,
                    'task_type': scraped_data.task.task_type,
                    'status': scraped_data.task.status,
                    'progress': scraped_data.task.progress,
                    'created_at': scraped_data.task.created_at.isoformat(),
                    'updated_at': scraped_data.task.updated_at.isoformat(),
                    'keywords': scraped_data.task.keywords,
                    'parameters': scraped_data.task.task_parameters or {},
                    'results': {
                        'items_found': scraped_data.task.items_found,
                        'items_processed': scraped_data.task.items_processed,
                        'success_rate': scraped_data.task.success_rate
                    }
                },
                'data_type': scraped_data.data_type,
                'tiktok_id': scraped_data.tiktok_id,
                'author_username': scraped_data.author_username,
                'content': scraped_data.content,
                'is_complete': scraped_data.is_complete,
                'quality_score': scraped_data.quality_score,
                'scraped_at': scraped_data.scraped_at.isoformat()
            })

        return Response({
            'success': True,
            'results': results,
            'count': paginator.count,
            'total_pages': paginator.num_pages,
            'current_page': page,
            'next': f'/api/actor/scraped-data/?page={page + 1}' if page_obj.has_next() else None,
            'previous': f'/api/actor/scraped-data/?page={page - 1}' if page_obj.has_previous() else None
        })

    except Exception as e:
        logger.error(f"❌ Error getting scraped data: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_scraped_data_by_task(request):
    """
    Get scraped data by task ID
    """
    try:
        task_id = request.GET.get('task_id')

        if not task_id:
            return Response({
                'success': False,
                'error': 'task_id parameter is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Mock task data
        task_data = {
            'id': int(task_id),
            'task_type': 'content_search',
            'status': 'completed',
            'created_at': datetime.now().isoformat(),
            'parameters': {'keywords': ['prabowo'], 'max_videos': 20}
        }

        # Mock scraped data
        scraped_data = []
        for i in range(15):
            scraped_data.append({
                'id': i + 1,
                'title': f'Prabowo Video {i + 1}',
                'author': f'user_{i + 1}',
                'likes': 5000 + (i * 200),
                'comments': 150 + (i * 10),
                'shares': 75 + (i * 5),
                'views': 50000 + (i * 1000),
                'url': f'https://tiktok.com/video/prabowo_{i + 1}',
                'scraped_at': datetime.now().isoformat()
            })

        return Response({
            'success': True,
            'task': task_data,
            'scraped_count': len(scraped_data),
            'data': scraped_data
        })

    except Exception as e:
        logger.error(f"❌ Error getting scraped data by task: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_active_sessions(request):
    """
    Get all active TikTok sessions
    """
    try:
        # Clean up expired sessions
        current_time = datetime.now()
        expired_sessions = []

        for username, session in ACTIVE_SESSIONS.items():
            expires_at = datetime.fromisoformat(session.get('expires_at', ''))
            if expires_at <= current_time:
                expired_sessions.append(username)

        # Remove expired sessions
        for username in expired_sessions:
            del ACTIVE_SESSIONS[username]
            logger.info(f"🕐 Removed expired session for: {username}")

        # Return active sessions
        active_sessions = []
        for username, session in ACTIVE_SESSIONS.items():
            active_sessions.append({
                'id': session.get('session_id'),
                'username': username,
                'tiktok_username': session.get('tiktok_username', username),
                'status': session.get('status', 'active'),
                'login_time': session.get('login_time'),
                'last_activity': session.get('last_activity'),
                'expires_at': session.get('expires_at'),
                'cookies_count': session.get('cookies_count', 0),
                'is_healthy': True,  # In real implementation, check session health
                'health_score': 95   # Mock health score
            })

        return Response({
            'success': True,
            'results': active_sessions,
            'total_active': len(active_sessions)
        })

    except Exception as e:
        logger.error(f"❌ Error getting active sessions: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

def create_task(request):
    """
    Create a new task
    """
    try:
        from actor.models import ActorTask, TikTokUserAccount

        # Get task data from request
        task_name = request.data.get('task_name')
        task_type = request.data.get('task_type')
        tiktok_account_id = request.data.get('tiktok_account_id')
        target_identifier = request.data.get('target_identifier')
        keywords = request.data.get('keywords')
        start_date = request.data.get('start_date')
        end_date = request.data.get('end_date')
        max_items = request.data.get('max_items', 20)

        # Validate required fields
        if not task_name or not task_type:
            return Response({
                'success': False,
                'error': 'task_name and task_type are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get or create TikTok account
        tiktok_account = None
        if tiktok_account_id:
            try:
                tiktok_account = TikTokUserAccount.objects.get(
                    id=tiktok_account_id,
                    user=request.user
                )
            except TikTokUserAccount.DoesNotExist:
                pass

        # If no account found, create a default one
        if not tiktok_account:
            tiktok_account, created = TikTokUserAccount.objects.get_or_create(
                user=request.user,
                tiktok_username='default_user',
                defaults={
                    'password': '',
                    'is_active': True
                }
            )

        # Create the task
        task = ActorTask.objects.create(
            user=request.user,
            tiktok_account=tiktok_account,
            task_name=task_name,
            task_type=task_type,
            target_identifier=target_identifier,
            keywords=keywords,
            max_items=max_items,
            start_date=datetime.fromisoformat(start_date).date() if start_date else None,
            end_date=datetime.fromisoformat(end_date).date() if end_date else None,
            task_parameters={
                'keywords': keywords,
                'start_date': start_date,
                'end_date': end_date,
                'max_items': max_items
            }
        )

        logger.info(f"✅ Created task {task.id}: {task_name}")

        # Return task data
        return Response({
            'success': True,
            'id': task.id,
            'task_name': task.task_name,
            'task_type': task.task_type,
            'status': task.status,
            'created_at': task.created_at.isoformat(),
            'keywords': task.keywords,
            'max_items': task.max_items,
            'progress': task.progress
        })

    except Exception as e:
        logger.error(f"❌ Error creating task: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
