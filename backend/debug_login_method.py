#!/usr/bin/env python3
"""
Diagnostic script to debug TikTok login method selection issues
"""

import os
import sys
import django
import time
import random
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor_tiktok.utils.tiktok_auth import TikTokAuthenticator
from actor_tiktok.utils.anti_detection import AntiDetectionManager

class LoginMethodDebugger:
    def __init__(self):
        self.driver = None
        self.auth = TikTokAuthenticator()
        self.anti_detection = AntiDetectionManager()
        
    def setup_driver(self):
        """Setup Chrome driver for debugging"""
        try:
            print("🔧 Setting up Chrome driver...")
            
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # Keep browser open for manual inspection
            chrome_options.add_experimental_option('detach', True)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✅ Driver setup completed")
            return True
            
        except Exception as e:
            print(f"❌ Driver setup failed: {str(e)}")
            return False
    
    def navigate_to_login(self):
        """Navigate to TikTok login page"""
        try:
            print("\n🌐 Navigating to TikTok login page...")
            self.driver.get('https://www.tiktok.com/login')
            time.sleep(5)
            
            current_url = self.driver.current_url
            print(f"📍 Current URL: {current_url}")
            
            # Take screenshot
            self.driver.save_screenshot('/tmp/tiktok_login_initial.png')
            print("📸 Screenshot saved: /tmp/tiktok_login_initial.png")
            
            return True
            
        except Exception as e:
            print(f"❌ Navigation failed: {str(e)}")
            return False
    
    def handle_popups(self):
        """Handle cookie consent and app popups"""
        try:
            print("\n🍪 Handling popups and modals...")
            
            # Handle cookie consent
            self.auth._handle_cookie_consent(self.driver)
            time.sleep(2)
            
            # Handle app popup
            self.auth._handle_app_popup(self.driver)
            time.sleep(2)
            
            # Handle get full app modal
            self.auth._handle_get_full_app_modal(self.driver)
            time.sleep(2)
            
            print("✅ Popups handled")
            return True
            
        except Exception as e:
            print(f"⚠️ Popup handling error: {str(e)}")
            return True  # Continue even if popup handling fails
    
    def debug_page_elements(self):
        """Debug what elements are available on the page"""
        try:
            print("\n🔍 Debugging page elements...")
            
            # Check page source for key terms
            page_source = self.driver.page_source.lower()
            key_terms = ['phone', 'email', 'username', 'login', 'sign in']
            
            print("📄 Key terms found in page source:")
            for term in key_terms:
                count = page_source.count(term)
                print(f"   {term}: {count} occurrences")
            
            # Look for common login method selectors
            selectors_to_check = [
                # Specific selectors from the code
                'div[data-e2e="channel-item"]',
                'div[role="link"]',
                'div[class*="tiktok-17hparj-DivBoxContainer"]',
                'div[class*="e1cgu1qo0"]',
                # Generic selectors
                'a[href*="phone-or-email"]',
                'a[href*="email"]',
                'button[data-e2e*="login"]',
                'div[data-testid*="login"]',
                # Form elements
                'input[type="text"]',
                'input[type="email"]',
                'input[type="password"]',
                'button[type="submit"]'
            ]
            
            print("\n🎯 Checking for specific selectors:")
            for selector in selectors_to_check:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    visible_elements = [elem for elem in elements if elem.is_displayed()]
                    print(f"   {selector}: {len(elements)} total, {len(visible_elements)} visible")
                    
                    # Print text content of visible elements
                    for i, elem in enumerate(visible_elements[:3]):  # Limit to first 3
                        try:
                            text = elem.text.strip()[:50]  # First 50 chars
                            if text:
                                print(f"      [{i}] Text: '{text}'")
                        except:
                            pass
                            
                except Exception as e:
                    print(f"   {selector}: Error - {str(e)}")
            
            # Check for text-based elements
            print("\n📝 Checking for text-based elements:")
            text_patterns = [
                "Use phone",
                "email", 
                "username",
                "Log in",
                "Sign in",
                "Continue"
            ]
            
            for pattern in text_patterns:
                try:
                    xpath = f"//*[contains(text(), '{pattern}')]"
                    elements = self.driver.find_elements(By.XPATH, xpath)
                    visible_elements = [elem for elem in elements if elem.is_displayed()]
                    print(f"   Text '{pattern}': {len(elements)} total, {len(visible_elements)} visible")
                except Exception as e:
                    print(f"   Text '{pattern}': Error - {str(e)}")
            
            return True
            
        except Exception as e:
            print(f"❌ Element debugging failed: {str(e)}")
            return False
    
    def test_login_method_selection(self):
        """Test the actual login method selection"""
        try:
            print("\n🧪 Testing login method selection...")
            
            # Take screenshot before selection
            self.driver.save_screenshot('/tmp/tiktok_before_selection.png')
            print("📸 Screenshot saved: /tmp/tiktok_before_selection.png")
            
            # Try the specific selectors from the code
            specific_selectors = [
                'div[data-e2e="channel-item"][tabindex="0"][role="link"][class="tiktok-17hparj-DivBoxContainer e1cgu1qo0"]',
                'div[data-e2e="channel-item"][role="link"]',
                'div[class*="tiktok-17hparj-DivBoxContainer"]',
                'div[class*="e1cgu1qo0"]'
            ]
            
            print("🎯 Testing specific selectors:")
            for i, selector in enumerate(specific_selectors):
                try:
                    print(f"   [{i+1}] Testing: {selector}")
                    element = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    
                    if element.is_displayed():
                        print(f"   ✅ Found element with text: '{element.text.strip()[:100]}'")
                        
                        # Try to click it
                        print(f"   🖱️ Attempting to click...")
                        self.anti_detection.human_like_click(self.driver, element)
                        time.sleep(3)
                        
                        # Take screenshot after click
                        self.driver.save_screenshot(f'/tmp/tiktok_after_click_{i+1}.png')
                        print(f"   📸 Screenshot saved: /tmp/tiktok_after_click_{i+1}.png")
                        
                        # Check if form appeared
                        username_field = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="text"], input[type="email"]')
                        password_field = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="password"]')
                        
                        if username_field and password_field:
                            print(f"   ✅ SUCCESS: Login form appeared after clicking!")
                            return True
                        else:
                            print(f"   ⚠️ Click successful but no form appeared")
                        
                        break
                        
                except TimeoutException:
                    print(f"   ❌ Element not found or not clickable")
                except Exception as e:
                    print(f"   ❌ Error: {str(e)}")
            
            # Try XPath selectors
            print("\n🎯 Testing XPath selectors:")
            xpath_selectors = [
                '//div[contains(text(), "Use phone / email / username")]',
                '//div[contains(text(), "Use phone") and contains(text(), "email") and contains(text(), "username")]',
                '//div[contains(text(), "phone") and contains(text(), "email")]',
                '//a[contains(text(), "email")]'
            ]
            
            for i, selector in enumerate(xpath_selectors):
                try:
                    print(f"   [{i+1}] Testing: {selector}")
                    element = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    
                    if element.is_displayed():
                        print(f"   ✅ Found element with text: '{element.text.strip()[:100]}'")
                        
                        # Try to click it
                        print(f"   🖱️ Attempting to click...")
                        self.anti_detection.human_like_click(self.driver, element)
                        time.sleep(3)
                        
                        # Check if form appeared
                        username_field = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="text"], input[type="email"]')
                        password_field = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="password"]')
                        
                        if username_field and password_field:
                            print(f"   ✅ SUCCESS: Login form appeared after clicking!")
                            return True
                        else:
                            print(f"   ⚠️ Click successful but no form appeared")
                        
                        break
                        
                except TimeoutException:
                    print(f"   ❌ Element not found or not clickable")
                except Exception as e:
                    print(f"   ❌ Error: {str(e)}")
            
            # Check if form is already available
            print("\n🔍 Checking if login form is already available...")
            username_field = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="text"], input[type="email"]')
            password_field = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="password"]')
            
            if username_field and password_field:
                print("   ✅ Login form is already available - no method selection needed!")
                return True
            else:
                print("   ❌ Login form not available and method selection failed")
                return False
            
        except Exception as e:
            print(f"❌ Login method selection test failed: {str(e)}")
            return False
    
    def run_debug(self):
        """Run the complete debugging process"""
        try:
            print("🚀 Starting TikTok Login Method Debugging")
            print("=" * 50)
            
            if not self.setup_driver():
                return False
            
            if not self.navigate_to_login():
                return False
            
            self.handle_popups()
            
            self.debug_page_elements()
            
            success = self.test_login_method_selection()
            
            print("\n" + "=" * 50)
            if success:
                print("✅ DEBUGGING COMPLETE: Login method selection working!")
            else:
                print("❌ DEBUGGING COMPLETE: Login method selection failed!")
                print("\n💡 Recommendations:")
                print("   1. Check if TikTok has updated their login page structure")
                print("   2. Update selectors in the TikTokAuthenticator class")
                print("   3. Consider using alternative login URLs")
                print("   4. Check for region-specific login page variations")
            
            print("\n🔍 Browser will remain open for manual inspection")
            print("   Check screenshots in /tmp/ for visual debugging")
            
            return success
            
        except Exception as e:
            print(f"❌ Debug process failed: {str(e)}")
            return False
        finally:
            # Don't close driver to allow manual inspection
            print("\n⏸️ Browser left open for manual inspection")
            print("   Close manually when done debugging")

if __name__ == "__main__":
    debugger = LoginMethodDebugger()
    debugger.run_debug()