#!/usr/bin/env python3
"""
Actor Account Session Checker and Re-authenticator

This script checks for authenticated sessions for actor accounts and
re-authenticates using Selenium if the session is invalid or expired.
"""

import os
import sys
import django
import logging
from datetime import datetime
from typing import Dict, List, Optional

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.utils import timezone
from actor.models import ActorAccount
from actor.services.actor_service import ActorService
from actor.engines.tiktok_engine import TikTokEngine
from actor.utils.simple_tiktok_auth import SimpleTikTokAuthenticator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ActorSessionManager:
    """
    Manages actor account sessions and re-authentication
    """
    
    def __init__(self):
        self.actor_service = ActorService()
        self.tiktok_engine = TikTokEngine()
        self.authenticator = SimpleTikTokAuthenticator()
        
    def check_all_actor_sessions(self) -> Dict:
        """
        Check all actor accounts for valid sessions
        
        Returns:
            Dict with session status for all accounts
        """
        logger.info("🔍 Checking all actor account sessions...")
        
        try:
            # Get all active actor accounts
            accounts = ActorAccount.objects.filter(is_active=True)
            
            if not accounts.exists():
                logger.warning("No active actor accounts found")
                return {
                    'success': True,
                    'message': 'No active actor accounts found',
                    'accounts_checked': 0,
                    'valid_sessions': 0,
                    'invalid_sessions': 0,
                    'reauth_attempts': 0,
                    'reauth_successes': 0
                }
            
            results = {
                'success': True,
                'accounts_checked': 0,
                'valid_sessions': 0,
                'invalid_sessions': 0,
                'reauth_attempts': 0,
                'reauth_successes': 0,
                'account_details': []
            }
            
            for account in accounts:
                logger.info(f"\n📋 Checking account: {account.platform_username} ({account.platform})")
                
                account_result = self.check_and_reauth_account(account)
                results['account_details'].append(account_result)
                results['accounts_checked'] += 1
                
                if account_result['session_valid']:
                    results['valid_sessions'] += 1
                else:
                    results['invalid_sessions'] += 1
                    
                if account_result['reauth_attempted']:
                    results['reauth_attempts'] += 1
                    if account_result['reauth_success']:
                        results['reauth_successes'] += 1
            
            # Summary
            logger.info(f"\n📊 Session Check Summary:")
            logger.info(f"   Accounts checked: {results['accounts_checked']}")
            logger.info(f"   Valid sessions: {results['valid_sessions']}")
            logger.info(f"   Invalid sessions: {results['invalid_sessions']}")
            logger.info(f"   Re-auth attempts: {results['reauth_attempts']}")
            logger.info(f"   Re-auth successes: {results['reauth_successes']}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error checking actor sessions: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def check_and_reauth_account(self, account: ActorAccount) -> Dict:
        """
        Check a specific account's session and re-authenticate if needed
        
        Args:
            account: ActorAccount instance
            
        Returns:
            Dict with account session status and re-auth results
        """
        result = {
            'account_id': account.id,
            'platform': account.platform,
            'username': account.platform_username,
            'session_valid': False,
            'session_expired': False,
            'reauth_attempted': False,
            'reauth_success': False,
            'error': None,
            'last_login': account.last_login.isoformat() if account.last_login else None,
            'session_expires_at': account.session_expires_at.isoformat() if account.session_expires_at else None
        }
        
        try:
            # Check if account is blocked
            if account.is_blocked:
                blocked_until = account.blocked_until
                if blocked_until and blocked_until > timezone.now():
                    remaining_time = (blocked_until - timezone.now()).total_seconds() / 60
                    logger.warning(f"   ⚠️  Account is blocked for {remaining_time:.0f} more minutes")
                    result['error'] = f"Account blocked for {remaining_time:.0f} minutes"
                    return result
            
            # Check session validity
            session_valid = account.is_session_valid()
            result['session_valid'] = session_valid
            
            # Check if session has expired
            if account.session_expires_at:
                session_expired = account.session_expires_at <= timezone.now()
                result['session_expired'] = session_expired
                
                if session_expired:
                    logger.info(f"   ⏰ Session expired at {account.session_expires_at}")
                else:
                    time_until_expiry = account.session_expires_at - timezone.now()
                    logger.info(f"   ✅ Session valid for {time_until_expiry.total_seconds() / 3600:.1f} more hours")
            
            if session_valid:
                logger.info(f"   ✅ Session is valid")
                return result
            
            # Session is invalid, attempt re-authentication
            logger.info(f"   ❌ Session is invalid, attempting re-authentication...")
            result['reauth_attempted'] = True
            
            # Attempt re-authentication based on platform
            if account.platform.lower() == 'tiktok':
                reauth_result = self._reauth_tiktok_account(account)
            else:
                logger.warning(f"   ⚠️  Platform {account.platform} not supported for re-authentication")
                result['error'] = f"Platform {account.platform} not supported"
                return result
            
            if reauth_result.get('success'):
                logger.info(f"   ✅ Re-authentication successful!")
                result['reauth_success'] = True
                result['session_valid'] = True
            else:
                error_msg = reauth_result.get('error', 'Unknown error')
                logger.error(f"   ❌ Re-authentication failed: {error_msg}")
                result['error'] = error_msg
            
            return result
            
        except Exception as e:
            logger.error(f"   ❌ Error checking account {account.id}: {str(e)}")
            result['error'] = str(e)
            return result
    
    def _reauth_tiktok_account(self, account: ActorAccount) -> Dict:
        """
        Re-authenticate a TikTok account using Selenium
        
        Args:
            account: ActorAccount instance for TikTok
            
        Returns:
            Dict with authentication result
        """
        try:
            # Get credentials
            username = account.platform_username
            password = account.decrypt_password()
            
            if not password:
                return {
                    'success': False,
                    'error': 'No password available for account'
                }
            
            logger.info(f"   🔐 Attempting TikTok login for {username}...")
            
            # Use the TikTok engine's authenticate method
            credentials = {
                'username': username,
                'password': password
            }
            
            auth_result = self.tiktok_engine.authenticate(account, credentials)
            
            if auth_result.get('success'):
                logger.info(f"   ✅ TikTok authentication successful")
                
                # Verify session was saved
                account.refresh_from_db()
                if account.is_session_valid():
                    logger.info(f"   ✅ Session data saved successfully")
                else:
                    logger.warning(f"   ⚠️  Authentication succeeded but session not valid")
                
                return auth_result
            else:
                error_msg = auth_result.get('error', 'Unknown authentication error')
                logger.error(f"   ❌ TikTok authentication failed: {error_msg}")
                return auth_result
                
        except Exception as e:
            logger.error(f"   ❌ TikTok re-authentication error: {str(e)}")
            return {
                'success': False,
                'error': f'Re-authentication error: {str(e)}'
            }
    
    def get_account_by_id(self, account_id: int) -> Optional[ActorAccount]:
        """
        Get actor account by ID
        
        Args:
            account_id: Account ID
            
        Returns:
            ActorAccount instance or None
        """
        try:
            return ActorAccount.objects.get(id=account_id, is_active=True)
        except ActorAccount.DoesNotExist:
            logger.error(f"Account {account_id} not found or not active")
            return None
    
    def check_specific_account(self, account_id: int) -> Dict:
        """
        Check a specific account by ID
        
        Args:
            account_id: Account ID to check
            
        Returns:
            Dict with account session status
        """
        account = self.get_account_by_id(account_id)
        if not account:
            return {
                'success': False,
                'error': f'Account {account_id} not found'
            }
        
        logger.info(f"🔍 Checking specific account: {account.platform_username} (ID: {account_id})")
        result = self.check_and_reauth_account(account)
        
        return {
            'success': True,
            'account': result
        }

def main():
    """
    Main function to run the session checker
    """
    logger.info("🚀 Starting Actor Account Session Checker...")
    
    session_manager = ActorSessionManager()
    
    # Check if specific account ID was provided as argument
    if len(sys.argv) > 1:
        try:
            account_id = int(sys.argv[1])
            logger.info(f"Checking specific account ID: {account_id}")
            result = session_manager.check_specific_account(account_id)
            
            if result['success']:
                account_info = result['account']
                print(f"\n📋 Account {account_id} Status:")
                print(f"   Platform: {account_info['platform']}")
                print(f"   Username: {account_info['username']}")
                print(f"   Session Valid: {account_info['session_valid']}")
                print(f"   Re-auth Attempted: {account_info['reauth_attempted']}")
                print(f"   Re-auth Success: {account_info['reauth_success']}")
                if account_info['error']:
                    print(f"   Error: {account_info['error']}")
            else:
                print(f"\n❌ Error: {result['error']}")
                
        except ValueError:
            logger.error("Invalid account ID provided. Please provide a numeric account ID.")
            sys.exit(1)
    else:
        # Check all accounts
        result = session_manager.check_all_actor_sessions()
        
        if result['success']:
            print(f"\n✅ Session check completed successfully")
        else:
            print(f"\n❌ Session check failed: {result.get('error')}")
            sys.exit(1)

if __name__ == '__main__':
    main()