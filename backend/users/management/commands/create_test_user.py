from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model

User = get_user_model()

class Command(BaseCommand):
    help = 'Create a test user for development'

    def handle(self, *args, **options):
        if User.objects.filter(username='admin').exists():
            self.stdout.write(self.style.WARNING('Test user already exists'))
            return

        user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_staff=True,
            is_superuser=True,
            is_active=True
        )

        self.stdout.write(self.style.SUCCESS(f'Successfully created test user: {user.username}'))
        self.stdout.write('Email: <EMAIL>')
        self.stdout.write('Password: admin123') 