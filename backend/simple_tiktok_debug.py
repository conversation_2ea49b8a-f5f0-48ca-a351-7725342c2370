# Simple TikTok Debug Script for Django Shell
# Run with: python manage.py shell < simple_tiktok_debug.py

from actor.models import ActorAccount, ActorTask, ActorScrapedData
from actor.engines.tiktok_engine import TikTokEngine
from datetime import datetime
import logging

print("🚀 Starting TikTok Debug Tests")
print(f"⏰ Timestamp: {datetime.now()}")

# Test 1: Check TikTok accounts
print("\n=== Checking TikTok Accounts ===")
tiktok_accounts = ActorAccount.objects.filter(platform='tiktok')
print(f"📊 Found {tiktok_accounts.count()} TikTok accounts")

if tiktok_accounts.exists():
    for account in tiktok_accounts:
        print(f"  - @{account.platform_username} (ID: {account.id})")
        print(f"    Last login: {account.last_login}")
        print(f"    Session expires: {account.session_expires_at}")
        
        # Test session validity
        engine = TikTokEngine()
        session_valid = engine.verify_session(account)
        print(f"    Session valid: {session_valid}")
        
        # Check password availability
        try:
            password = account.get_decrypted_password()
            has_password = bool(password)
            print(f"    Has password: {has_password}")
        except Exception as e:
            print(f"    Password error: {str(e)}")
else:
    print("❌ No TikTok accounts found")

# Test 2: Check recent tasks
print("\n=== Checking Recent TikTok Tasks ===")
recent_tasks = ActorTask.objects.filter(
    actor_account__platform='tiktok'
).order_by('-created_at')[:5]

print(f"📊 Found {recent_tasks.count()} recent TikTok tasks")

for task in recent_tasks:
    print(f"  Task {task.id}: {task.task_type} - {task.status}")
    print(f"    Keywords: {task.keywords}")
    print(f"    Items scraped: {task.items_scraped}")
    print(f"    Created: {task.created_at}")
    print(f"    Account: @{task.actor_account.platform_username}")
    
    # Check if this task has any scraped data
    scraped_data = ActorScrapedData.objects.filter(task=task)
    print(f"    Scraped data records: {scraped_data.count()}")
    print()

# Test 3: Test engine search with first account
if tiktok_accounts.exists():
    print("\n=== Testing Engine Search ===")
    account = tiktok_accounts.first()
    print(f"🔍 Testing with account: @{account.platform_username}")
    
    try:
        engine = TikTokEngine()
        
        # Test simple search
        print("🎯 Testing search for 'dance' (limit: 3)")
        results = engine.search_content(
            account=account,
            keywords=['dance'],
            limit=3
        )
        
        print(f"📊 Search returned {len(results)} results")
        
        if results:
            print("✅ Search successful - sample results:")
            for i, result in enumerate(results[:2]):
                print(f"  {i+1}. {result.get('desc', 'No description')[:50]}...")
                print(f"     Author: {result.get('author', {}).get('uniqueId', 'Unknown')}")
                print(f"     Real scraped: {result.get('real_scraped', False)}")
                print(f"     Source: {result.get('source', 'Unknown')}")
        else:
            print("❌ Search returned no results")
            
    except Exception as e:
        print(f"❌ Search test failed: {str(e)}")
        import traceback
        traceback.print_exc()

# Test 4: Check data distribution
print("\n=== Data Distribution Analysis ===")
total_tiktok_data = ActorScrapedData.objects.filter(
    actor_account__platform='tiktok'
).count()
print(f"📊 Total TikTok data items: {total_tiktok_data}")

# Group by task
from django.db.models import Count
task_data_counts = ActorScrapedData.objects.filter(
    actor_account__platform='tiktok'
).values('task_id').annotate(count=Count('id')).order_by('-count')

print("📈 Data by task:")
for item in task_data_counts[:10]:
    task_id = item['task_id']
    count = item['count']
    try:
        task = ActorTask.objects.get(id=task_id)
        print(f"  Task {task_id}: {count} items - {task.task_type} - {task.status}")
    except:
        print(f"  Task {task_id}: {count} items - (task not found)")

print("\n=== Debug Summary ===")
print(f"🔐 TikTok Accounts: {tiktok_accounts.count()}")
print(f"📋 Recent Tasks: {recent_tasks.count()}")
print(f"📊 Total Data Items: {total_tiktok_data}")

if recent_tasks.exists():
    # Count completed tasks with zero items using a fresh query
    completed_with_zero = ActorTask.objects.filter(
        actor_account__platform='tiktok',
        status='COMPLETED',
        items_scraped=0
    ).count()
    print(f"⚠️ Completed tasks with 0 items: {completed_with_zero}")
    
    if completed_with_zero > 0:
        print("\n🎯 ISSUE IDENTIFIED: Tasks completing with 0 items")
        print("   Possible causes:")
        print("   1. Authentication failing during scraping")
        print("   2. TikTok page structure changes")
        print("   3. Anti-bot measures blocking scraping")
        print("   4. Selenium WebDriver issues")
        print("   5. Network connectivity problems")

print("\n✅ Debug analysis completed")