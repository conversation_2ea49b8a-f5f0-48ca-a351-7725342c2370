import django_filters
from .models import Tik<PERSON><PERSON>Task, ScrapedData
from django.db import models

class TikTokTaskFilter(django_filters.FilterSet):
    job_name = django_filters.CharFilter(lookup_expr='icontains')
    task_type = django_filters.ChoiceFilter(choices=TikTokTask.TASK_TYPE_CHOICES)
    status = django_filters.ChoiceFilter(choices=TikTokTask.STATUS_CHOICES)
    
    created_at = django_filters.DateFromToRangeFilter()
    completed_at = django_filters.DateFromToRangeFilter()
    
    class Meta:
        model = TikTokTask
        fields = {
            'job_name': ['exact', 'icontains'],
            'task_type': ['exact'],
            'identifier': ['exact', 'icontains'],
            'status': ['exact'],
            'created_at': ['exact', 'gte', 'lte'],
            'updated_at': ['exact', 'gte', 'lte'],
            'completed_at': ['exact', 'gte', 'lte'],
        }
        filter_overrides = {
            models.CharField: {
                'filter_class': django_filters.CharFilter,
                'extra': lambda f: {
                    'lookup_expr': 'icontains',
                },
            },
        }

class ScrapedDataFilter(django_filters.FilterSet):
    data_type = django_filters.ChoiceFilter(choices=ScrapedData.DATA_TYPE_CHOICES)
    scraped_at = django_filters.DateFromToRangeFilter()
    
    class Meta:
        model = ScrapedData
        fields = {
            'task': ['exact'],
            'data_type': ['exact'],
            'scraped_at': ['exact', 'gte', 'lte'],
        }