#!/usr/bin/env python
import os
import sys
import django
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from crawler_tiktok.models import TikTokTask
from crawler_tiktok.utils.driver_setup import WebDriverPool
from crawler_tiktok.utils.tiktok_crawler import TikTokCrawler

def test_crawler():
    """Test the TikTok crawler functionality."""
    print("Testing TikTok Crawler...")
    
    try:
        # Test WebDriver creation
        print("1. Testing WebDriver creation...")
        pool = WebDriverPool(max_drivers=1)
        driver_id, driver = pool.get_driver()
        print("✓ WebDriver created successfully")
        
        # Test basic navigation
        print("2. Testing basic navigation...")
        driver.get("https://www.tiktok.com")
        print(f"✓ Navigated to TikTok, title: {driver.title}")
        
        # Test TikTokCrawler initialization
        print("3. Testing TikTokCrawler initialization...")
        crawler = TikTokCrawler(driver)
        print("✓ TikTokCrawler initialized successfully")
        
        # Clean up
        pool.return_driver(driver_id)
        pool.cleanup()
        print("✓ Cleanup completed")
        
        print("\n🎉 All tests passed! The crawler setup is working.")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def check_recent_tasks():
    """Check recent tasks and their status."""
    print("\nChecking recent tasks...")
    
    try:
        tasks = TikTokTask.objects.all().order_by('-created_at')[:5]
        
        if not tasks:
            print("No tasks found in database.")
            return
        
        print(f"Found {len(tasks)} recent tasks:")
        for task in tasks:
            print(f"  ID: {task.id}")
            print(f"  Status: {task.status}")
            print(f"  Type: {task.task_type}")
            print(f"  Identifier: {task.identifier}")
            print(f"  Created: {task.created_at}")
            if task.error_message:
                print(f"  Error: {task.error_message}")
            print("  ---")
            
    except Exception as e:
        print(f"Error checking tasks: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("TikTok Crawler Diagnostic Tool")
    print("=" * 40)
    
    # Check recent tasks first
    check_recent_tasks()
    
    # Test crawler functionality
    test_crawler()