from django.core.cache import cache
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from .models import TikTokTask
from .config import MONITORING

class CrawlerMonitor:
    @staticmethod
    def get_task_statistics(time_window=None):
        """Get statistics for tasks within the specified time window."""
        if time_window is None:
            time_window = MONITORING['health_check']['time_window']

        start_time = timezone.now() - timedelta(seconds=time_window)
        tasks = TikTokTask.objects.filter(created_at__gte=start_time)

        total_tasks = tasks.count()
        if total_tasks == 0:
            return {
                'total_tasks': 0,
                'success_rate': 1.0,  # No failures if no tasks
                'error_rate': 0.0,
                'avg_completion_time': 0.0
            }

        completed_tasks = tasks.filter(status='completed')
        failed_tasks = tasks.filter(status='failed')
        pending_tasks = tasks.filter(status='pending')

        # Calculate completion times for finished tasks
        completion_times = []
        for task in completed_tasks:
            if task.completed_at and task.created_at:
                completion_time = (task.completed_at - task.created_at).total_seconds()
                completion_times.append(completion_time)

        avg_completion_time = sum(completion_times) / len(completion_times) if completion_times else 0

        return {
            'total_tasks': total_tasks,
            'completed_tasks': completed_tasks.count(),
            'failed_tasks': failed_tasks.count(),
            'pending_tasks': pending_tasks.count(),
            'success_rate': completed_tasks.count() / total_tasks,
            'error_rate': failed_tasks.count() / total_tasks,
            'avg_completion_time': avg_completion_time
        }

    @staticmethod
    def get_error_summary(time_window=None):
        """Get summary of errors from failed tasks."""
        if time_window is None:
            time_window = MONITORING['health_check']['time_window']

        start_time = timezone.now() - timedelta(seconds=time_window)
        failed_tasks = TikTokTask.objects.filter(
            status='failed',
            created_at__gte=start_time
        )

        error_counts = {}
        for task in failed_tasks:
            error_type = task.error_message.split(':')[0] if task.error_message else 'Unknown'
            error_counts[error_type] = error_counts.get(error_type, 0) + 1

        return {
            'total_errors': failed_tasks.count(),
            'error_types': error_counts,
            'time_window': time_window
        }

    @staticmethod
    def check_health():
        """Check the overall health of the crawler system."""
        stats = CrawlerMonitor.get_task_statistics()
        thresholds = MONITORING['health_check']

        status = 'healthy'
        issues = []

        # Check error rate
        if stats['error_rate'] >= thresholds['critical_threshold']['error_rate']:
            status = 'critical'
            issues.append('Critical error rate')
        elif stats['error_rate'] >= thresholds['warning_threshold']['error_rate']:
            status = 'warning'
            issues.append('High error rate')

        # Check success rate
        if stats['success_rate'] <= thresholds['critical_threshold']['success_rate']:
            status = 'critical'
            issues.append('Critical success rate')
        elif stats['success_rate'] <= thresholds['warning_threshold']['success_rate']:
            status = 'warning'
            issues.append('Low success rate')

        # Check average completion time
        if stats['avg_completion_time'] >= thresholds['critical_threshold']['avg_response_time']:
            status = 'critical'
            issues.append('Critical response time')
        elif stats['avg_completion_time'] >= thresholds['warning_threshold']['avg_response_time']:
            status = 'warning'
            issues.append('High response time')

        # Format the message based on issues
        message = 'System is healthy' if status == 'healthy' else ', '.join(issues)
        
        # Format the response to match the frontend's HealthStatus interface
        return {
            'status': status,
            'message': message,
            'metrics': {
                'success_rate': stats['success_rate'],
                'error_rate': stats['error_rate'],
                'average_response_time': stats['avg_completion_time']
            }
        }

    @staticmethod
    def log_metrics(task_id, metrics):
        """Log metrics for a specific task."""
        cache_key = f'task_metrics:{task_id}'
        cache.set(
            cache_key,
            metrics,
            timeout=MONITORING['metrics']['cache_duration']
        )

    @staticmethod
    def get_task_metrics(task_id):
        """Get metrics for a specific task."""
        cache_key = f'task_metrics:{task_id}'
        return cache.get(cache_key)

    @staticmethod
    def cleanup_old_tasks():
        """Clean up old task records."""
        cleanup_config = MONITORING['task_cleanup']
        cutoff_date = timezone.now() - cleanup_config['max_age']
        
        # Delete in batches to avoid memory issues
        while True:
            tasks = TikTokTask.objects.filter(
                created_at__lt=cutoff_date
            )[:cleanup_config['batch_size']]
            
            if not tasks:
                break
                
            tasks.delete()