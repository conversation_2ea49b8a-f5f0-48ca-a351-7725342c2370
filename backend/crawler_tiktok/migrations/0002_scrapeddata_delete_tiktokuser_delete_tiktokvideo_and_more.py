# Generated by Django 5.2.4 on 2025-07-10 20:50

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('crawler_tiktok', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ScrapedData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data_type', models.CharField(choices=[('VIDEO', 'Video Data'), ('USER', 'User Data'), ('SEARCH_RESULT', 'Search Result Data')], help_text='Type of scraped data', max_length=20)),
                ('content', models.JSONField(help_text='JSON content of the scraped data')),
                ('scraped_at', models.DateTimeField(auto_now_add=True, help_text='Timestamp when the data was scraped')),
            ],
            options={
                'verbose_name_plural': 'Scraped Data',
            },
        ),
        migrations.DeleteModel(
            name='TikTokUser',
        ),
        migrations.DeleteModel(
            name='TikTokVideo',
        ),
        migrations.RemoveField(
            model_name='tiktoktask',
            name='param',
        ),
        migrations.RemoveField(
            model_name='tiktoktask',
            name='result',
        ),
        migrations.RemoveField(
            model_name='tiktoktask',
            name='scrape_type',
        ),
        migrations.RemoveField(
            model_name='tiktoktask',
            name='task_id',
        ),
        migrations.AddField(
            model_name='tiktoktask',
            name='celery_task_id',
            field=models.CharField(blank=True, help_text='Celery task ID if applicable', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='tiktoktask',
            name='end_date',
            field=models.DateField(blank=True, help_text='End date for data collection (e.g., for search/user posts)', null=True),
        ),
        migrations.AddField(
            model_name='tiktoktask',
            name='identifier',
            field=models.CharField(default='default_identifier', help_text='Identifier for the task (e.g., video URL, username, search keyword)', max_length=255),
        ),
        migrations.AddField(
            model_name='tiktoktask',
            name='job_name',
            field=models.CharField(default='default_job', help_text='Name of the scraping job', max_length=255),
        ),
        migrations.AddField(
            model_name='tiktoktask',
            name='start_date',
            field=models.DateField(blank=True, help_text='Start date for data collection (e.g., for search/user posts)', null=True),
        ),
        migrations.AddField(
            model_name='tiktoktask',
            name='task_type',
            field=models.CharField(choices=[('VIDEO', 'Video Scrape'), ('USER', 'User Scrape'), ('SEARCH', 'Search Scrape')], default='VIDEO', help_text='Type of TikTok content to scrape', max_length=10),
        ),
        migrations.AddField(
            model_name='tiktoktask',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, help_text='Timestamp when the task was last updated'),
        ),
        migrations.AlterField(
            model_name='tiktoktask',
            name='completed_at',
            field=models.DateTimeField(blank=True, help_text='Timestamp when the task was completed', null=True),
        ),
        migrations.AlterField(
            model_name='tiktoktask',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, help_text='Timestamp when the task was created'),
        ),
        migrations.AlterField(
            model_name='tiktoktask',
            name='error_message',
            field=models.TextField(blank=True, help_text='Error message if the task failed', null=True),
        ),
        migrations.AlterField(
            model_name='tiktoktask',
            name='status',
            field=models.CharField(choices=[('PENDING', 'Pending'), ('RUNNING', 'Running'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('CANCELLED', 'Cancelled')], default='PENDING', help_text='Current status of the task', max_length=10),
        ),
        migrations.AddField(
            model_name='scrapeddata',
            name='task',
            field=models.ForeignKey(help_text='Associated TikTok task', on_delete=django.db.models.deletion.CASCADE, related_name='scraped_data', to='crawler_tiktok.tiktoktask'),
        ),
    ]
