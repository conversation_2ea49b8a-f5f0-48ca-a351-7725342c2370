# TikTok Scraped Data Access Guide

## Overview
When TikTok scraping tasks complete successfully, the extracted data is saved to the database with rich metadata including content text, likes, shares, comments, and more.

## Available Metadata

### For Search Results (`SEARCH_RESULT`)
- **description** (`desc`): Video description/caption text
- **author**: Username of the video creator (e.g., `@username`)
- **caption**: Additional video caption text
- **stats**:
  - `likes`: Number of likes
  - `comments`: Number of comments
  - `shares`: Number of shares (when available)
- **id**: Unique identifier for the video
- **createTime**: When the video was created

### For Video Data (`VIDEO`)
- **description** (`desc`): Video description text
- **author**: Username of the video creator
- **stats**:
  - `likes`: Number of likes
  - `comments`: Number of comments
  - `shares`: Number of shares
- **music**: Music/audio information
- **video**: Video file information

### For User Data (`USER`)
- **nickname**: Display name of the user
- **uniqueId**: Unique username handle
- **signature**: User bio/description
- **stats**:
  - `following`: Number of accounts they follow
  - `followers`: Number of followers
  - `likes`: Total likes received
- **verified**: Whether the account is verified

## API Endpoints to Access Scraped Data

### 1. Get All Scraped Data
```
GET /api/crawler-tiktok/scraped-data/
```
Returns all scraped data entries with pagination.

### 2. Get Data for Specific Task
```
GET /api/crawler-tiktok/scraped-data/by_task/?task_id=<TASK_ID>
```
Returns formatted data for a specific task with enhanced metadata extraction.

**Example Response:**
```json
{
  "task": {
    "id": 1,
    "job_name": "Prabowo Search",
    "task_type": "SEARCH",
    "identifier": "prabowo",
    "status": "COMPLETED"
  },
  "results_count": 5,
  "results": [
    {
      "id": 1,
      "data_type": "SEARCH_RESULT",
      "scraped_at": "2024-01-15T10:30:00Z",
      "content": {
        "id": "search_0",
        "desc": "Prabowo campaign video highlights",
        "author": "@prabowosubianto",
        "stats": {
          "likes": 15000,
          "comments": 1200,
          "shares": 800
        }
      },
      "metadata": {
        "description": "Prabowo campaign video highlights",
        "author": "@prabowosubianto",
        "likes": 15000,
        "comments": 1200,
        "shares": 800,
        "video_id": "search_0",
        "create_time": "N/A"
      }
    }
  ]
}
```

### 3. Get Summary of All Scraped Data
```
GET /api/crawler-tiktok/scraped-data/summary/
```
Returns a summary with statistics and recent items.

**Example Response:**
```json
{
  "total_scraped_items": 25,
  "by_data_type": {
    "VIDEO": 5,
    "USER": 3,
    "SEARCH_RESULT": 17
  },
  "recent_items": [
    {
      "id": 1,
      "task_id": 1,
      "task_type": "SEARCH",
      "data_type": "SEARCH_RESULT",
      "scraped_at": "2024-01-15T10:30:00Z",
      "preview": {
        "description": "Prabowo campaign video highlights...",
        "author": "@prabowosubianto",
        "likes": 15000,
        "comments": 1200
      }
    }
  ]
}
```

### 4. Filter by Data Type
```
GET /api/crawler-tiktok/scraped-data/?data_type=SEARCH_RESULT
GET /api/crawler-tiktok/scraped-data/?data_type=VIDEO
GET /api/crawler-tiktok/scraped-data/?data_type=USER
```

### 5. Filter by Task
```
GET /api/crawler-tiktok/scraped-data/?task=<TASK_ID>
```

## Frontend Integration

To display scraped data in your frontend:

1. **After creating a task**, save the task ID
2. **Poll the task status** until it's `COMPLETED`
3. **Fetch the scraped data** using the `/by_task/` endpoint
4. **Display the metadata** in a user-friendly format

### Example Frontend Code (React/TypeScript)
```typescript
// Fetch scraped data for a completed task
const fetchScrapedData = async (taskId: number) => {
  const response = await fetch(`/api/crawler-tiktok/scraped-data/by_task/?task_id=${taskId}`);
  const data = await response.json();
  
  return data.results.map(item => ({
    id: item.id,
    description: item.metadata.description,
    author: item.metadata.author,
    likes: item.metadata.likes,
    comments: item.metadata.comments,
    shares: item.metadata.shares,
    scrapedAt: item.scraped_at
  }));
};
```

## Database Access (Django Shell)

For direct database access:

```python
from crawler_tiktok.models import TikTokTask, ScrapedData

# Get all completed tasks
completed_tasks = TikTokTask.objects.filter(status='COMPLETED')

# Get scraped data for a specific task
task = TikTokTask.objects.get(id=1)
scraped_data = task.scraped_data.all()

# Access the content
for data in scraped_data:
    content = data.content
    print(f"Description: {content.get('desc')}")
    print(f"Author: {content.get('author')}")
    print(f"Likes: {content.get('stats', {}).get('likes', 0)}")
    print(f"Comments: {content.get('stats', {}).get('comments', 0)}")
```

## Troubleshooting

### No Data in Results
1. **Check task status**: Ensure the task completed successfully
2. **Check error messages**: Look at the task's `error_message` field
3. **Verify selectors**: TikTok may have changed their HTML structure
4. **Check logs**: Look at the crawler logs for detailed error information

### Missing Metadata
1. **Content structure**: TikTok's data structure may vary
2. **Fallback values**: The system provides fallback values (0, 'N/A') when data is missing
3. **Data processor**: Check if the data processor is extracting from the correct HTML elements

## Data Structure Notes

- All scraped data is stored as JSON in the `content` field
- The enhanced API endpoints extract common fields into a `metadata` object for easier access
- Numbers (likes, comments, shares) are parsed from TikTok's formatted strings (e.g., "1.2K" → 1200)
- Missing or unavailable data is represented as 0 for numbers and 'N/A' for strings