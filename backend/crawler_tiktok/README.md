# TikTok Crawler

A robust Django application for scraping TikTok content with advanced features for error handling, rate limiting, and monitoring.

## Features

### Content Scraping
- Video scraping with metadata extraction
- User profile scraping
- Search results scraping
- Configurable scroll and wait times

### Error Handling
- Custom exception hierarchy for different error types
- Automatic retries with exponential backoff
- Detailed error logging and monitoring

### Performance Optimization
- WebDriver pooling for efficient resource usage
- Result caching to reduce duplicate requests
- Configurable batch processing

### Rate Limiting
- Per-endpoint rate limiting
- IP-based request tracking
- Configurable time windows and limits

### Monitoring and Health Checks
- Real-time health status monitoring
- Task completion statistics
- Error rate tracking
- Performance metrics collection

## API Endpoints

### Videos
- `POST /videos/scrape/`: Start video scraping task
- `GET /videos/{id}/status/`: Check video scraping status

### Users
- `POST /users/scrape/`: Start user profile scraping
- `GET /users/{id}/status/`: Check user scraping status

### Search
- `POST /tasks/search/`: Start search scraping task
- `GET /tasks/{id}/status/`: Check search task status

### Monitoring
- `GET /health/`: System health check
- `GET /tasks/statistics/`: Task statistics (admin only)
- `GET /tasks/errors/`: Error summary (admin only)

## Configuration

Key settings are managed in `config.py`:

### Rate Limits
```python
RATE_LIMITS = {
    'video': {'requests': 100, 'window': 3600},  # 100 requests per hour
    'user': {'requests': 200, 'window': 3600},    # 200 requests per hour
    'search': {'requests': 50, 'window': 3600},   # 50 requests per hour
}
```

### Monitoring Thresholds
```python
MONITORING = {
    'health_check': {
        'warning_threshold': {
            'error_rate': 0.1,        # 10% error rate
            'success_rate': 0.8,       # 80% success rate
            'avg_response_time': 10.0  # 10 seconds
        }
    }
}
```

### Crawler Settings
```python
CRAWLER = {
    'retry': {
        'max_attempts': 3,
        'min_wait': 4,
        'max_wait': 10
    }
}
```

## Maintenance

### Automatic Cleanup
- Old task records are automatically cleaned up daily
- Configurable retention period (default: 7 days)
- Batch processing to avoid memory issues

### Health Monitoring
- System health is continuously monitored
- Alerts on high error rates or slow response times
- Detailed metrics for debugging and optimization

## Dependencies

- Django
- Celery
- Selenium
- Redis (for caching and rate limiting)