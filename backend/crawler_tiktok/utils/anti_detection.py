import random
import time
import json
from datetime import datetime
from typing import Dict, List, Optional
import logging

logger = logging.getLogger(__name__)

# Modern browsers and their typical user agent patterns
BROWSER_PATTERNS = {
    'chrome': [
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ],
    'firefox': [
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/20100101 Firefox/120.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
        'Mozilla/5.0 (X11; Linux x86_64; rv:120.0) Gecko/20100101 Firefox/120.0'
    ],
    'safari': [
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15'
    ]
}

def get_random_user_agent() -> str:
    """Get a random user agent string from modern browser patterns."""
    browser = random.choice(list(BROWSER_PATTERNS.keys()))
    return random.choice(BROWSER_PATTERNS[browser])

def random_delay(min_delay: float = 1, max_delay: float = 5) -> None:
    """Implement a random delay with microsecond precision."""
    delay = random.uniform(min_delay, max_delay)
    time.sleep(delay)

def simulate_human_interaction(driver) -> None:
    """Simulate realistic human-like behavior in the browser."""
    try:
        # Random initial delay
        random_delay(0.5, 2.0)

        # Simulate natural viewport movement
        viewport_height = driver.execute_script('return window.innerHeight')
        page_height = driver.execute_script('return document.body.scrollHeight')
        current_position = 0

        while current_position < page_height:
            # Random scroll amount
            scroll_amount = random.randint(100, viewport_height // 2)
            current_position += scroll_amount

            # Smooth scroll with easing
            driver.execute_script(f"""
                window.scrollTo({{top: {current_position}, behavior: 'smooth'}});
            """)

            # Random pause between scrolls
            random_delay(0.5, 2.0)

            # Occasional mouse movements
            if random.random() < 0.3:  # 30% chance
                simulate_mouse_movement(driver)

    except Exception as e:
        logger.warning(f"Error during human interaction simulation: {str(e)}")

def simulate_mouse_movement(driver) -> None:
    """Simulate natural mouse movements."""
    try:
        viewport_width = driver.execute_script('return window.innerWidth')
        viewport_height = driver.execute_script('return window.innerHeight')

        # Generate random points for mouse movement
        points = [
            (random.randint(0, viewport_width), random.randint(0, viewport_height))
            for _ in range(3)
        ]

        for x, y in points:
            driver.execute_script(f"""
                var event = new MouseEvent('mousemove', {{
                    bubbles: true,
                    cancelable: true,
                    clientX: {x},
                    clientY: {y}
                }});
                document.dispatchEvent(event);
            """)
            random_delay(0.1, 0.3)

    except Exception as e:
        logger.warning(f"Error during mouse movement simulation: {str(e)}")

def modify_webdriver_properties(driver) -> None:
    """Modify WebDriver properties to avoid detection."""
    try:
        # Remove webdriver properties
        driver.execute_script("""
            Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        """)

        # Add random plugins and mime types
        driver.execute_script("""
            const pluginArray = [
                {name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer'},
                {name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai'},
                {name: 'Native Client', filename: 'internal-nacl-plugin'}
            ];
            Object.defineProperty(navigator, 'plugins', {
                get: () => pluginArray
            });
        """)

    except Exception as e:
        logger.warning(f"Error modifying WebDriver properties: {str(e)}")

def setup_browser_environment(driver) -> None:
    """Set up a complete browser environment to avoid detection."""
    try:
        # Set common browser features
        driver.execute_script("""
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en']
            });
            Object.defineProperty(navigator, 'platform', {
                get: () => 'MacIntel'
            });
        """)

        # Add touch support simulation
        driver.execute_script("""
            Object.defineProperty(navigator, 'maxTouchPoints', {
                get: () => 5
            });
        """)

        # Modify WebGL properties
        driver.execute_script("""
            const getParameter = WebGLRenderingContext.prototype.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                if (parameter === 37445) {
                    return 'Intel Open Source Technology Center';
                }
                if (parameter === 37446) {
                    return 'Mesa DRI Intel(R) Iris(R) Plus Graphics (ICL GT2)';
                }
                return getParameter.apply(this, arguments);
            };
        """)

    except Exception as e:
        logger.warning(f"Error setting up browser environment: {str(e)}")