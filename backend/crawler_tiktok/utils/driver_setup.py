import os
import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import WebDriverException
from typing import Optional, Dict

from .anti_detection import (
    get_random_user_agent,
    modify_webdriver_properties,
    setup_browser_environment
)

logger = logging.getLogger(__name__)

class WebDriverPool:
    def __init__(self, max_drivers: int = 3):
        self.max_drivers = max_drivers
        self.available_drivers = []
        self.in_use_drivers = {}
        self.last_driver_id = 0

    def get_driver(self, proxy: Optional[Dict[str, str]] = None) -> tuple[int, webdriver.Chrome]:
        """Get an available driver from the pool or create a new one."""
        try:
            if self.available_drivers:
                driver = self.available_drivers.pop(0)
                driver_id = self.last_driver_id + 1
                self.last_driver_id = driver_id
                self.in_use_drivers[driver_id] = driver
                return driver_id, driver

            if len(self.in_use_drivers) >= self.max_drivers:
                raise Exception("Maximum number of drivers reached")

            driver = self._create_driver(proxy)
            driver_id = self.last_driver_id + 1
            self.last_driver_id = driver_id
            self.in_use_drivers[driver_id] = driver
            return driver_id, driver

        except Exception as e:
            logger.error(f"Error getting WebDriver: {str(e)}")
            raise

    def return_driver(self, driver_id: int) -> None:
        """Return a driver to the pool."""
        try:
            if driver_id in self.in_use_drivers:
                driver = self.in_use_drivers.pop(driver_id)
                if self._is_driver_healthy(driver):
                    self.available_drivers.append(driver)
                else:
                    self._quit_driver(driver)

        except Exception as e:
            logger.error(f"Error returning WebDriver: {str(e)}")

    def _create_driver(self, proxy: Optional[Dict[str, str]] = None) -> webdriver.Chrome:
        """Create a new Chrome WebDriver instance with anti-detection measures."""
        try:
            options = Options()
            
            # Minimal Chrome options for stability
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--window-size=1920,1080')
            options.add_argument('--disable-blink-features=AutomationControlled')
            
            # Set user agent
            user_agent = get_random_user_agent()
            options.add_argument(f'--user-agent={user_agent}')
            
            # Basic anti-detection
            options.add_experimental_option('excludeSwitches', ['enable-automation'])
            options.add_experimental_option('useAutomationExtension', False)
            
            # Add proxy if provided
            if proxy:
                options.add_argument(f'--proxy-server={proxy["http"]}')

            # Set Chrome binary location for macOS
            chrome_path = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
            if os.path.exists(chrome_path):
                options.binary_location = chrome_path

            # Create driver with ChromeDriverManager
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)

            # Set window size and position
            driver.set_window_size(1920, 1080)
            
            # Apply additional anti-detection measures
            modify_webdriver_properties(driver)
            setup_browser_environment(driver)

            # Execute CDP commands to prevent detection
            driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                "userAgent": user_agent,
                "platform": "MacIntel",
                "acceptLanguage": "en-US,en;q=0.9"
            })

            return driver

        except Exception as e:
            logger.error(f"Error creating WebDriver: {str(e)}")
            raise

    def _is_driver_healthy(self, driver: webdriver.Chrome) -> bool:
        """Check if a driver is still functional."""
        try:
            driver.current_url
            return True
        except WebDriverException:
            return False

    def _quit_driver(self, driver: webdriver.Chrome) -> None:
        """Safely quit a driver."""
        try:
            driver.quit()
        except Exception as e:
            logger.error(f"Error quitting WebDriver: {str(e)}")

    def cleanup(self) -> None:
        """Clean up all drivers in the pool."""
        try:
            for driver in self.available_drivers:
                self._quit_driver(driver)
            self.available_drivers.clear()

            for driver in self.in_use_drivers.values():
                self._quit_driver(driver)
            self.in_use_drivers.clear()

        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")

    def __del__(self):
        """Ensure all drivers are cleaned up when the pool is destroyed."""
        self.cleanup()