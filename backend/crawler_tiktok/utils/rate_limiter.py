from django.core.cache import cache
from ..exceptions import TikTokScrapingException
from ..config import RATE_LIMITS

class RateLimiter:
    def __init__(self, key_prefix, max_requests, window):
        self.key_prefix = key_prefix
        self.max_requests = max_requests
        self.window = window  # Time window in seconds

    def _get_cache_key(self, identifier):
        return f"rate_limit:{self.key_prefix}:{identifier}"

    def check_and_update(self, identifier):
        cache_key = self._get_cache_key(identifier)
        request_count = cache.get(cache_key, 0)

        if request_count >= self.max_requests:
            raise TikTokScrapingException(
                f"Rate limit exceeded for {self.key_prefix}. "
                f"Maximum {self.max_requests} requests allowed per {self.window} seconds."
            )

        # Use cache.add for atomic increment
        if request_count == 0:
            cache.add(cache_key, 1, self.window)
        else:
            cache.incr(cache_key)

        return True

    def get_remaining(self, identifier):
        cache_key = self._get_cache_key(identifier)
        request_count = cache.get(cache_key, 0)
        return max(0, self.max_requests - request_count)

    def get_reset_time(self, identifier):
        cache_key = self._get_cache_key(identifier)
        ttl = cache.ttl(cache_key)
        return max(0, ttl)

# Initialize rate limiters using configuration
video_rate_limiter = RateLimiter(
    'video',
    RATE_LIMITS['video']['requests'],
    RATE_LIMITS['video']['window']
)

user_rate_limiter = RateLimiter(
    'user',
    RATE_LIMITS['user']['requests'],
    RATE_LIMITS['user']['window']
)

search_rate_limiter = RateLimiter(
    'search',
    RATE_LIMITS['search']['requests'],
    RATE_LIMITS['search']['window']
)

def check_rate_limit(limiter, identifier):
    """Check if the request should be rate limited.

    Args:
        limiter: RateLimiter instance to use
        identifier: Unique identifier for the client (e.g. IP address)

    Returns:
        bool: True if request is allowed

    Raises:
        TikTokScrapingException: If rate limit is exceeded
    """
    try:
        return limiter.check_and_update(identifier)
    except Exception as e:
        raise TikTokScrapingException(f"Rate limit check failed: {str(e)}")