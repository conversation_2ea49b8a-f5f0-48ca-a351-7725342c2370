import json
import logging
from typing import Dict, List, Optional, Union
from bs4 import BeautifulSoup
from datetime import datetime
import re

logger = logging.getLogger(__name__)

def extract_json_data(html_content: str) -> Optional[Dict]:
    """Extract JSON data from various TikTok data structures in the page."""
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Try JSON-LD data first
        json_ld = soup.find('script', {'type': 'application/ld+json'})
        if json_ld:
            try:
                return json.loads(json_ld.string)
            except (json.JSONDecodeError, AttributeError):
                pass

        # Try SIGI_STATE data
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string and 'SIGI_STATE' in script.string:
                match = re.search(r'window\[\'SIGI_STATE\'\]=(.*?);</script>', script.string)
                if match:
                    try:
                        return json.loads(match.group(1))
                    except json.JSONDecodeError:
                        continue

        # Try universal rehydration data
        for script in scripts:
            if script.string and '__UNIVERSAL_DATA_FOR_REHYDRATION__' in script.string:
                match = re.search(r'window\[\'__UNIVERSAL_DATA_FOR_REHYDRATION__\'\]=(.*?);</script>', script.string)
                if match:
                    try:
                        return json.loads(match.group(1))
                    except json.JSONDecodeError:
                        continue

        return None

    except Exception as e:
        logger.error(f"Error extracting JSON data: {str(e)}")
        return None

def process_json_data(json_data: Dict, data_type: str) -> Optional[Union[Dict, List[Dict]]]:
    """Process extracted JSON data based on the type of content."""
    try:
        if data_type == 'video':
            if 'ItemModule' in json_data:
                video_data = next(iter(json_data['ItemModule'].values()))
                
                # Extract comprehensive metadata
                author_meta = video_data.get('author', {})
                stats = video_data.get('stats', {})
                music_meta = video_data.get('music', {})
                video_meta = video_data.get('video', {})
                
                # Parse create time
                create_time = video_data.get('createTime')
                create_time_iso = None
                if create_time:
                    try:
                        create_time_iso = datetime.fromtimestamp(create_time).isoformat()
                    except (ValueError, TypeError):
                        create_time_iso = None
                
                # Extract hashtags and mentions
                desc = video_data.get('desc', '')
                hashtags = [tag.strip('#') for tag in re.findall(r'#\w+', desc)]
                mentions = [mention.strip('@') for mention in re.findall(r'@\w+', desc)]
                
                # Extract detailed mentions from challenges
                detailed_mentions = []
                challenges = video_data.get('challenges', [])
                for challenge in challenges:
                    if challenge.get('title'):
                        detailed_mentions.append({
                            'title': challenge.get('title'),
                            'id': challenge.get('id'),
                            'desc': challenge.get('desc')
                        })
                
                # Extract effect stickers
                effect_stickers = video_data.get('effectStickers', [])
                
                # Extract media URLs
                media_urls = []
                if video_meta.get('playAddr'):
                    media_urls.append(video_meta.get('playAddr'))
                if video_meta.get('downloadAddr'):
                    media_urls.append(video_meta.get('downloadAddr'))
                
                return {
                    # Basic identifiers
                    'id': video_data.get('id'),
                    'input': video_data.get('webVideoUrl', ''),
                    
                    # Content
                    'text': desc,
                    'desc': desc,
                    'textLanguage': video_data.get('textLanguage', 'N/A'),
                    
                    # Author metadata
                    'author': author_meta.get('uniqueId', 'N/A'),
                    'authorMeta': {
                        'id': author_meta.get('id'),
                        'uniqueId': author_meta.get('uniqueId'),
                        'nickname': author_meta.get('nickname'),
                        'signature': author_meta.get('signature'),
                        'avatar': author_meta.get('avatarLarger'),
                        'verified': author_meta.get('verified', False),
                        'secUid': author_meta.get('secUid'),
                        'privateAccount': author_meta.get('privateAccount', False)
                    },
                    
                    # Engagement metrics
                    'collectCount': stats.get('collectCount', 0),
                    'commentCount': stats.get('commentCount', 0),
                    'diggCount': stats.get('diggCount', 0),
                    'playCount': stats.get('playCount', 0),
                    'shareCount': stats.get('shareCount', 0),
                    
                    # Legacy stats format for backward compatibility
                    'stats': stats,
                    
                    # Time information
                    'createTime': create_time,
                    'createTimeISO': create_time_iso,
                    
                    # Content classification
                    'isAd': video_data.get('isAd', False),
                    'isPinned': video_data.get('isPinned', False),
                    'isSlideshow': video_data.get('isSlideshow', False),
                    'isSponsored': video_data.get('isSponsored', False),
                    
                    # Social elements
                    'hashtags': hashtags,
                    'mentions': mentions,
                    'detailedMentions': detailed_mentions,
                    'searchHashtag': hashtags[0] if hashtags else None,
                    
                    # Media information
                    'mediaUrls': media_urls,
                    'webVideoUrl': video_data.get('webVideoUrl'),
                    
                    # Music metadata
                    'musicMeta': {
                        'musicId': music_meta.get('id'),
                        'musicName': music_meta.get('title'),
                        'musicAuthor': music_meta.get('authorName'),
                        'musicOriginal': music_meta.get('original'),
                        'musicAlbum': music_meta.get('album'),
                        'playUrl': music_meta.get('playUrl'),
                        'coverLarge': music_meta.get('coverLarge'),
                        'coverMedium': music_meta.get('coverMedium'),
                        'coverThumb': music_meta.get('coverThumb'),
                        'duration': music_meta.get('duration')
                    },
                    
                    # Video metadata
                    'videoMeta': {
                        'height': video_meta.get('height'),
                        'width': video_meta.get('width'),
                        'duration': video_meta.get('duration'),
                        'ratio': video_meta.get('ratio'),
                        'cover': video_meta.get('cover'),
                        'originCover': video_meta.get('originCover'),
                        'dynamicCover': video_meta.get('dynamicCover'),
                        'playAddr': video_meta.get('playAddr'),
                        'downloadAddr': video_meta.get('downloadAddr'),
                        'shareCover': video_meta.get('shareCover'),
                        'reflowCover': video_meta.get('reflowCover'),
                        'bitrate': video_meta.get('bitrate'),
                        'encodedType': video_meta.get('encodedType'),
                        'format': video_meta.get('format'),
                        'videoQuality': video_meta.get('videoQuality'),
                        'encodeUserTag': video_meta.get('encodeUserTag'),
                        'codecType': video_meta.get('codecType'),
                        'definition': video_meta.get('definition')
                    },
                    
                    # Effect stickers
                    'effectStickers': effect_stickers,
                    
                    # Legacy fields for backward compatibility
                    'music': music_meta,
                    'video': video_meta
                }
            return None

        elif data_type == 'user':
            if 'UserModule' in json_data:
                user_data = json_data['UserModule'].get('users', {})
                if user_data:
                    user = next(iter(user_data.values()))
                    stats = user.get('stats', {})
                    
                    return {
                        # Basic identifiers
                        'id': user.get('id'),
                        'uniqueId': user.get('uniqueId'),
                        'secUid': user.get('secUid'),
                        
                        # Profile information
                        'nickname': user.get('nickname'),
                        'signature': user.get('signature'),
                        'avatar': user.get('avatarLarger'),
                        'avatarThumb': user.get('avatarThumb'),
                        'avatarMedium': user.get('avatarMedium'),
                        
                        # Account status
                        'verified': user.get('verified', False),
                        'privateAccount': user.get('privateAccount', False),
                        'isUnderAge18': user.get('isUnderAge18', False),
                        'secret': user.get('secret', False),
                        
                        # Engagement metrics
                        'followingCount': stats.get('followingCount', 0),
                        'followerCount': stats.get('followerCount', 0),
                        'heartCount': stats.get('heartCount', 0),
                        'videoCount': stats.get('videoCount', 0),
                        'diggCount': stats.get('diggCount', 0),
                        
                        # Legacy stats format for backward compatibility
                        'stats': stats,
                        
                        # Additional metadata
                        'authorMeta': {
                            'id': user.get('id'),
                            'uniqueId': user.get('uniqueId'),
                            'nickname': user.get('nickname'),
                            'signature': user.get('signature'),
                            'avatar': user.get('avatarLarger'),
                            'verified': user.get('verified', False),
                            'secUid': user.get('secUid'),
                            'privateAccount': user.get('privateAccount', False)
                        }
                    }
            return None

        elif data_type == 'search':
            if 'SearchModule' in json_data:
                search_data = json_data['SearchModule'].get('searchData', [])
                results = []
                for item in search_data:
                    if item.get('type') == 1:  # Video results
                        author_meta = item.get('author', {})
                        stats = item.get('stats', {})
                        music_meta = item.get('music', {})
                        video_meta = item.get('video', {})
                        
                        # Parse create time
                        create_time = item.get('createTime')
                        create_time_iso = None
                        if create_time:
                            try:
                                create_time_iso = datetime.fromtimestamp(create_time).isoformat()
                            except (ValueError, TypeError):
                                create_time_iso = None
                        
                        # Extract hashtags and mentions
                        desc = item.get('desc', '')
                        hashtags = [tag.strip('#') for tag in re.findall(r'#\w+', desc)]
                        mentions = [mention.strip('@') for mention in re.findall(r'@\w+', desc)]
                        
                        # Extract media URLs
                        media_urls = []
                        if video_meta.get('playAddr'):
                            media_urls.append(video_meta.get('playAddr'))
                        if video_meta.get('downloadAddr'):
                            media_urls.append(video_meta.get('downloadAddr'))
                        
                        results.append({
                            # Basic identifiers
                            'id': item.get('id'),
                            'input': item.get('webVideoUrl', ''),
                            
                            # Content
                            'text': desc,
                            'desc': desc,
                            'textLanguage': item.get('textLanguage', 'N/A'),
                            
                            # Author metadata
                            'author': author_meta.get('uniqueId', 'N/A'),
                            'authorMeta': {
                                'id': author_meta.get('id'),
                                'uniqueId': author_meta.get('uniqueId'),
                                'nickname': author_meta.get('nickname'),
                                'signature': author_meta.get('signature'),
                                'avatar': author_meta.get('avatarLarger'),
                                'verified': author_meta.get('verified', False),
                                'secUid': author_meta.get('secUid'),
                                'privateAccount': author_meta.get('privateAccount', False)
                            },
                            
                            # Engagement metrics
                            'collectCount': stats.get('collectCount', 0),
                            'commentCount': stats.get('commentCount', 0),
                            'diggCount': stats.get('diggCount', 0),
                            'playCount': stats.get('playCount', 0),
                            'shareCount': stats.get('shareCount', 0),
                            
                            # Legacy stats format for backward compatibility
                            'stats': stats,
                            
                            # Time information
                            'createTime': create_time,
                            'createTimeISO': create_time_iso,
                            
                            # Content classification
                            'isAd': item.get('isAd', False),
                            'isPinned': item.get('isPinned', False),
                            'isSlideshow': item.get('isSlideshow', False),
                            'isSponsored': item.get('isSponsored', False),
                            
                            # Social elements
                            'hashtags': hashtags,
                            'mentions': mentions,
                            'searchHashtag': hashtags[0] if hashtags else None,
                            
                            # Media information
                            'mediaUrls': media_urls,
                            'webVideoUrl': item.get('webVideoUrl'),
                            
                            # Music metadata
                            'musicMeta': {
                                'musicId': music_meta.get('id'),
                                'musicName': music_meta.get('title'),
                                'musicAuthor': music_meta.get('authorName'),
                                'musicOriginal': music_meta.get('original'),
                                'playUrl': music_meta.get('playUrl'),
                                'duration': music_meta.get('duration')
                            },
                            
                            # Video metadata
                            'videoMeta': {
                                'height': video_meta.get('height'),
                                'width': video_meta.get('width'),
                                'duration': video_meta.get('duration'),
                                'cover': video_meta.get('cover'),
                                'playAddr': video_meta.get('playAddr'),
                                'downloadAddr': video_meta.get('downloadAddr')
                            },
                            
                            # Legacy fields for backward compatibility
                            'music': music_meta,
                            'video': video_meta
                        })
                return results
            return []

        return None

    except Exception as e:
        logger.error(f"Error processing JSON data: {str(e)}")
        return None

def parse_html_data(html_content: str, data_type: str) -> Optional[Union[Dict, List[Dict]]]:
    """Parse TikTok data from HTML content when JSON extraction fails."""
    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        if data_type == 'video':
            video_container = soup.find('div', {'data-e2e': 'browse-video'})
            if not video_container:
                return None

            return {
                'desc': _get_text(video_container.find('div', {'data-e2e': 'browse-video-desc'})),
                'author': _get_text(video_container.find('a', {'data-e2e': 'browse-video-author'})),
                'stats': {
                    'likes': _parse_count(video_container.find('strong', {'data-e2e': 'like-count'})),
                    'comments': _parse_count(video_container.find('strong', {'data-e2e': 'comment-count'})),
                    'shares': _parse_count(video_container.find('strong', {'data-e2e': 'share-count'}))
                }
            }

        elif data_type == 'user':
            profile_container = soup.find('div', {'data-e2e': 'user-profile'})
            if not profile_container:
                return None

            return {
                'nickname': _get_text(profile_container.find('h1', {'data-e2e': 'user-nickname'})),
                'uniqueId': _get_text(profile_container.find('h1', {'data-e2e': 'user-uniqueid'})),
                'signature': _get_text(profile_container.find('h2', {'data-e2e': 'user-bio'})),
                'stats': {
                    'following': _parse_count(profile_container.find('strong', {'data-e2e': 'following-count'})),
                    'followers': _parse_count(profile_container.find('strong', {'data-e2e': 'followers-count'})),
                    'likes': _parse_count(profile_container.find('strong', {'data-e2e': 'likes-count'}))
                }
            }

        elif data_type == 'search':
            results = []
            # Find search results by looking for elements with search-card data-e2e attributes
            desc_elements = soup.find_all(attrs={'data-e2e': 'search-card-desc'})
            
            for desc_elem in desc_elements:
                # Find the parent container that holds all the search card info
                card = desc_elem.find_parent() or desc_elem
                while card and not any(attr.startswith('search-card') for attr in card.get('data-e2e', '').split()):
                    card = card.find_parent()
                    if not card:
                        card = desc_elem.find_parent()
                        break
                
                result = {
                    'id': f"search_{len(results)}",  # Generate a simple ID
                    'desc': _get_text(desc_elem),
                    'author': _get_text(card.find(attrs={'data-e2e': 'search-card-user-link'})) if card else None,
                    'caption': _get_text(card.find(attrs={'data-e2e': 'search-card-video-caption'})) if card else None,
                    'stats': {
                        'likes': _parse_count(card.find(attrs={'data-e2e': 'search-card-like-count'})) if card else None,
                        'comments': _parse_count(card.find(attrs={'data-e2e': 'search-card-comment-count'})) if card else None
                    }
                }
                results.append(result)
            
            return results

        return None

    except Exception as e:
        logger.error(f"Error parsing HTML data: {str(e)}")
        return None

def _get_text(element) -> Optional[str]:
    """Safely extract text from a BeautifulSoup element."""
    return element.get_text(strip=True) if element else None

def _parse_count(element) -> Optional[int]:
    """Parse count values from TikTok's formatted strings."""
    if not element:
        return None

    text = element.get_text(strip=True).lower()
    
    try:
        # Handle counts with K, M, B suffixes
        if 'k' in text:
            return int(float(text.replace('k', '')) * 1000)
        elif 'm' in text:
            return int(float(text.replace('m', '')) * 1000000)
        elif 'b' in text:
            return int(float(text.replace('b', '')) * 1000000000)
        return int(text.replace(',', ''))
    except (ValueError, AttributeError):
        return None

def parse_tiktok_data(html_content: str, data_type: str) -> Optional[Union[Dict, List[Dict]]]:
    """Main function to parse TikTok data from a page."""
    try:
        # First try to extract and process JSON data
        json_data = extract_json_data(html_content)
        if json_data:
            processed_data = process_json_data(json_data, data_type)
            if processed_data:
                return processed_data

        # Fall back to HTML parsing if JSON extraction fails
        return parse_html_data(html_content, data_type)

    except Exception as e:
        logger.error(f"Error parsing TikTok data: {str(e)}")
        return None