from django.test import TestCase
from unittest.mock import Mock, patch, MagicMock
from .models import TikTokTask, ScrapedData
from .tasks import process_tiktok_data
from .utils.tiktok_crawler import TikTokCrawler
from .utils.driver_setup import WebDriverPool
from .utils.data_processor import parse_tiktok_data
from .utils.anti_detection import get_random_user_agent, random_delay

class ProcessTikTokDataTestCase(TestCase):
    def test_process_tiktok_data_video(self):
        task = TikTokTask.objects.create(job_name="test_job", task_type="video", identifier="123", status="PENDING")
        sample_data = {"id": "abc123", "desc": "Test video"}
        process_tiktok_data(task, sample_data, "video")
        saved = ScrapedData.objects.filter(task=task, data_type="VIDEO").first()
        self.assertIsNotNone(saved)
        self.assertEqual(saved.content, sample_data)

    def test_process_tiktok_data_user(self):
        task = TikTokTask.objects.create(job_name="test_job_user", task_type="user", identifier="user123", status="PENDING")
        sample_data = {"id": "user123", "nickname": "tester"}
        process_tiktok_data(task, sample_data, "user")
        saved = ScrapedData.objects.filter(task=task, data_type="USER").first()
        self.assertIsNotNone(saved)
        self.assertEqual(saved.content, sample_data)

    def test_process_tiktok_data_search(self):
        task = TikTokTask.objects.create(job_name="test_job_search", task_type="search", identifier="searchterm", status="PENDING")
        sample_data = [
            {"id": "vid1", "desc": "Result 1"},
            {"id": "vid2", "desc": "Result 2"}
        ]
        process_tiktok_data(task, sample_data, "search")
        saved = ScrapedData.objects.filter(task=task, data_type="SEARCH_RESULT")
        self.assertEqual(saved.count(), 2)
        self.assertTrue(any(item.content == sample_data[0] for item in saved))
        self.assertTrue(any(item.content == sample_data[1] for item in saved))

class TikTokCrawlerTestCase(TestCase):
    def setUp(self):
        self.mock_driver = Mock()
        self.mock_driver.page_source = '<html><body>Test content</body></html>'
        self.crawler = TikTokCrawler(self.mock_driver)

    def test_crawler_initialization(self):
        """Test that TikTokCrawler initializes correctly."""
        self.assertEqual(self.crawler.driver, self.mock_driver)
        self.assertEqual(self.crawler.timeout, 20)
        self.assertIsNotNone(self.crawler.wait)

    @patch('crawler_tiktok.utils.tiktok_crawler.parse_tiktok_data')
    @patch('crawler_tiktok.utils.tiktok_crawler.simulate_human_interaction')
    def test_scrape_video_success(self, mock_simulate, mock_parse):
        """Test successful video scraping."""
        mock_parse.return_value = {"id": "test123", "desc": "Test video"}
        self.mock_driver.find_elements.return_value = []
        
        result = self.crawler.scrape_video("https://www.tiktok.com/@user/video/123")
        
        self.mock_driver.get.assert_called_once()
        mock_simulate.assert_called_once()
        mock_parse.assert_called_once()
        self.assertIsNotNone(result)

    @patch('crawler_tiktok.utils.tiktok_crawler.parse_tiktok_data')
    @patch('crawler_tiktok.utils.tiktok_crawler.simulate_human_interaction')
    def test_scrape_user_success(self, mock_simulate, mock_parse):
        """Test successful user profile scraping."""
        mock_parse.return_value = {"id": "user123", "nickname": "testuser"}
        self.mock_driver.find_elements.return_value = []
        
        result = self.crawler.scrape_user("testuser")
        
        self.mock_driver.get.assert_called_once_with("https://www.tiktok.com/@testuser")
        mock_simulate.assert_called_once()
        mock_parse.assert_called_once()
        self.assertIsNotNone(result)

class WebDriverPoolTestCase(TestCase):
    def setUp(self):
        self.pool = WebDriverPool(max_drivers=2)

    def test_pool_initialization(self):
        """Test that WebDriverPool initializes correctly."""
        self.assertEqual(self.pool.max_drivers, 2)
        self.assertEqual(len(self.pool.available_drivers), 0)
        self.assertEqual(len(self.pool.in_use_drivers), 0)

    @patch('crawler_tiktok.utils.driver_setup.webdriver.Chrome')
    @patch('crawler_tiktok.utils.driver_setup.ChromeDriverManager')
    def test_get_driver(self, mock_manager, mock_chrome):
        """Test getting a driver from the pool."""
        mock_driver = Mock()
        mock_chrome.return_value = mock_driver
        mock_manager.return_value.install.return_value = '/path/to/chromedriver'
        
        driver_id, driver = self.pool.get_driver()
        
        self.assertIsNotNone(driver_id)
        self.assertEqual(driver, mock_driver)
        self.assertIn(driver_id, self.pool.in_use_drivers)

    def test_return_driver(self):
        """Test returning a driver to the pool."""
        mock_driver = Mock()
        mock_driver.current_url = "https://example.com"
        
        # Manually add driver to in_use_drivers
        driver_id = 1
        self.pool.in_use_drivers[driver_id] = mock_driver
        
        self.pool.return_driver(driver_id)
        
        self.assertNotIn(driver_id, self.pool.in_use_drivers)
        self.assertIn(mock_driver, self.pool.available_drivers)

class AntiDetectionTestCase(TestCase):
    def test_get_random_user_agent(self):
        """Test that random user agent generation works."""
        user_agent = get_random_user_agent()
        self.assertIsInstance(user_agent, str)
        self.assertGreater(len(user_agent), 0)
        self.assertIn('Mozilla', user_agent)

    @patch('time.sleep')
    def test_random_delay(self, mock_sleep):
        """Test that random delay function works."""
        random_delay(1, 2)
        mock_sleep.assert_called_once()
        
        # Check that the delay is within the expected range
        call_args = mock_sleep.call_args[0][0]
        self.assertGreaterEqual(call_args, 1)
        self.assertLessEqual(call_args, 2)

class DataProcessorTestCase(TestCase):
    def test_parse_tiktok_data_with_empty_html(self):
        """Test data parsing with empty HTML."""
        result = parse_tiktok_data("", "video")
        self.assertIsNone(result)

    def test_parse_tiktok_data_with_invalid_type(self):
        """Test data parsing with invalid data type."""
        html = "<html><body>Test</body></html>"
        result = parse_tiktok_data(html, "invalid_type")
        self.assertIsNone(result)
