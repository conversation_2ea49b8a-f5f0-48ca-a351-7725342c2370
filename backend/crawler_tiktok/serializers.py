from rest_framework import serializers
from .models import TikTokTask, ScrapedData

class TikTokTaskSerializer(serializers.ModelSerializer):
    start_urls = serializers.ListField(child=serializers.CharField(), write_only=True, required=True, help_text="List of TikTok URLs (video, user) or search keywords to scrape")
    scrape_type = serializers.ChoiceField(choices=['VIDEO', 'USER', 'SEARCH'], write_only=True, required=True, help_text="Type of scraping: VIDEO, USER, or SEARCH")
    job_name = serializers.CharField(required=False, help_text="Optional: A name for this scraping job")
    proxy_config = serializers.JSONField(required=False, allow_null=True, help_text="Optional: JSON configuration for proxy settings")
    max_items = serializers.IntegerField(required=False, allow_null=True, help_text="Optional: Maximum number of items to scrape")
    start_date = serializers.DateField(required=False, allow_null=True, help_text="Optional: Start date for data collection (YYYY-MM-DD)")
    end_date = serializers.DateField(required=False, allow_null=True, help_text="Optional: End date for data collection (YYYY-MM-DD)")

    class Meta:
        model = TikTokTask
        fields = [
            'id', 'job_name', 'task_type', 'identifier', 'status', 'celery_task_id',
            'created_at', 'updated_at', 'completed_at', 'error_message',
            'start_urls', 'scrape_type', 'proxy_config', 'max_items', 'start_date', 'end_date'
        ]
        read_only_fields = [
            'id', 'task_type', 'identifier', 'status', 'celery_task_id',
            'created_at', 'updated_at', 'completed_at', 'error_message'
        ]

    def validate(self, data):
        start_urls = data.get('start_urls')
        scrape_type = data.get('scrape_type')

        if not start_urls:
            raise serializers.ValidationError({"start_urls": "This field is required."})
        if not scrape_type:
            raise serializers.ValidationError({"scrape_type": "This field is required."})

        # Validate URLs based on scrape type
        first_url = start_urls[0].strip() if start_urls else ''
        
        if scrape_type in ['VIDEO', 'USER']:
            # For VIDEO and USER types, validate that it's a proper TikTok URL
            if not (first_url.startswith('http://') or first_url.startswith('https://')):
                raise serializers.ValidationError({"start_urls": "Please provide a valid TikTok URL for video or user scraping."})
            
            if 'tiktok.com' not in first_url:
                raise serializers.ValidationError({"start_urls": "Please provide a valid TikTok URL."})
            
            if scrape_type == 'VIDEO' and not (first_url.find('/video/') > -1 or (first_url.find('/@') > -1 and first_url.find('/video/') > -1)):
                raise serializers.ValidationError({"start_urls": "Please provide a valid TikTok video URL."})
            
            if scrape_type == 'USER' and not (first_url.find('/@') > -1 and first_url.find('/video/') == -1):
                raise serializers.ValidationError({"start_urls": "Please provide a valid TikTok user profile URL."})
        
        # For SEARCH type, any non-empty string is valid (keywords)
        elif scrape_type == 'SEARCH':
            if not first_url:
                raise serializers.ValidationError({"start_urls": "Please provide search keywords."})

        # For simplicity, we'll take the first URL/identifier from the list
        # In a real-world scenario, you might want to handle multiple URLs differently
        data['identifier'] = start_urls[0]
        data['task_type'] = scrape_type

        return data

class ScrapedDataSerializer(serializers.ModelSerializer):
    task_id = serializers.PrimaryKeyRelatedField(source='task', read_only=True)

    class Meta:
        model = ScrapedData
        fields = ['id', 'task_id', 'data_type', 'content', 'scraped_at']
        read_only_fields = ['id', 'task_id', 'data_type', 'content', 'scraped_at']