from __future__ import absolute_import, unicode_literals
from celery import Celery
from celery.schedules import crontab
from django.conf import settings
import os

# Set the default Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

app = Celery('crawler_tiktok')

# Load task modules from all registered Django app configs
app.config_from_object('django.conf:settings', namespace='CELERY')

# Configure Celery Beat schedule
app.conf.beat_schedule = {
    'cleanup-old-tasks': {
        'task': 'crawler_tiktok.tasks.cleanup_old_tasks',
        'schedule': crontab(hour=0, minute=0),  # Run daily at midnight
    },
}

# Auto-discover tasks in all installed apps
app.autodiscover_tasks(lambda: settings.INSTALLED_APPS)

# Configure Celery
app.conf.update(
    worker_max_tasks_per_child=1000,  # Restart worker after 1000 tasks
    worker_prefetch_multiplier=1,      # Don't prefetch more than one task
    task_acks_late=True,               # Task acknowledgment after execution
    task_reject_on_worker_lost=True,   # Reject task if worker disconnects
    task_default_rate_limit='10/m',    # Default rate limit of 10 tasks per minute
    broker_connection_retry=True,       # Retry broker connection on failure
    broker_connection_max_retries=None, # Keep retrying broker connection
    result_expires=86400,              # Results expire after 24 hours
)