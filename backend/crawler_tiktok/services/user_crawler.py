from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from ..utils.anti_detection import get_random_user_agent, random_delay, simulate_human_interaction
from ..utils.config import LOAD_TIMEOUT, SCROLL_PAUSE_TIME
from .driver_setup import create_driver, return_driver
from .data_processor import parse_user_profile, parse_user_videos
import time

def _apply_session_data(driver, session_data):
    """Apply session data (cookies, tokens) to the WebDriver."""
    try:
        # Navigate to TikTok first to set domain for cookies
        driver.get("https://www.tiktok.com")
        
        # Apply cookies if available
        cookies = session_data.get('cookies', [])
        for cookie in cookies:
            try:
                driver.add_cookie(cookie)
            except Exception as e:
                print(f"Failed to add cookie: {e}")
        
        # Apply tokens via localStorage if available
        tokens = session_data.get('tokens', {})
        if tokens:
            for key, value in tokens.items():
                try:
                    driver.execute_script(f"localStorage.setItem('{key}', '{value}');")
                except Exception as e:
                    print(f"Failed to set localStorage item {key}: {e}")
        
        # Refresh page to apply session data
        driver.refresh()
        time.sleep(2)
        
    except Exception as e:
        print(f"Error applying session data: {e}")

def scrape_tiktok_user(username, proxy=None, max_videos=30, session_data=None):
    driver = None
    try:
        user_agent = get_random_user_agent()
        driver = create_driver(
            headless=True,
            proxy=proxy,
            user_agent=user_agent
        )
        
        # Apply session data if available (cookies, tokens, etc.)
        if session_data and session_data.get('authenticated'):
            _apply_session_data(driver, session_data)
        
        # Navigate to user profile
        user_url = f"https://www.tiktok.com/@{username}"
        driver.get(user_url)
        
        # Wait for content to load
        try:
            WebDriverWait(driver, LOAD_TIMEOUT).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div.tiktok-1g04lal-DivShareLayout"))
            )
        except TimeoutException:
            raise Exception("User profile failed to load within timeout")
        
        # Simulate human behavior
        simulate_human_interaction(driver)
        
        # Parse profile data
        page_source = driver.page_source
        profile_data = parse_user_profile(page_source)
        
        # Scroll to load videos
        videos = []
        last_height = driver.execute_script("return document.body.scrollHeight")
        
        while len(videos) < max_videos:
            # Scroll down
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(SCROLL_PAUSE_TIME)
            
            # Parse current videos
            page_source = driver.page_source
            current_videos = parse_user_videos(page_source)
            videos.extend(current_videos)
            
            # Deduplicate videos
            seen = set()
            videos = [v for v in videos if v['id'] not in seen and not seen.add(v['id'])]
            
            # Check if we've reached the end
            new_height = driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                break
            last_height = new_height
            
            # Random delay between scrolls
            random_delay(1, 3)
            
            # Break if we have enough videos
            if len(videos) >= max_videos:
                videos = videos[:max_videos]
                break
        
        return {
            'profile': profile_data,
            'videos': videos
        }
    
    except Exception as e:
        raise Exception(f"User scraping failed: {str(e)}")
    finally:
        if driver:
            driver.quit()