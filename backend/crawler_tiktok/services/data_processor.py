from bs4 import BeautifulSoup
import json
import re
from datetime import datetime
import logging
from ..exceptions import TikTokContentChangedException

logger = logging.getLogger(__name__)

def extract_json_data(html_content):
    """Extract embedded JSON data from TikTok's page source."""
    try:
        # Try to find JSON-LD data first
        soup = BeautifulSoup(html_content, 'html.parser')
        json_ld = soup.find('script', type='application/ld+json')
        if json_ld:
            return json.loads(json_ld.string)

        # Look for SIGI_STATE data
        sigi_state_pattern = re.compile(r'window\[\'SIGI_STATE\'\]\s*=\s*(\{.*?\});', re.DOTALL)
        sigi_match = sigi_state_pattern.search(html_content)
        if sigi_match:
            return json.loads(sigi_match.group(1))

        # Look for __UNIVERSAL_DATA_FOR_REHYDRATION__ data
        universal_pattern = re.compile(r'window\[\'__UNIVERSAL_DATA_FOR_REHYDRATION__\'\]\s*=\s*(\{.*?\});', re.DOTALL)
        universal_match = universal_pattern.search(html_content)
        if universal_match:
            return json.loads(universal_match.group(1))

    except Exception as e:
        logger.warning(f"Failed to extract JSON data: {str(e)}")
        return None

def parse_tiktok_data(html_content):
    """Parse TikTok video data from HTML content."""
    try:
        # Try to extract embedded JSON data first
        json_data = extract_json_data(html_content)
        if json_data:
            return process_json_data(json_data)

        # Fallback to HTML parsing
        return parse_html_data(html_content)

    except Exception as e:
        logger.error(f"Error parsing TikTok data: {str(e)}")
        raise TikTokContentChangedException(f"Failed to parse video data: {str(e)}")

def process_json_data(json_data):
    """Process extracted JSON data into a standardized format."""
    processed_data = {
        'video': {},
        'engagement': {},
        'user': {},
        'hashtags': [],
        'music': {}
    }

    try:
        # Extract video information
        if 'video' in json_data:
            video_info = json_data['video']
            processed_data['video'].update({
                'url': video_info.get('url'),
                'description': video_info.get('description'),
                'duration': video_info.get('duration'),
                'created_at': video_info.get('uploadDate'),
                'video_id': video_info.get('id')
            })

        # Extract engagement metrics
        if 'stats' in json_data:
            stats = json_data['stats']
            processed_data['engagement'].update({
                'likes': int(stats.get('diggCount', 0)),
                'comments': int(stats.get('commentCount', 0)),
                'shares': int(stats.get('shareCount', 0)),
                'views': int(stats.get('playCount', 0))
            })

        # Extract user information
        if 'author' in json_data:
            author = json_data['author']
            processed_data['user'].update({
                'username': author.get('uniqueId'),
                'nickname': author.get('nickname'),
                'avatar_url': author.get('avatarLarger'),
                'signature': author.get('signature')
            })

        # Extract hashtags
        if 'challenges' in json_data:
            processed_data['hashtags'] = [
                tag.get('title', '').strip('#')
                for tag in json_data['challenges']
                if tag.get('title')
            ]

        # Extract music information
        if 'music' in json_data:
            music = json_data['music']
            processed_data['music'].update({
                'title': music.get('title'),
                'author': music.get('authorName'),
                'duration': music.get('duration'),
                'url': music.get('playUrl')
            })

        return processed_data

    except Exception as e:
        logger.error(f"Error processing JSON data: {str(e)}")
        return None

def parse_html_data(html_content):
    """Parse TikTok data from HTML when JSON extraction fails."""
    soup = BeautifulSoup(html_content, 'html.parser')
    processed_data = {
        'video': {},
        'engagement': {},
        'user': {},
        'hashtags': [],
        'music': {}
    }

    try:
        # Extract video information from meta tags
        meta_tags = {
            'og:url': 'url',
            'og:description': 'description',
            'og:video:duration': 'duration',
            'og:title': 'title'
        }

        for meta_name, data_key in meta_tags.items():
            meta_tag = soup.find('meta', property=meta_name)
            if meta_tag:
                processed_data['video'][data_key] = meta_tag.get('content')

        # Extract engagement metrics
        engagement_selectors = {
            'likes': '[data-e2e="like-count"]',
            'comments': '[data-e2e="comment-count"]',
            'shares': '[data-e2e="share-count"]'
        }

        for metric, selector in engagement_selectors.items():
            element = soup.select_one(selector)
            if element:
                processed_data['engagement'][metric] = parse_count(element.text)

        # Extract user information
        user_link = soup.select_one('a[data-e2e="video-author-uniqueid"]')
        if user_link:
            processed_data['user'].update({
                'username': user_link.text.strip('@'),
                'profile_url': user_link.get('href')
            })

        # Extract hashtags
        hashtag_links = soup.select('a[href*="/tag/"]')
        processed_data['hashtags'] = [
            link.text.strip('#')
            for link in hashtag_links
            if link.text.startswith('#')
        ]

        # Extract music information
        music_link = soup.select_one('a[data-e2e="video-music"]')
        if music_link:
            processed_data['music'].update({
                'title': music_link.text.strip(),
                'url': music_link.get('href')
            })

        return processed_data

    except Exception as e:
        logger.error(f"Error parsing HTML data: {str(e)}")
        return None

def parse_count(count_str):
    """Parse string representations of counts (e.g., '1.5M', '300K') into integers."""
    try:
        count_str = count_str.strip().upper()
        multipliers = {'K': 1000, 'M': 1000000, 'B': 1000000000}
        
        if not count_str:
            return 0
            
        if count_str[-1] in multipliers:
            number = float(count_str[:-1].replace(',', ''))
            return int(number * multipliers[count_str[-1]])
            
        return int(count_str.replace(',', ''))
    except (ValueError, AttributeError):
        return 0

def parse_user_profile(html_content):
    """Parse user profile data from TikTok user page HTML."""
    try:
        json_data = extract_json_data(html_content)
        if json_data and 'UserModule' in json_data:
            user_data = json_data['UserModule']['users']
            if user_data:
                user_id = list(user_data.keys())[0]
                user_info = user_data[user_id]
                
                return {
                    'id': user_info.get('id'),
                    'unique_id': user_info.get('uniqueId'),
                    'nickname': user_info.get('nickname'),
                    'avatar_url': user_info.get('avatarLarger'),
                    'signature': user_info.get('signature'),
                    'verified': user_info.get('verified', False),
                    'stats': {
                        'follower_count': user_info.get('stats', {}).get('followerCount', 0),
                        'following_count': user_info.get('stats', {}).get('followingCount', 0),
                        'heart_count': user_info.get('stats', {}).get('heartCount', 0),
                        'video_count': user_info.get('stats', {}).get('videoCount', 0)
                    }
                }
        
        # Fallback to HTML parsing
        soup = BeautifulSoup(html_content, 'html.parser')
        return {
            'unique_id': 'unknown',
            'nickname': 'Unknown User',
            'avatar_url': '',
            'signature': '',
            'verified': False,
            'stats': {
                'follower_count': 0,
                'following_count': 0,
                'heart_count': 0,
                'video_count': 0
            }
        }
    except Exception as e:
        logger.error(f"Error parsing user profile: {str(e)}")
        return None

def parse_user_videos(html_content):
    """Parse user videos from TikTok user page HTML."""
    try:
        json_data = extract_json_data(html_content)
        videos = []
        
        if json_data and 'ItemModule' in json_data:
            item_data = json_data['ItemModule']
            for video_id, video_info in item_data.items():
                if isinstance(video_info, dict) and 'id' in video_info:
                    video = {
                        'id': video_info.get('id'),
                        'desc': video_info.get('desc', ''),
                        'create_time': video_info.get('createTime'),
                        'video': {
                            'duration': video_info.get('video', {}).get('duration', 0),
                            'play_addr': video_info.get('video', {}).get('playAddr', ''),
                            'cover': video_info.get('video', {}).get('cover', '')
                        },
                        'author': {
                            'unique_id': video_info.get('author', {}).get('uniqueId', ''),
                            'nickname': video_info.get('author', {}).get('nickname', '')
                        },
                        'statistics': {
                            'play_count': video_info.get('stats', {}).get('playCount', 0),
                            'digg_count': video_info.get('stats', {}).get('diggCount', 0),
                            'comment_count': video_info.get('stats', {}).get('commentCount', 0),
                            'share_count': video_info.get('stats', {}).get('shareCount', 0)
                        }
                    }
                    videos.append(video)
        
        return videos
    except Exception as e:
        logger.error(f"Error parsing user videos: {str(e)}")
        return []

def parse_hashtag_videos(html_content):
    """Parse hashtag videos from TikTok hashtag page HTML."""
    try:
        json_data = extract_json_data(html_content)
        videos = []
        
        if json_data and 'ItemModule' in json_data:
            item_data = json_data['ItemModule']
            for video_id, video_info in item_data.items():
                if isinstance(video_info, dict) and 'id' in video_info:
                    video = {
                        'id': video_info.get('id'),
                        'desc': video_info.get('desc', ''),
                        'create_time': video_info.get('createTime'),
                        'video': {
                            'duration': video_info.get('video', {}).get('duration', 0),
                            'play_addr': video_info.get('video', {}).get('playAddr', ''),
                            'cover': video_info.get('video', {}).get('cover', '')
                        },
                        'author': {
                            'unique_id': video_info.get('author', {}).get('uniqueId', ''),
                            'nickname': video_info.get('author', {}).get('nickname', '')
                        },
                        'statistics': {
                            'play_count': video_info.get('stats', {}).get('playCount', 0),
                            'digg_count': video_info.get('stats', {}).get('diggCount', 0),
                            'comment_count': video_info.get('stats', {}).get('commentCount', 0),
                            'share_count': video_info.get('stats', {}).get('shareCount', 0)
                        }
                    }
                    videos.append(video)
        
        return videos
    except Exception as e:
        logger.error(f"Error parsing hashtag videos: {str(e)}")
        return []

def parse_search_videos(html_content):
    """Parse search videos from TikTok search results HTML."""
    try:
        json_data = extract_json_data(html_content)
        videos = []
        
        if json_data and 'ItemModule' in json_data:
            item_data = json_data['ItemModule']
            for video_id, video_info in item_data.items():
                if isinstance(video_info, dict) and 'id' in video_info:
                    video = {
                        'id': video_info.get('id'),
                        'desc': video_info.get('desc', ''),
                        'create_time': video_info.get('createTime'),
                        'video': {
                            'duration': video_info.get('video', {}).get('duration', 0),
                            'play_addr': video_info.get('video', {}).get('playAddr', ''),
                            'cover': video_info.get('video', {}).get('cover', '')
                        },
                        'author': {
                            'unique_id': video_info.get('author', {}).get('uniqueId', ''),
                            'nickname': video_info.get('author', {}).get('nickname', '')
                        },
                        'statistics': {
                            'play_count': video_info.get('stats', {}).get('playCount', 0),
                            'digg_count': video_info.get('stats', {}).get('diggCount', 0),
                            'comment_count': video_info.get('stats', {}).get('commentCount', 0),
                            'share_count': video_info.get('stats', {}).get('shareCount', 0)
                        }
                    }
                    videos.append(video)
        
        return videos
    except Exception as e:
        logger.error(f"Error parsing search videos: {str(e)}")
        return []

def parse_search_results(html_content):
    """Parse search results from TikTok search HTML."""
    try:
        json_data = extract_json_data(html_content)
        if json_data:
            return process_json_data(json_data)
        
        # Fallback to HTML parsing
        return parse_html_data(html_content)
    except Exception as e:
        logger.error(f"Error parsing search results: {str(e)}")
        return []