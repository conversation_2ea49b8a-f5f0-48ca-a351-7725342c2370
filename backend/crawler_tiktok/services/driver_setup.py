from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from queue import Queue
from threading import Lock
import atexit
import platform
import os

class WebDriverPool:
    _instance = None
    _lock = Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(WebDriverPool, cls).__new__(cls)
                cls._instance.pool = Queue()
                cls._instance.max_drivers = 5
                atexit.register(cls._instance.cleanup)
            return cls._instance
    
    def get_driver(self, headless=True, proxy=None, user_agent=None):
        if not self.pool.empty():
            driver = self.pool.get()
            if self._is_driver_healthy(driver):
                return driver
            else:
                self._quit_driver(driver)
        
        return self._create_driver(headless, proxy, user_agent)
    
    def return_driver(self, driver):
        if self.pool.qsize() < self.max_drivers and self._is_driver_healthy(driver):
            self.pool.put(driver)
        else:
            self._quit_driver(driver)
    
    def _create_driver(self, headless=True, proxy=None, user_agent=None):
        chrome_options = Options()
        if headless:
            chrome_options.add_argument('--headless=new')
        
        # Basic Chrome options
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-infobars')
        chrome_options.add_argument('--disable-notifications')
        chrome_options.add_argument('--disable-popup-blocking')
        
        # Anti-detection options
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        if user_agent:
            chrome_options.add_argument(f'user-agent={user_agent}')
        
        if proxy:
            chrome_options.add_argument(f'--proxy-server={proxy}')

        # Set Chrome binary location for macOS
        if platform.system() == 'Darwin':
            chrome_path = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
            if os.path.exists(chrome_path):
                chrome_options.binary_location = chrome_path

        # Create ChromeDriver service with automatic driver management
        service = Service(ChromeDriverManager().install())
        
        # Create and return the WebDriver instance
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Execute CDP commands to prevent detection
        driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
            'source': '''
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
            '''
        })
        
        return driver
    
    def _is_driver_healthy(self, driver):
        try:
            driver.current_url
            return True
        except:
            return False
    
    def _quit_driver(self, driver):
        try:
            driver.quit()
        except:
            pass
    
    def cleanup(self):
        while not self.pool.empty():
            driver = self.pool.get()
            self._quit_driver(driver)

def create_driver(headless=True, proxy=None, user_agent=None):
    driver_pool = WebDriverPool()
    return driver_pool.get_driver(headless, proxy, user_agent)

def return_driver(driver):
    if driver:
        WebDriverPool().return_driver(driver)