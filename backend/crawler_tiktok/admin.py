from django.contrib import admin
from .models import TikTokTask, ScrapedData

@admin.register(TikTokTask)
class TikTokTaskAdmin(admin.ModelAdmin):
    list_display = ('job_name', 'task_type', 'identifier', 'status', 'created_at', 'completed_at')
    list_filter = ('task_type', 'status')
    search_fields = ('job_name', 'identifier')
    readonly_fields = ('created_at', 'updated_at', 'completed_at', 'celery_task_id', 'error_message')

@admin.register(ScrapedData)
class ScrapedDataAdmin(admin.ModelAdmin):
    list_display = ('task', 'data_type', 'scraped_at')
    list_filter = ('data_type', 'task__task_type', 'task__job_name')
    search_fields = ('task__identifier', 'content')
    readonly_fields = ('scraped_at', 'content')
