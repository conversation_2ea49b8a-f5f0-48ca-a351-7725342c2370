import logging
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from .models import TikTokTask, ScrapedData
from .serializers import TikTokTaskSerializer, ScrapedDataSerializer
from .tasks import scrape_tiktok_video_task, scrape_tiktok_user_task, scrape_tiktok_search_task
from .filters import TikTokTaskFilter
from .monitoring import CrawlerMonitor

logger = logging.getLogger(__name__)

class TikTokTaskViewSet(viewsets.ModelViewSet):
    queryset = TikTokTask.objects.all().order_by('-created_at')
    serializer_class = TikTokTaskSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_class = TikTokTaskFilter
    permission_classes = [IsAuthenticated]
    
    def update(self, request, *args, **kwargs):
        """Update a task (partial updates allowed)."""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        
        # Prevent updating certain fields if task is running
        if instance.status in ['RUNNING', 'PENDING']:
            restricted_fields = ['task_type', 'identifier', 'start_date', 'end_date']
            for field in restricted_fields:
                if field in request.data:
                    return Response(
                        {'error': f'Cannot update {field} while task is {instance.status.lower()}'}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
        
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        
        return Response(serializer.data)
    
    def destroy(self, request, *args, **kwargs):
        """Delete a task and its associated scraped data."""
        instance = self.get_object()
        
        # Prevent deletion of running tasks
        if instance.status in ['RUNNING', 'PENDING']:
            return Response(
                {'error': 'Cannot delete a running or pending task'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Delete associated scraped data first
        ScrapedData.objects.filter(task=instance).delete()
        
        # Delete the task
        self.perform_destroy(instance)
        return Response(status=status.HTTP_204_NO_CONTENT)

    def create(self, request, *args, **kwargs):
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            start_urls = serializer.validated_data.get('start_urls')
            scrape_type = serializer.validated_data.get('scrape_type')
            job_name = serializer.validated_data.get('job_name', f'{scrape_type.title()} Scrape')
            # proxy_config = serializer.validated_data.get('proxy_config')  # Removed - not supported
            max_items = serializer.validated_data.get('max_items')
            start_date = serializer.validated_data.get('start_date')
            end_date = serializer.validated_data.get('end_date')

            # For now, only create one task per request (first URL)
            identifier = start_urls[0]
            task = TikTokTask.objects.create(
                job_name=job_name,
                task_type=scrape_type,
                identifier=identifier,
                status='PENDING',
                start_date=start_date,
                end_date=end_date
            )

            # Dispatch the appropriate Celery task
            try:
                if scrape_type == 'VIDEO':
                    scrape_tiktok_video_task.delay(task.id, identifier, str(start_date) if start_date else None, str(end_date) if end_date else None, max_items)
                elif scrape_type == 'USER':
                    scrape_tiktok_user_task.delay(task.id, identifier, str(start_date) if start_date else None, str(end_date) if end_date else None, max_items)
                elif scrape_type == 'SEARCH':
                    scrape_tiktok_search_task.delay(task.id, identifier, str(start_date) if start_date else None, str(end_date) if end_date else None, max_items)
                else:
                    task.status = 'FAILED'
                    task.error_message = 'Invalid scrape_type'
                    task.save()
                    return Response({'error': 'Invalid scrape_type'}, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                logger.error(f"Failed to create Celery task: {str(e)}")
                task.status = 'FAILED'
                task.error_message = f"Failed to start task: {str(e)}"
                task.save()
                return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            return Response(TikTokTaskSerializer(task).data, status=status.HTTP_201_CREATED)
        except Exception as e:
            logger.error(f"Error in create: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get task statistics."""
        try:
            stats = CrawlerMonitor.get_task_statistics()
            
            # Format the response to match frontend expectations
            response_data = {
                'success_rate': stats.get('success_rate', 0.0),
                'average_duration': stats.get('avg_completion_time', 0.0),
                'total_tasks': stats.get('total_tasks', 0),
                'failed_tasks': stats.get('failed_tasks', 0)
            }
            
            return Response(response_data, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error getting task statistics: {str(e)}")
            return Response(
                {'error': f'Failed to get statistics: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def history(self, request):
        """Get task history."""
        try:
            # Get completed and failed tasks, ordered by most recent
            tasks = TikTokTask.objects.filter(
                status__in=['completed', 'failed']
            ).order_by('-updated_at')[:50]  # Limit to last 50 tasks
            
            serializer = self.get_serializer(tasks, many=True)
            return Response({'tasks': serializer.data}, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error getting task history: {str(e)}")
            return Response(
                {'error': f'Failed to get task history: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def retry(self, request, pk=None):
        """Retry a failed task."""
        task = self.get_object()
        
        if task.status not in ['FAILED', 'COMPLETED']:
            return Response(
                {'error': 'Can only retry failed or completed tasks'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Reset task status
            task.status = 'PENDING'
            task.error_message = None
            task.completed_at = None
            task.save()
            
            # Dispatch the appropriate Celery task
            if task.task_type == 'VIDEO':
                scrape_tiktok_video_task.delay(
                    task.id, task.identifier, 
                    str(task.start_date) if task.start_date else None, 
                    str(task.end_date) if task.end_date else None
                )
            elif task.task_type == 'USER':
                scrape_tiktok_user_task.delay(
                    task.id, task.identifier,
                    str(task.start_date) if task.start_date else None, 
                    str(task.end_date) if task.end_date else None
                )
            elif task.task_type == 'SEARCH':
                scrape_tiktok_search_task.delay(
                    task.id, task.identifier,
                    str(task.start_date) if task.start_date else None, 
                    str(task.end_date) if task.end_date else None
                )
            
            return Response({'message': 'Task retry initiated'}, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error retrying task: {str(e)}")
            return Response(
                {'error': f'Failed to retry task: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel a running or pending task."""
        task = self.get_object()
        
        if task.status not in ['PENDING', 'RUNNING']:
            return Response(
                {'error': 'Can only cancel pending or running tasks'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Update task status
            task.status = 'CANCELLED'
            task.error_message = 'Task cancelled by user'
            task.save()
            
            # TODO: Implement actual Celery task cancellation if needed
            # This would require storing the Celery task ID
            
            return Response({'message': 'Task cancelled'}, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error cancelling task: {str(e)}")
            return Response(
                {'error': f'Failed to cancel task: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['post'])
    def bulk_delete(self, request):
        """Delete multiple tasks at once."""
        task_ids = request.data.get('task_ids', [])
        
        if not task_ids:
            return Response(
                {'error': 'task_ids is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            tasks = TikTokTask.objects.filter(id__in=task_ids)
            
            # Check if any tasks are running
            running_tasks = tasks.filter(status__in=['RUNNING', 'PENDING'])
            if running_tasks.exists():
                return Response(
                    {'error': 'Cannot delete running or pending tasks'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Delete associated scraped data first
            ScrapedData.objects.filter(task__in=tasks).delete()
            
            # Delete the tasks
            deleted_count = tasks.delete()[0]
            
            return Response(
                {'message': f'Successfully deleted {deleted_count} tasks'}, 
                status=status.HTTP_200_OK
            )
        except Exception as e:
            logger.error(f"Error bulk deleting tasks: {str(e)}")
            return Response(
                {'error': f'Failed to delete tasks: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class ScrapedDataViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = ScrapedData.objects.all().order_by('-scraped_at')
    serializer_class = ScrapedDataSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['task', 'data_type']
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'])
    def by_task(self, request):
        """Get scraped data for a specific task with enhanced formatting."""
        task_id = request.query_params.get('task_id')
        if not task_id:
            return Response({'error': 'task_id parameter is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            task = TikTokTask.objects.get(id=task_id)
            scraped_data = ScrapedData.objects.filter(task=task).order_by('-scraped_at')
            
            # Format the response with enhanced metadata
            formatted_results = []
            for data in scraped_data:
                content = data.content
                formatted_item = {
                    'id': data.id,
                    'data_type': data.data_type,
                    'scraped_at': data.scraped_at,
                    'content': content,
                    # Extract key metadata for easy access
                    'metadata': {
                        'description': content.get('desc') or content.get('caption', 'N/A'),
                        'author': content.get('author', 'N/A'),
                        'likes': content.get('stats', {}).get('likes', 0) if content.get('stats') else 0,
                        'comments': content.get('stats', {}).get('comments', 0) if content.get('stats') else 0,
                        'shares': content.get('stats', {}).get('shares', 0) if content.get('stats') else 0,
                        'video_id': content.get('id', 'N/A'),
                        'create_time': content.get('createTime', 'N/A')
                    }
                }
                formatted_results.append(formatted_item)
            
            # Transform the data to match frontend expectations
            transformed_data = []
            for item in formatted_results:
                content = item['content']
                transformed_item = {
                    'id': content.get('id', item['id']),
                    'desc': content.get('desc', ''),
                    'author': content.get('author', ''),
                    'caption': content.get('caption', ''),
                    'stats': {
                        'likes': content.get('stats', {}).get('likes', 0),
                        'comments': content.get('stats', {}).get('comments', 0),
                        'shares': content.get('stats', {}).get('shares', 0),
                        'views': content.get('stats', {}).get('views', 0)
                    },
                    'video_id': content.get('id', ''),
                    'created_time': content.get('createTime', ''),
                    'duration': content.get('duration', 0),
                    'music': content.get('music', {})
                }
                transformed_data.append(transformed_item)
            
            return Response({
                'task': {
                    'id': task.id,
                    'job_name': task.job_name,
                    'task_type': task.task_type,
                    'identifier': task.identifier,
                    'status': task.status,
                    'created_at': task.created_at,
                    'completed_at': task.completed_at
                },
                'scraped_count': len(transformed_data),
                'data': transformed_data
            }, status=status.HTTP_200_OK)
            
        except TikTokTask.DoesNotExist:
            return Response({'error': 'Task not found'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error getting scraped data: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get a summary of all scraped data with key statistics."""
        try:
            # Get recent scraped data
            recent_data = ScrapedData.objects.all().order_by('-scraped_at')[:20]
            
            summary_results = []
            for data in recent_data:
                content = data.content
                summary_item = {
                    'id': data.id,
                    'task_id': data.task.id,
                    'task_type': data.task.task_type,
                    'data_type': data.data_type,
                    'scraped_at': data.scraped_at,
                    'preview': {
                        'description': (content.get('desc') or content.get('caption', 'N/A'))[:100] + '...' if content.get('desc') or content.get('caption') else 'N/A',
                        'author': content.get('author', 'N/A'),
                        'likes': content.get('stats', {}).get('likes', 0) if content.get('stats') else 0,
                        'comments': content.get('stats', {}).get('comments', 0) if content.get('stats') else 0
                    }
                }
                summary_results.append(summary_item)
            
            # Get overall statistics
            total_scraped = ScrapedData.objects.count()
            by_type = {}
            for data_type in ['VIDEO', 'USER', 'SEARCH_RESULT']:
                by_type[data_type] = ScrapedData.objects.filter(data_type=data_type).count()
            
            # Count tasks with data
            tasks_with_data = TikTokTask.objects.filter(scraped_data__isnull=False).distinct().count()
            
            return Response({
                'total_items': total_scraped,
                'by_type': by_type,
                'recent_items': summary_results,
                'tasks_with_data': tasks_with_data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error getting scraped data summary: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)