import logging
import json
from celery import shared_task
from django.utils import timezone
from .models import TikTokTask, ScrapedData
from .utils.tiktok_crawler import TikTokCrawler
from .utils.driver_setup import WebDriverPool
from .utils.config import CRAWLER_CONFIG, ERROR_HANDLING, WEBDRIVER_CONFIG
from .monitoring import CrawlerMonitor

logger = logging.getLogger(__name__)

# Initialize WebDriverPool globally or manage within tasks
# For simplicity, initializing here. In a real-world Celery setup, 
# you might want to manage this more carefully, e.g., using a pool per worker.
webdriver_pool = WebDriverPool(max_drivers=WEBDRIVER_CONFIG['pool_size'])

def _scrape_task(task_id: int, scrape_type: str, identifier: str, start_date: str = None, end_date: str = None, max_items=None):
    task = TikTokTask.objects.get(id=task_id)
    task.status = 'RUNNING'
    task.start_time = timezone.now()
    task.save()

    driver_id = None
    try:
        driver_id, driver = webdriver_pool.get_driver()
        crawler = TikTokCrawler(driver, timeout=CRAWLER_CONFIG['page_load_timeout'])
        scraped_data = None

        if scrape_type == 'video':
            scraped_data = crawler.scrape_video(identifier)
        elif scrape_type == 'user':
            scraped_data = crawler.scrape_user(identifier)
        elif scrape_type == 'search':
            scraped_data = crawler.search_tiktok(identifier, max_results=max_items or 50)

        if scraped_data:
            process_tiktok_data(task, scraped_data, scrape_type)
            task.status = 'COMPLETED'
            CrawlerMonitor.log_metrics(task.id, {
                'status': 'success',
                'scrape_type': scrape_type,
                'data_count': 1
            })
        else:
            task.status = 'FAILED'
            task.error_message = "No data scraped or content unavailable."
            CrawlerMonitor.log_metrics(task.id, {
                'status': 'failed',
                'scrape_type': scrape_type,
                'error': "No data scraped"
            })

    except Exception as e:
        task.status = 'FAILED'
        task.error_message = str(e)
        logger.error(f"Task {task_id} failed: {e}")
        CrawlerMonitor.log_metrics(task.id, {
            'status': 'failed',
            'scrape_type': scrape_type,
            'error': str(e)
        })
    finally:
        task.end_time = timezone.now()
        task.save()
        if driver_id:
            webdriver_pool.return_driver(driver_id)

def process_tiktok_data(task: TikTokTask, data: dict, scrape_type: str):
    if scrape_type == 'video':
        ScrapedData.objects.create(
            task=task,
            data_type='VIDEO',
            content=data
        )
    elif scrape_type == 'user':
        ScrapedData.objects.create(
            task=task,
            data_type='USER',
            content=data
        )
    elif scrape_type == 'search':
        for item in data:
            ScrapedData.objects.create(
                task=task,
                data_type='SEARCH_RESULT',
                content=item
            )

@shared_task(bind=True, max_retries=ERROR_HANDLING['max_retries'])
def scrape_tiktok_video_task(self, task_id: int, video_url: str, start_date: str = None, end_date: str = None, max_items=None):
    try:
        _scrape_task(task_id, 'video', video_url, start_date, end_date, max_items)
    except Exception as e:
        logger.error(f"Video scraping task {task_id} failed: {e}")
        self.retry(exc=e, countdown=ERROR_HANDLING['retry_delay'])

@shared_task(bind=True, max_retries=ERROR_HANDLING['max_retries'])
def scrape_tiktok_user_task(self, task_id: int, username: str, start_date: str = None, end_date: str = None, max_items=None):
    try:
        _scrape_task(task_id, 'user', username, start_date, end_date, max_items)
    except Exception as e:
        logger.error(f"User scraping task {task_id} failed: {e}")
        self.retry(exc=e, countdown=ERROR_HANDLING['retry_delay'])

@shared_task(bind=True, max_retries=ERROR_HANDLING['max_retries'])
def scrape_tiktok_search_task(self, task_id: int, keyword: str, start_date: str = None, end_date: str = None, max_items=None):
    try:
        _scrape_task(task_id, 'search', keyword, start_date, end_date, max_items)
    except Exception as e:
        logger.error(f"Search scraping task {task_id} failed: {e}")
        self.retry(exc=e, countdown=ERROR_HANDLING['retry_delay'])