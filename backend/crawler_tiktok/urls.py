from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from .views import TikTokTaskViewSet, ScrapedDataViewSet
from .views_health import health_check

router = DefaultRouter()
router.register(r'tasks', TikTokTaskViewSet)
router.register(r'scraped-data', ScrapedDataViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('health/', health_check, name='health-check'),
]