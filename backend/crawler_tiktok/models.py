from django.db import models

class TikTokTask(models.Model):
    TASK_TYPE_CHOICES = [
        ('VIDEO', 'Video Scrape'),
        ('USER', 'User Scrape'),
        ('SEARCH', 'Search Scrape'),
    ]
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('RUNNING', 'Running'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled'),
    ]

    job_name = models.CharField(max_length=255, default='default_job', help_text="Name of the scraping job")
    task_type = models.Char<PERSON>ield(max_length=10, choices=TASK_TYPE_CHOICES, default='VIDEO', help_text="Type of TikTok content to scrape")
    identifier = models.Char<PERSON>ield(max_length=255, default='default_identifier', help_text="Identifier for the task (e.g., video URL, username, search keyword)")
    status = models.Char<PERSON><PERSON>(max_length=10, choices=STATUS_CHOICES, default='PENDING', help_text="Current status of the task")
    celery_task_id = models.<PERSON>r<PERSON><PERSON>(max_length=255, null=True, blank=True, help_text="Celery task ID if applicable")
    created_at = models.DateTimeField(auto_now_add=True, help_text="Timestamp when the task was created")
    updated_at = models.DateTimeField(auto_now=True, help_text="Timestamp when the task was last updated")
    completed_at = models.DateTimeField(null=True, blank=True, help_text="Timestamp when the task was completed")
    error_message = models.TextField(null=True, blank=True, help_text="Error message if the task failed")
    start_date = models.DateField(null=True, blank=True, help_text="Start date for data collection (e.g., for search/user posts)")
    end_date = models.DateField(null=True, blank=True, help_text="End date for data collection (e.g., for search/user posts)")

    def __str__(self):
        return f"{self.job_name} - {self.task_type}: {self.identifier} ({self.status})"

class ScrapedData(models.Model):
    DATA_TYPE_CHOICES = [
        ('VIDEO', 'Video Data'),
        ('USER', 'User Data'),
        ('SEARCH_RESULT', 'Search Result Data'),
    ]

    task = models.ForeignKey(TikTokTask, on_delete=models.CASCADE, related_name='scraped_data', help_text="Associated TikTok task")
    data_type = models.CharField(max_length=20, choices=DATA_TYPE_CHOICES, help_text="Type of scraped data")
    content = models.JSONField(help_text="JSON content of the scraped data")
    scraped_at = models.DateTimeField(auto_now_add=True, help_text="Timestamp when the data was scraped")

    def __str__(self):
        return f"Scraped {self.data_type} for Task {self.task.id} at {self.scraped_at.strftime('%Y-%m-%d %H:%M:%S')}"

    class Meta:
        verbose_name_plural = "Scraped Data"



