from datetime import timedelta

# Rate Limiting Configuration
RATE_LIMITS = {
    'video': {
        'requests': 50,     # Reduced number of requests
        'window': 3600,     # Time window in seconds (1 hour)
    },
    'user': {
        'requests': 100,    # Reduced number of requests
        'window': 3600,
    },
    'search': {
        'requests': 30,     # Reduced number of requests
        'window': 3600,
    }
}

# Monitoring Configuration
MONITORING = {
    'health_check': {
        'warning_threshold': {
            'error_rate': 0.1,        # 10% error rate
            'success_rate': 0.8,       # 80% success rate
            'avg_response_time': 10.0,  # 10 seconds
        },
        'critical_threshold': {
            'error_rate': 0.2,         # 20% error rate
            'success_rate': 0.6,        # 60% success rate
            'avg_response_time': 20.0,  # 20 seconds
        },
        'time_window': 3600,          # 1 hour window for health metrics
    },
    'task_cleanup': {
        'max_age': timedelta(days=7),  # Keep task records for 7 days
        'batch_size': 1000,            # Number of tasks to cleanup in one batch
    },
    'metrics': {
        'cache_duration': 3600,       # Cache metrics for 1 hour
    }
}

# Crawler Configuration
CRAWLER = {
    'retry': {
        'max_attempts': 3,
        'min_wait': 10,     # Increased minimum wait time between retries (seconds)
        'max_wait': 30,     # Increased maximum wait time between retries (seconds)
    },
    'scroll': {
        'max_attempts': 3,  # Reduced maximum number of scroll attempts
        'wait_time': 3,     # Increased wait time between scrolls (seconds)
    },
    'timeout': {
        'page_load': 45,    # Increased page load timeout (seconds)
        'element_wait': 15,  # Increased element wait timeout (seconds)
    },
    'delays': {
        'before_action': (1, 3),    # Random delay before actions (min, max seconds)
        'after_action': (2, 5),     # Random delay after actions (min, max seconds)
        'between_requests': (5, 15), # Random delay between requests (min, max seconds)
    },
    'user_agents': {
        'rotate': True,             # Enable user agent rotation
        'update_frequency': 3600,    # Update user agent list every hour
    },
    'proxy': {
        'enabled': False,           # Enable proxy rotation (if available)
        'rotation_interval': 600,    # Rotate proxy every 10 minutes
        'max_failures': 3,          # Maximum failures before blacklisting proxy
    }
}

# WebDriver Pool Configuration
WEBDRIVER_POOL = {
    'min_size': 1,          # Reduced minimum number of drivers in pool
    'max_size': 5,          # Reduced maximum number of drivers in pool
    'timeout': 300,         # Driver checkout timeout (seconds)
    'ttl': 1800,           # Reduced time-to-live for each driver (30 minutes)
    'health_check': {
        'interval': 300,    # Health check interval (seconds)
        'timeout': 10,      # Health check timeout (seconds)
    },
    'browser': {
        'headless': True,   # Run in headless mode
        'sandbox': False,   # Disable sandbox for stability
        'images': False,    # Disable image loading
        'javascript': True, # Enable JavaScript
        'cookies': True,    # Enable cookies
    }
}