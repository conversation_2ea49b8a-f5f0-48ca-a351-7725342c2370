import logging
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .monitoring import CrawlerMonitor

logger = logging.getLogger(__name__)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def health_check(request):
    """Health check endpoint for the TikTok crawler system.
    Returns the current health status and metrics."""
    try:
        health_data = CrawlerMonitor.check_health()
        return Response(health_data, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return Response(
            {
                'status': 'critical',
                'message': f'Health check failed: {str(e)}',
                'metrics': {
                    'success_rate': 0,
                    'error_rate': 1.0,
                    'average_response_time': 0
                }
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )