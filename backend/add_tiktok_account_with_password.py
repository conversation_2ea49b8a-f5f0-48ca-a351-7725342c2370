#!/usr/bin/env python3
"""
Add TikTok Account with Password

Script to add a TikTok account with proper encrypted password for testing.
"""

import os
import sys
import django
from django.conf import settings

# Add the backend directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/fullstax/backend')

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from actor.models import ActorAccount
from actor.services.actor_service import ActorService
from actor.engines.tiktok_engine import TikTokEngine
from actor.utils.simple_tiktok_auth import SimpleTikTokAuthenticator

def add_test_tiktok_account():
    """Add a test TikTok account with proper credentials"""
    print("\n=== Adding Test TikTok Account ===")
    
    try:
        # Get or create test user
        test_user, created = User.objects.get_or_create(
            username='test_tiktok_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User'
            }
        )
        
        if created:
            print(f"✅ Created new user: {test_user.username}")
        else:
            print(f"✅ Using existing user: {test_user.username}")
        
        # Test credentials (replace with real ones for actual testing)
        tiktok_username = "test_tiktok_account"
        tiktok_password = "test_password_123"
        
        # Check if account already exists
        existing_account = ActorAccount.objects.filter(
            user=test_user,
            platform='tiktok',
            platform_username=tiktok_username
        ).first()
        
        if existing_account:
            print(f"✅ Account already exists: @{existing_account.platform_username}")
            
            # Update password if needed
            if not existing_account.get_decrypted_password():
                print("🔐 Adding password to existing account...")
                existing_account.encrypt_password(tiktok_password)
                existing_account.save()
                print("✅ Password added successfully")
            
            return existing_account
        
        # Create new account
        print(f"🆕 Creating new TikTok account: @{tiktok_username}")
        
        account = ActorAccount(
            user=test_user,
            platform='tiktok',
            platform_username=tiktok_username,
            email='<EMAIL>',
            is_active=True
        )
        
        # Encrypt and set password
        account.encrypt_password(tiktok_password)
        
        # Set initial session data
        account.encrypt_session_data({})
        
        # Save account
        account.save()
        
        print(f"✅ Account created successfully!")
        print(f"   Account ID: {account.id}")
        print(f"   Username: @{account.platform_username}")
        print(f"   Password: {'*' * len(tiktok_password)}")
        
        # Verify password encryption/decryption
        decrypted = account.get_decrypted_password()
        if decrypted == tiktok_password:
            print(f"✅ Password encryption/decryption working correctly")
        else:
            print(f"❌ Password encryption/decryption failed")
        
        return account
        
    except Exception as e:
        print(f"❌ Error adding TikTok account: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_account_authentication(account):
    """Test authentication with the created account"""
    print(f"\n=== Testing Account Authentication ===")
    
    try:
        print(f"🔐 Testing authentication for @{account.platform_username}")
        
        # Test 1: Direct TikTokEngine authentication
        print("\n1️⃣ Testing TikTokEngine.authenticate()")
        engine = TikTokEngine()
        
        credentials = {
            'username': account.platform_username,
            'password': account.get_decrypted_password()
        }
        
        result = engine.authenticate(account, credentials)
        
        if result.get('success'):
            print("✅ TikTokEngine authentication successful!")
            print(f"   Message: {result.get('message')}")
        else:
            print(f"❌ TikTokEngine authentication failed: {result.get('error')}")
        
        # Test 2: ActorService authentication
        print("\n2️⃣ Testing ActorService.authenticate_account()")
        actor_service = ActorService()
        
        result = actor_service.authenticate_account(account.id)
        
        if result.get('success'):
            print("✅ ActorService authentication successful!")
            print(f"   Message: {result.get('message')}")
        else:
            print(f"❌ ActorService authentication failed: {result.get('error')}")
        
        # Test 3: Direct SimpleTikTokAuthenticator
        print("\n3️⃣ Testing SimpleTikTokAuthenticator.login()")
        auth = SimpleTikTokAuthenticator()
        
        result = auth.login(
            account.platform_username,
            account.get_decrypted_password()
        )
        
        if result.get('success'):
            print("✅ SimpleTikTokAuthenticator login successful!")
            print(f"   Session info keys: {list(result.get('session_info', {}).keys())}")
        else:
            print(f"❌ SimpleTikTokAuthenticator login failed: {result.get('error')}")
            print(f"   Current URL: {result.get('current_url')}")
        
    except Exception as e:
        print(f"❌ Error testing authentication: {e}")
        import traceback
        traceback.print_exc()

def update_existing_account_with_real_credentials():
    """Update an existing account with real credentials for testing"""
    print(f"\n=== Updating Existing Account with Real Credentials ===")
    
    try:
        # Get the first TikTok account
        account = ActorAccount.objects.filter(
            platform='tiktok',
            is_active=True
        ).first()
        
        if not account:
            print("❌ No TikTok accounts found")
            return None
        
        print(f"📱 Found account: @{account.platform_username}")
        
        # For testing purposes, use test credentials
        # In real scenario, you would use actual TikTok credentials
        test_password = "test_password_123"
        
        print(f"🔐 Adding test password to account...")
        account.encrypt_password(test_password)
        account.save()
        
        # Verify password was saved
        decrypted = account.get_decrypted_password()
        if decrypted == test_password:
            print(f"✅ Password added successfully: {'*' * len(test_password)}")
            return account
        else:
            print(f"❌ Password verification failed")
            return None
        
    except Exception as e:
        print(f"❌ Error updating account: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    print("🔧 TikTok Account Setup and Testing")
    print("=" * 50)
    
    # Option 1: Add new test account
    print("\n🆕 Option 1: Add new test account")
    test_account = add_test_tiktok_account()
    
    if test_account:
        test_account_authentication(test_account)
    
    # Option 2: Update existing account
    print("\n🔄 Option 2: Update existing account")
    existing_account = update_existing_account_with_real_credentials()
    
    if existing_account:
        test_account_authentication(existing_account)
    
    print("\n=== Setup Complete ===")
    print("\nNext steps:")
    print("1. Replace test credentials with real TikTok account credentials")
    print("2. Test authentication with real credentials")
    print("3. Verify TikTok login selectors are up to date")
    print("4. Test task creation and execution")
    
    # Show all TikTok accounts
    print("\n📋 Current TikTok Accounts:")
    accounts = ActorAccount.objects.filter(platform='tiktok')
    for i, acc in enumerate(accounts, 1):
        password_status = "✅ Available" if acc.get_decrypted_password() else "❌ Missing"
        print(f"   {i}. @{acc.platform_username} - Password: {password_status}")

if __name__ == '__main__':
    main()