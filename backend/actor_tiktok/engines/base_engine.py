"""
Base Engine for Actor Platform System

This module defines the abstract base class that all platform engines must implement.
Each platform (TikTok, Instagram, Facebook, Twitter, YouTube) will have its own engine
that inherits from this base class.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)


class BaseActorEngine(ABC):
    """
    Abstract base class for all platform engines in the Actor system.
    
    Each platform engine must implement these methods to provide a consistent
    interface for authentication, scraping, and data processing.
    """
    
    def __init__(self, platform: str):
        self.platform = platform
        self.logger = logging.getLogger(f"{__name__}.{platform}")
    
    @abstractmethod
    def authenticate(self, account, credentials: Dict[str, Any]) -> Dict[str, Any]:
        """
        Authenticate with the platform using provided credentials.
        
        Args:
            account: ActorAccount instance
            credentials: Dictionary containing authentication credentials
            
        Returns:
            Dict containing authentication result with success status and session data
        """
        pass
    
    @abstractmethod
    def verify_session(self, account) -> bool:
        """
        Verify if the current session is still valid.
        
        Args:
            account: ActorAccount instance
            
        Returns:
            Boolean indicating if session is valid
        """
        pass
    
    @abstractmethod
    def scrape_user_profile(self, account, target_username: str, **kwargs) -> Dict[str, Any]:
        """
        Scrape user profile data.
        
        Args:
            account: ActorAccount instance
            target_username: Username to scrape
            **kwargs: Additional parameters
            
        Returns:
            Dict containing scraped profile data
        """
        pass
    
    @abstractmethod
    def scrape_user_content(self, account, target_username: str, limit: int = 50, **kwargs) -> List[Dict[str, Any]]:
        """
        Scrape user's content (videos, posts, etc.).
        
        Args:
            account: ActorAccount instance
            target_username: Username to scrape
            limit: Maximum number of items to scrape
            **kwargs: Additional parameters
            
        Returns:
            List of scraped content items
        """
        pass
    
    @abstractmethod
    def search_content(self, account, keywords: List[str], limit: int = 50, **kwargs) -> List[Dict[str, Any]]:
        """
        Search for content using keywords.
        
        Args:
            account: ActorAccount instance
            keywords: List of keywords to search for
            limit: Maximum number of items to return
            **kwargs: Additional parameters (date_range, filters, etc.)
            
        Returns:
            List of search results
        """
        pass
    
    @abstractmethod
    def scrape_my_content(self, account, limit: int = 50, **kwargs) -> List[Dict[str, Any]]:
        """
        Scrape authenticated user's own content.
        
        Args:
            account: ActorAccount instance
            limit: Maximum number of items to scrape
            **kwargs: Additional parameters
            
        Returns:
            List of user's own content
        """
        pass
    
    @abstractmethod
    def scrape_feed(self, account, limit: int = 50, **kwargs) -> List[Dict[str, Any]]:
        """
        Scrape user's personalized feed.
        
        Args:
            account: ActorAccount instance
            limit: Maximum number of items to scrape
            **kwargs: Additional parameters
            
        Returns:
            List of feed items
        """
        pass
    
    def normalize_data(self, raw_data: Dict[str, Any], data_type: str) -> Dict[str, Any]:
        """
        Normalize platform-specific data to a common format.
        
        Args:
            raw_data: Raw data from the platform
            data_type: Type of data (video, post, user, etc.)
            
        Returns:
            Normalized data dictionary
        """
        # Default implementation - can be overridden by specific engines
        return {
            'platform': self.platform,
            'data_type': data_type,
            'raw_data': raw_data,
            'normalized_at': self._get_current_timestamp()
        }
    
    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def get_platform_specific_id(self, data: Dict[str, Any]) -> Optional[str]:
        """
        Extract platform-specific ID from data.
        
        Args:
            data: Data dictionary
            
        Returns:
            Platform-specific ID or None
        """
        # Default implementation - should be overridden by specific engines
        return data.get('id') or data.get('platform_id')
    
    def handle_rate_limiting(self, retry_count: int = 0) -> bool:
        """
        Handle rate limiting for the platform.
        
        Args:
            retry_count: Current retry attempt
            
        Returns:
            Boolean indicating if operation should be retried
        """
        # Default implementation - can be overridden
        import time
        import random
        
        if retry_count >= 3:
            return False
        
        # Exponential backoff with jitter
        delay = (2 ** retry_count) + random.uniform(0, 1)
        self.logger.info(f"Rate limited, waiting {delay:.2f} seconds before retry {retry_count + 1}")
        time.sleep(delay)
        return True
    
    def cleanup_session(self, account) -> None:
        """
        Clean up session resources.
        
        Args:
            account: ActorAccount instance
        """
        # Default implementation - can be overridden
        self.logger.info(f"Cleaning up session for {self.platform} account: {account.platform_username}")


class EngineRegistry:
    """Registry for platform engines."""
    
    _engines = {}
    
    @classmethod
    def register(cls, platform: str, engine_class):
        """Register an engine for a platform."""
        cls._engines[platform] = engine_class
    
    @classmethod
    def get_engine(cls, platform: str) -> Optional[BaseActorEngine]:
        """Get engine instance for a platform."""
        engine_class = cls._engines.get(platform)
        if engine_class:
            return engine_class(platform)
        return None
    
    @classmethod
    def get_available_platforms(cls) -> List[str]:
        """Get list of available platforms."""
        return list(cls._engines.keys())
