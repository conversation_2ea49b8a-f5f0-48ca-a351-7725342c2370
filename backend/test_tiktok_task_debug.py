#!/usr/bin/env python3
"""
Debug TikTok Task Execution

This script manually tests TikTok task execution to identify why tasks
complete successfully but scrape 0 items.
"""

import os
import sys
import django
from datetime import datetime

# Add the project directory to Python path
sys.path.append('/Users/<USER>/Documents/fullstax/backend')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fullstax.settings')
django.setup()

from actor.models import ActorAccount, ActorTask, ActorScrapedData
from actor.engines.tiktok_engine import TikTokEngine
from actor.scrapers.real_tiktok_scraper import RealTikTokScraper
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_tiktok_authentication():
    """Test TikTok authentication with a real account"""
    print("\n=== Testing TikTok Authentication ===")
    
    try:
        # Get a TikTok account
        tiktok_accounts = ActorAccount.objects.filter(platform='tiktok')
        if not tiktok_accounts.exists():
            print("❌ No TikTok accounts found")
            return False
            
        account = tiktok_accounts.first()
        print(f"✅ Found TikTok account: @{account.platform_username}")
        
        # Test engine authentication
        engine = TikTokEngine()
        
        # Check session validity
        session_valid = engine.verify_session(account)
        print(f"📱 Session valid: {session_valid}")
        
        if not session_valid:
            print("⚠️ Session invalid - attempting authentication")
            
            # Try to get password
            try:
                password = account.get_decrypted_password()
                if password:
                    print("🔑 Password available for authentication")
                    
                    # Test direct scraper login
                    scraper = RealTikTokScraper()
                    login_result = scraper.login(account.platform_username, password)
                    print(f"🔐 Direct login result: {login_result}")
                    scraper.close()
                    
                    return login_result.get('success', False)
                else:
                    print("❌ No password available for authentication")
                    return False
            except Exception as e:
                print(f"❌ Authentication error: {str(e)}")
                return False
        else:
            print("✅ Session is valid")
            return True
            
    except Exception as e:
        print(f"❌ Authentication test failed: {str(e)}")
        return False

def test_tiktok_search():
    """Test TikTok search functionality"""
    print("\n=== Testing TikTok Search ===")
    
    try:
        # Get a TikTok account
        account = ActorAccount.objects.filter(platform='tiktok').first()
        if not account:
            print("❌ No TikTok account available")
            return []
            
        print(f"🔍 Testing search with account: @{account.platform_username}")
        
        # Test engine search
        engine = TikTokEngine()
        
        # Test search with a simple keyword
        test_keywords = ['dance']
        limit = 5
        
        print(f"🎯 Searching for: {test_keywords} (limit: {limit})")
        
        results = engine.search_content(
            account=account,
            keywords=test_keywords,
            limit=limit
        )
        
        print(f"📊 Search results: {len(results)} items")
        
        if results:
            for i, result in enumerate(results[:3]):
                print(f"  {i+1}. {result.get('desc', 'No description')[:50]}...")
                print(f"     Author: {result.get('author', {}).get('uniqueId', 'Unknown')}")
                print(f"     Real scraped: {result.get('real_scraped', False)}")
        else:
            print("⚠️ No results returned")
            
        return results
        
    except Exception as e:
        print(f"❌ Search test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def test_direct_scraper():
    """Test the RealTikTokScraper directly"""
    print("\n=== Testing Direct Scraper ===")
    
    try:
        # Get account credentials
        account = ActorAccount.objects.filter(platform='tiktok').first()
        if not account:
            print("❌ No TikTok account available")
            return []
            
        password = account.get_decrypted_password()
        if not password:
            print("❌ No password available")
            return []
            
        print(f"🤖 Testing direct scraper with @{account.platform_username}")
        
        scraper = RealTikTokScraper()
        
        # Test login
        print("🔐 Attempting login...")
        login_result = scraper.login(account.platform_username, password)
        print(f"Login result: {login_result}")
        
        if login_result.get('success'):
            print("✅ Login successful")
            
            # Test search
            print("🔍 Testing search...")
            search_result = scraper.search_videos('dance', 3)
            print(f"Search result: {search_result}")
            
            videos = search_result.get('videos', [])
            print(f"📊 Found {len(videos)} videos")
            
            scraper.close()
            return videos
        else:
            print(f"❌ Login failed: {login_result.get('error')}")
            scraper.close()
            return []
            
    except Exception as e:
        print(f"❌ Direct scraper test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def test_task_execution():
    """Test actual task execution"""
    print("\n=== Testing Task Execution ===")
    
    try:
        # Get the latest failed task
        recent_tasks = ActorTask.objects.filter(
            actor_account__platform='tiktok',
            status='COMPLETED',
            items_scraped=0
        ).order_by('-created_at')[:1]
        
        if not recent_tasks.exists():
            print("❌ No recent failed tasks found")
            return
            
        task = recent_tasks.first()
        print(f"🎯 Testing task: {task.id} - {task.task_type}")
        print(f"   Keywords: {task.keywords}")
        print(f"   Max items: {task.max_items}")
        print(f"   Account: @{task.actor_account.platform_username}")
        
        # Manually execute the task logic
        from actor.engines.tiktok_engine import TikTokEngine
        
        engine = TikTokEngine()
        account = task.actor_account
        
        # Get task parameters
        keywords = task.keywords or ""
        hashtag = keywords.replace('#', '').strip()
        limit = task.max_items or 50
        
        print(f"🔍 Executing search for hashtag: #{hashtag}, limit: {limit}")
        
        # Perform search
        results = engine.search_content(
            account=account,
            keywords=[f"#{hashtag}"],
            limit=limit
        )
        
        print(f"📊 Manual execution results: {len(results)} items")
        
        if results:
            print("✅ Manual execution successful - data was found")
            print("🔍 This suggests the issue might be in task processing or data saving")
            
            # Check if data would be saved correctly
            for i, result in enumerate(results[:3]):
                print(f"  Sample {i+1}:")
                print(f"    ID: {result.get('id', 'N/A')}")
                print(f"    Description: {result.get('desc', 'N/A')[:50]}...")
                print(f"    Real scraped: {result.get('real_scraped', False)}")
        else:
            print("❌ Manual execution also returned no results")
            print("🔍 This suggests the issue is in the scraping logic itself")
            
    except Exception as e:
        print(f"❌ Task execution test failed: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """Run all debug tests"""
    print("🚀 Starting TikTok Task Debug Tests")
    print(f"⏰ Timestamp: {datetime.now()}")
    
    # Test 1: Authentication
    auth_success = test_tiktok_authentication()
    
    # Test 2: Search functionality
    search_results = test_tiktok_search()
    
    # Test 3: Direct scraper
    direct_results = test_direct_scraper()
    
    # Test 4: Task execution
    test_task_execution()
    
    # Summary
    print("\n=== Debug Summary ===")
    print(f"🔐 Authentication: {'✅ Success' if auth_success else '❌ Failed'}")
    print(f"🔍 Engine Search: {'✅ Success' if search_results else '❌ Failed'} ({len(search_results)} items)")
    print(f"🤖 Direct Scraper: {'✅ Success' if direct_results else '❌ Failed'} ({len(direct_results)} items)")
    
    if not auth_success:
        print("\n🎯 ISSUE IDENTIFIED: Authentication is failing")
        print("   - Check TikTok account credentials")
        print("   - Verify password decryption")
        print("   - Check TikTok login process")
    elif not search_results and not direct_results:
        print("\n🎯 ISSUE IDENTIFIED: Scraping is failing")
        print("   - TikTok may have changed their page structure")
        print("   - Selenium selectors may need updating")
        print("   - Anti-bot measures may be blocking scraping")
    elif search_results or direct_results:
        print("\n🎯 ISSUE IDENTIFIED: Scraping works but task processing fails")
        print("   - Check Celery task implementation")
        print("   - Verify data saving logic")
        print("   - Check ActorScrapedData creation")
    
    print("\n✅ Debug tests completed")

if __name__ == '__main__':
    main()