# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
*.manifest
*.spec
pip-log.txt
pip-delete-this-directory.txt

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media

# Node
node_modules/
.next/
out/
build/
.DS_Store
*.pem
.env.local
.env.development.local
.env.test.local
.env.production.local
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.vercel

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Environment variables
.env
.env.*
