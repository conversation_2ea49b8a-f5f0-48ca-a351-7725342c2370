#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append('/Users/<USER>/Documents/fullstax/backend')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor.models import ActorTask, ActorAccount
from actor.services import ActorService
from django.contrib.auth.models import User

def test_tiktok_task_execution():
    print("Testing TikTok task execution with proper field names...")
    
    try:
        # Get the task
        task = ActorTask.objects.get(id=131)
        print(f"Found task: {task.task_name} (ID: {task.id})")
        print(f"Task type: {task.task_type}")
        print(f"Platform: {task.platform}")
        print(f"Status: {task.status}")
        print(f"Max items: {task.max_items}")
        print(f"Target identifier: {task.target_identifier}")
        print(f"Keywords: {task.keywords}")
        
        # Check task parameters
        print(f"Task parameters: {task.task_parameters}")
        
        # Get the associated account
        if task.actor_account:
            account = task.actor_account
            print(f"\nAssociated account: {account.platform_username} on {account.platform}")
            print(f"Account active: {account.is_active}")
            print(f"Session valid: {account.is_session_valid()}")
            
            # Check session data
            session_data = account.decrypt_session_data()
            print(f"Session data available: {bool(session_data)}")
            if session_data:
                print(f"Session keys: {list(session_data.keys())}")
        else:
            print("\nNo actor_account associated with this task")
            
        if task.tiktok_account:
            tiktok_account = task.tiktok_account
            print(f"\nBackward compatibility TikTok account: {tiktok_account.tiktok_username}")
        else:
            print("\nNo tiktok_account associated with this task")
        
        # Try to execute the task
        print("\n=== Attempting to execute task ===")
        service = ActorService()
        
        # Execute the task
        result = service.execute_task(task.id)
        print(f"Task execution result: {result}")
        
        # Check task status after execution
        task.refresh_from_db()
        print(f"\nTask status after execution: {task.status}")
        print(f"Items scraped: {task.items_scraped}")
        print(f"Progress: {task.progress_percentage}%")
        
        if task.error_message:
            print(f"Error message: {task.error_message}")
            
    except ActorTask.DoesNotExist:
        print("Task with ID 131 not found")
    except Exception as e:
        print(f"Error during task execution: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tiktok_task_execution()