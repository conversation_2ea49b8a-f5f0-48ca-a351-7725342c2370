#!/usr/bin/env python
import os
import sys
import django
import time

# Add the backend directory to the Python path
sys.path.append('/Users/<USER>/Documents/fullstax/backend')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

def check_tiktok_captcha_and_verification():
    print("🔍 Checking for CAPTCHA and verification elements on TikTok login...")
    
    username = "grafisone"
    password = "Puyol@102410"
    
    driver = None
    try:
        # Setup Chrome driver with more stealth options
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # More realistic user agent
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        chrome_options.add_argument("--window-size=1280,720")
        
        # Additional stealth options
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--disable-extensions")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        wait = WebDriverWait(driver, 15)
        
        # Remove webdriver property and add more stealth
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
        driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})")
        
        print("✅ WebDriver initialized with stealth options")
        
        # Navigate to TikTok login page
        print("\n📍 Navigating to TikTok login page...")
        driver.get("https://www.tiktok.com/login/phone-or-email/email")
        time.sleep(5)
        
        print(f"Current URL: {driver.current_url}")
        
        # Check for CAPTCHA elements
        print("\n🤖 Checking for CAPTCHA elements...")
        captcha_selectors = [
            'iframe[src*="captcha"]',
            'div[class*="captcha"]',
            'div[id*="captcha"]',
            '.captcha',
            '#captcha',
            'iframe[title*="reCAPTCHA"]',
            'div[class*="recaptcha"]',
            '.g-recaptcha',
            'div[data-sitekey]',
            'canvas',  # Often used for puzzle captchas
            'div[class*="puzzle"]',
            'div[class*="verification"]',
            'div[class*="challenge"]'
        ]
        
        captcha_found = False
        for selector in captcha_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    for element in elements:
                        if element.is_displayed():
                            print(f"  🚨 CAPTCHA/Verification element found: {selector}")
                            print(f"     Element text: '{element.text[:100]}'")
                            print(f"     Element class: '{element.get_attribute('class')}'")
                            captcha_found = True
            except Exception as e:
                print(f"  Error checking {selector}: {e}")
        
        if not captcha_found:
            print("  ✅ No obvious CAPTCHA elements found")
        
        # Fill in the form
        print("\n📝 Filling login form...")
        
        # Username
        username_field = driver.find_element(By.NAME, "username")
        username_field.clear()
        username_field.send_keys(username)
        time.sleep(2)
        
        # Password
        password_field = driver.find_element(By.CSS_SELECTOR, 'input[type="password"]')
        password_field.clear()
        password_field.send_keys(password)
        time.sleep(2)
        
        print("✅ Form filled")
        
        # Check for any new CAPTCHA elements after filling
        print("\n🔍 Checking for CAPTCHA after form fill...")
        time.sleep(2)
        
        for selector in captcha_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    for element in elements:
                        if element.is_displayed():
                            print(f"  🚨 New CAPTCHA element appeared: {selector}")
                            captcha_found = True
            except:
                pass
        
        # Click login and monitor for verification
        print("\n🚀 Clicking login button...")
        login_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
        login_button.click()
        
        # Wait and check for verification/CAPTCHA after login attempt
        print("\n⏳ Waiting for response and checking for verification...")
        time.sleep(8)
        
        # Check current URL and page content
        current_url = driver.current_url
        page_title = driver.title
        
        print(f"\nAfter login attempt:")
        print(f"  URL: {current_url}")
        print(f"  Title: {page_title}")
        
        # Look for verification/error messages
        verification_selectors = [
            'div[class*="error"]',
            'div[class*="verification"]',
            'div[class*="challenge"]',
            'div[class*="security"]',
            'div[class*="confirm"]',
            'div[class*="verify"]',
            '.error-message',
            '.verification-message',
            'span[class*="error"]',
            'p[class*="error"]'
        ]
        
        print("\n📋 Checking for verification/error messages...")
        messages_found = False
        for selector in verification_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.text.strip():
                        print(f"  📝 Message found ({selector}): '{element.text.strip()}'")
                        messages_found = True
            except:
                pass
        
        if not messages_found:
            print("  ✅ No obvious error/verification messages found")
        
        # Check if we're still on login page
        if "login" in current_url.lower():
            print("\n❌ Still on login page - login failed")
            
            # Try to find any hidden elements or scripts that might indicate why
            print("\n🔍 Analyzing page for clues...")
            
            # Check for any JavaScript errors in console
            try:
                logs = driver.get_log('browser')
                if logs:
                    print("  📜 Browser console logs:")
                    for log in logs[-5:]:  # Show last 5 logs
                        print(f"    {log['level']}: {log['message'][:100]}")
            except:
                print("  ❌ Could not retrieve console logs")
            
            # Check for any network requests that might indicate blocking
            try:
                performance_logs = driver.get_log('performance')
                if performance_logs:
                    print(f"  🌐 Found {len(performance_logs)} network requests")
                    # Look for any requests that might indicate verification
                    for log in performance_logs[-10:]:
                        message = log.get('message', {})
                        if isinstance(message, str):
                            import json
                            try:
                                message = json.loads(message)
                            except:
                                continue
                        
                        if message.get('message', {}).get('method') == 'Network.responseReceived':
                            url = message.get('message', {}).get('params', {}).get('response', {}).get('url', '')
                            status = message.get('message', {}).get('params', {}).get('response', {}).get('status', 0)
                            if 'captcha' in url.lower() or 'verify' in url.lower() or status >= 400:
                                print(f"    🚨 Suspicious request: {url} (Status: {status})")
            except:
                print("  ❌ Could not retrieve performance logs")
        else:
            print("\n✅ Login appears successful - redirected away from login page")
        
        # Take final screenshot
        screenshot_path = "/Users/<USER>/Documents/fullstax/tiktok_captcha_check.png"
        driver.save_screenshot(screenshot_path)
        print(f"\n📸 Screenshot saved: {screenshot_path}")
        
        # Wait before closing
        print("\n⏳ Waiting 10 seconds before closing...")
        time.sleep(10)
        
    except Exception as e:
        print(f"❌ Error during CAPTCHA check: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        if driver:
            try:
                driver.quit()
                print("\n🔧 WebDriver closed")
            except:
                pass

if __name__ == "__main__":
    check_tiktok_captcha_and_verification()