#!/usr/bin/env python3
"""
Test script to create a task and test the execute button functionality.
"""

import os
import sys
import django
from datetime import datetime

# Add the backend directory to Python path
sys.path.append('/Users/<USER>/Documents/fullstax/backend')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from actor.models import ActorAccount, ActorTask
from actor.services.actor_service import ActorService

def create_and_execute_test_task():
    """
    Create a test task and execute it to verify the execute button functionality.
    """
    print("🔍 Creating and Executing Test Task")
    print("=" * 40)
    
    # Initialize service
    actor_service = ActorService()
    
    try:
        # Get a test user
        user = User.objects.first()
        if not user:
            print("❌ No users found")
            return
            
        print(f"👤 Using user: {user.username}")
        
        # Get a TikTok account
        tiktok_account = ActorAccount.objects.filter(user=user, platform='tiktok').first()
        
        if not tiktok_account:
            print("❌ No TikTok accounts found")
            return
            
        print(f"📱 Using account: {tiktok_account.platform_username} (ID: {tiktok_account.id})")
        print(f"🔐 Session valid: {tiktok_account.is_session_valid()}")
        
        # Create a test task
        print("\n📋 Creating test task...")
        
        task_data = {
            'task_name': 'Execute Button Test Task',
            'task_type': 'HASHTAG_SCRAPE',
            'keywords': 'test',
            'max_items': 5,
            'task_parameters': {
                'hashtag': 'test',
                'limit': 5
            }
        }
        
        create_result = actor_service.create_task(
            user=user,
            account_id=tiktok_account.id,
            **task_data
        )
        
        if not create_result.get('success'):
            print(f"❌ Failed to create task: {create_result.get('error')}")
            return
            
        task_id = create_result.get('task_id')
        print(f"✅ Task created successfully with ID: {task_id}")
        
        # Get the created task
        task = ActorTask.objects.get(id=task_id)
        print(f"📊 Task status: {task.status}")
        print(f"🎯 Task type: {task.task_type}")
        print(f"🔤 Keywords: {task.keywords}")
        
        # Now test the execute functionality (simulate execute button click)
        print("\n🚀 Testing Execute Button Functionality...")
        print("This simulates what happens when you click the execute button in the frontend.")
        
        execute_result = actor_service.execute_task_async(task_id, user=user)
        
        print(f"\n📊 Execute Result:")
        print(f"   Success: {execute_result.get('success')}")
        print(f"   Message: {execute_result.get('message')}")
        print(f"   Status: {execute_result.get('status')}")
        print(f"   Celery Task ID: {execute_result.get('celery_task_id')}")
        
        if execute_result.get('success'):
            print("\n🎉 SUCCESS: Execute button is working!")
            print("✅ Celery task has been dispatched successfully")
            
            # Check task status after execution
            task.refresh_from_db()
            print(f"\n📊 Updated task status: {task.status}")
            print(f"🆔 Celery task ID stored: {task.celery_task_id}")
            
            # Wait a moment and check if task is being processed
            import time
            print("\n⏳ Waiting 3 seconds to check task progress...")
            time.sleep(3)
            
            task.refresh_from_db()
            print(f"📊 Task status after 3 seconds: {task.status}")
            print(f"📈 Items scraped: {task.items_scraped}")
            
            if task.status == 'RUNNING':
                print("✅ Task is being processed by Celery worker")
            elif task.status == 'COMPLETED':
                print("✅ Task completed successfully")
            elif task.status == 'FAILED':
                print(f"❌ Task failed: {task.error_message}")
            
        else:
            print(f"\n❌ FAILED: Execute button not working")
            print(f"Error: {execute_result.get('error')}")
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()

def check_celery_worker_status():
    """
    Check if Celery worker is running and can process tasks.
    """
    print("\n🔧 Checking Celery Worker Status")
    print("=" * 35)
    
    try:
        from backend.celery import app
        
        # Check if we can inspect the Celery worker
        inspect = app.control.inspect()
        
        # Get worker stats
        stats = inspect.stats()
        if stats:
            print("✅ Celery worker is running")
            for worker, worker_stats in stats.items():
                print(f"   Worker: {worker}")
                print(f"   Pool: {worker_stats.get('pool', {}).get('max-concurrency', 'Unknown')} processes")
        else:
            print("❌ No Celery workers found")
            
        # Check active tasks
        active = inspect.active()
        if active:
            print(f"\n📊 Active tasks: {len(sum(active.values(), []))}")
            for worker, tasks in active.items():
                if tasks:
                    print(f"   {worker}: {len(tasks)} active tasks")
        else:
            print("\n📊 No active tasks")
            
    except Exception as e:
        print(f"❌ Error checking Celery status: {str(e)}")

if __name__ == "__main__":
    print("🧪 Execute Button Test")
    print("=" * 25)
    print(f"⏰ Test started at: {datetime.now()}")
    
    check_celery_worker_status()
    create_and_execute_test_task()
    
    print(f"\n⏰ Test completed at: {datetime.now()}")