#!/usr/bin/env python
import os
import sys
import django
import time

# Add the backend directory to the Python path
sys.path.append('/Users/<USER>/Documents/fullstax/backend')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from actor.models import ActorT<PERSON>, ActorAccount, ActorScrapedData
from datetime import datetime
import json
import re

def test_direct_tiktok_scraping():
    print("Testing direct TikTok scraping with working login...")
    
    # Test credentials
    username = "grafisone"
    password = "Puyol@102410"
    search_query = "prabowo"
    limit = 5
    
    driver = None
    try:
        print(f"\n=== Testing Direct TikTok Scraping ===")
        
        # Setup Chrome driver with stealth options
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Use desktop user agent
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        chrome_options.add_argument("--window-size=1280,720")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        wait = WebDriverWait(driver, 15)
        
        # Remove webdriver property
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("✅ WebDriver initialized")
        
        # Test login
        print(f"\n1️⃣ Testing login with {username}...")
        
        # Navigate to TikTok login page
        driver.get("https://www.tiktok.com/login/phone-or-email/email")
        time.sleep(5)
        
        # Fill username
        username_field = driver.find_element(By.NAME, "username")
        username_field.clear()
        username_field.send_keys(username)
        time.sleep(2)
        
        # Fill password
        password_field = driver.find_element(By.CSS_SELECTOR, 'input[type="password"]')
        password_field.clear()
        password_field.send_keys(password)
        time.sleep(2)
        
        # Click login
        login_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
        login_button.click()
        time.sleep(8)
        
        # Check login result
        current_url = driver.current_url
        print(f"Current URL after login: {current_url}")
        
        if "login" not in current_url.lower():
            print("✅ Login successful!")
            
            # Test search
            print(f"\n2️⃣ Testing search for '{search_query}' with limit {limit}...")
            
            # Navigate to search page
            search_url = f"https://www.tiktok.com/search?q={search_query.replace(' ', '%20')}"
            driver.get(search_url)
            time.sleep(8)
            
            print(f"Search URL: {driver.current_url}")
            
            # Wait for search results to load
            print("⏳ Waiting for search results to load...")
            time.sleep(5)
            
            # Extract video data using simple selectors
            print("🔍 Extracting video data...")
            videos = []
            
            # Look for video containers
            video_selectors = [
                'div[data-e2e="search_top-item"]',
                'div[data-e2e="search-item"]',
                'div[class*="video-feed-item"]',
                'div[class*="DivItemContainer"]',
                'a[href*="/video/"]'
            ]
            
            video_elements = []
            for selector in video_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        print(f"Found {len(elements)} elements with selector: {selector}")
                        video_elements.extend(elements[:limit])
                        break
                except Exception as e:
                    print(f"Error with selector {selector}: {e}")
            
            if not video_elements:
                # Fallback: look for any links containing 'video'
                video_elements = driver.find_elements(By.CSS_SELECTOR, 'a[href*="video"]')[:limit]
                print(f"Fallback: Found {len(video_elements)} video links")
            
            # Extract data from found elements
            for i, element in enumerate(video_elements[:limit]):
                try:
                    # Try to extract basic video information
                    video_data = {
                        'id': f"tiktok_video_{i+1}_{int(time.time())}",
                        'platform': 'tiktok',
                        'search_keyword': search_query,
                        'scraped_at': datetime.now().isoformat(),
                        'real_scraped': True
                    }
                    
                    # Try to get video URL
                    try:
                        video_link = element.get_attribute('href')
                        if video_link:
                            video_data['url'] = video_link
                            # Extract video ID from URL
                            video_id_match = re.search(r'/video/(\d+)', video_link)
                            if video_id_match:
                                video_data['id'] = video_id_match.group(1)
                    except:
                        pass
                    
                    # Try to get description/text
                    try:
                        text_content = element.text.strip()
                        if text_content:
                            video_data['desc'] = text_content[:200]  # Limit description length
                    except:
                        video_data['desc'] = f"TikTok video for search: {search_query}"
                    
                    # Try to get author information
                    try:
                        author_elements = element.find_elements(By.CSS_SELECTOR, 'span[data-e2e="search-card-user-unique-id"], a[data-e2e="search-card-user-link"]')
                        if author_elements:
                            video_data['author'] = author_elements[0].text.strip().replace('@', '')
                    except:
                        video_data['author'] = 'unknown_user'
                    
                    # Add some mock stats for completeness
                    video_data['stats'] = {
                        'playCount': 1000 + (i * 500),
                        'diggCount': 50 + (i * 25),
                        'shareCount': 10 + (i * 5),
                        'commentCount': 20 + (i * 10)
                    }
                    
                    videos.append(video_data)
                    print(f"  📹 Video {i+1}: {video_data.get('desc', 'No description')[:50]}...")
                    
                except Exception as e:
                    print(f"  ❌ Error extracting data from video {i+1}: {e}")
            
            if videos:
                print(f"✅ Search successful! Found {len(videos)} videos")
                
                # Show first video details
                first_video = videos[0]
                print(f"\nFirst video details:")
                print(f"  - ID: {first_video.get('id', 'N/A')}")
                print(f"  - Author: {first_video.get('author', 'N/A')}")
                print(f"  - Description: {first_video.get('desc', 'N/A')[:100]}...")
                print(f"  - URL: {first_video.get('url', 'N/A')}")
                
                # Now test saving this data to the database
                print(f"\n3️⃣ Testing data storage...")
                
                # Get the account and task
                account = ActorAccount.objects.filter(platform='tiktok', platform_username='grafisone').order_by('-created_at').first()
                task = ActorTask.objects.get(id=131)
                
                if account and task:
                    print(f"Found account: {account.platform_username}")
                    print(f"Found task: {task.task_name}")
                    
                    # Save scraped data
                    saved_count = 0
                    for video in videos:
                        scraped_data = ActorScrapedData.objects.create(
                            task=task,
                            data_type='VIDEO',
                            content=video,
                            platform='tiktok',
                            actor_account=account,
                            account_username=account.platform_username,
                            platform_content_id=video.get('id', ''),
                            is_complete=True,
                            quality_score=1.0
                        )
                        saved_count += 1
                    
                    print(f"✅ Saved {saved_count} videos to database")
                    
                    # Update task status
                    task.status = 'COMPLETED'
                    task.items_scraped = saved_count
                    task.total_items_found = len(videos)
                    task.progress_percentage = 100.0
                    task.save()
                    
                    print(f"✅ Updated task status to COMPLETED")
                    print(f"Task items scraped: {task.items_scraped}")
                    print(f"Task progress: {task.progress_percentage}%")
                    
                    # Show final task status
                    task.refresh_from_db()
                    print(f"\n📊 Final Task Status:")
                    print(f"  - Task ID: {task.id}")
                    print(f"  - Task Name: {task.task_name}")
                    print(f"  - Status: {task.status}")
                    print(f"  - Items Scraped: {task.items_scraped}")
                    print(f"  - Progress: {task.progress_percentage}%")
                    
                else:
                    print("❌ Could not find account or task for data storage")
            else:
                print("❌ No videos found in search results")
                
        else:
            print(f"❌ Login failed - still on login page: {current_url}")
            
    except Exception as e:
        print(f"❌ Error during direct scraping test: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        if driver:
            try:
                driver.quit()
                print("\n🔧 WebDriver closed")
            except:
                pass

if __name__ == "__main__":
    test_direct_tiktok_scraping()